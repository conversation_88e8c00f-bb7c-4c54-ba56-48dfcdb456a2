{"version": 3, "sources": ["../../../../@tanstack/react-start-client/src/Meta.tsx", "../../../../@tanstack/react-start-client/src/Scripts.tsx", "../../../../@tanstack/react-start-client/src/StartClient.tsx", "../../../../@tanstack/react-start-client/src/renderRSC.tsx", "../../../../@tanstack/react-start-client/src/useServerFn.ts"], "sourcesContent": ["import { HeadContent } from '@tanstack/react-router'\n\nexport const Meta = () => {\n  if (process.env.NODE_ENV === 'development') {\n    console.warn(\n      'The Meta component is deprecated. Use `HeadContent` from `@tanstack/react-router` instead.',\n    )\n  }\n  return <HeadContent />\n}\n", "import { Scripts as RouterScripts } from '@tanstack/react-router'\n\nexport const Scripts = () => {\n  if (process.env.NODE_ENV === 'development') {\n    console.warn('The Scripts component was moved to `@tanstack/react-router`')\n  }\n  return <RouterScripts />\n}\n", "import { Await, RouterProvider } from '@tanstack/react-router'\nimport { hydrate } from '@tanstack/start-client-core'\nimport type { AnyRouter } from '@tanstack/router-core'\n\nlet hydrationPromise: Promise<void | Array<Array<void>>> | undefined\n\nexport function StartClient(props: { router: AnyRouter }) {\n  if (!hydrationPromise) {\n    if (!props.router.state.matches.length) {\n      hydrationPromise = hydrate(props.router)\n    } else {\n      hydrationPromise = Promise.resolve()\n    }\n  }\n  return (\n    <Await\n      promise={hydrationPromise}\n      children={() => <RouterProvider router={props.router} />}\n    />\n  )\n}\n", "// TODO: RSCs\n// // @ts-expect-error\n// import * as reactDom from '@vinxi/react-server-dom/client'\nimport { isValidElement } from 'react'\nimport invariant from 'tiny-invariant'\nimport type React from 'react'\n\nexport function renderRsc(input: any): React.JSX.Element {\n  if (isValidElement(input)) {\n    return input\n  }\n\n  if (typeof input === 'object' && !input.state) {\n    input.state = {\n      status: 'pending',\n      promise: Promise.resolve()\n        .then(() => {\n          let element\n\n          // We're in node\n          // TODO: RSCs\n          // if (reactDom.createFromNodeStream) {\n          //   const stream = await import('node:stream')\n\n          //   let body: any = input\n\n          //   // Unwrap the response\n          //   if (input instanceof Response) {\n          //     body = input.body\n          //   }\n\n          //   // Convert ReadableStream to NodeJS stream.Readable\n          //   if (body instanceof ReadableStream) {\n          //     body = stream.Readable.fromWeb(body as any)\n          //   }\n\n          //   if (stream.Readable.isReadable(body)) {\n          //     // body = copyStreamToRaw(body)\n          //   } else if (input.text) {\n          //     // create a readable stream by awaiting the text method\n          //     body = new stream.Readable({\n          //       async read() {\n          //         input.text().then((value: any) => {\n          //           this.push(value)\n          //           this.push(null)\n          //         })\n          //       },\n          //     })\n          //   } else {\n          //     console.error('input', input)\n          //     throw new Error('Unexpected rsc input type 👆')\n          //   }\n\n          //   element = await reactDom.createFromNodeStream(body)\n          // } else {\n          //   // We're in the browser\n          //   if (input.body instanceof ReadableStream) {\n          //     input = input.body\n          //   }\n\n          //   if (input instanceof ReadableStream) {\n          //     element = await reactDom.createFromReadableStream(input)\n          //   }\n\n          //   if (input instanceof Response) {\n          //     // copy to the response body to cache the raw data\n          //     element = await reactDom.createFromFetch(input)\n          //   }\n          // }\n\n          // return element\n\n          invariant(false, 'renderRSC() is coming soon!')\n        })\n        .then((element) => {\n          input.state.value = element\n          input.state.status = 'success'\n        })\n        .catch((err) => {\n          input.state.status = 'error'\n          input.state.error = err\n        }),\n    }\n  }\n\n  if (input.state.status === 'pending') {\n    throw input.state.promise\n  }\n\n  return input.state.value\n}\n", "import { isRedirect } from '@tanstack/router-core'\nimport { useRouter } from '@tanstack/react-router'\n\nexport function useServerFn<T extends (...deps: Array<any>) => Promise<any>>(\n  serverFn: T,\n): (...args: Parameters<T>) => ReturnType<T> {\n  const router = useRouter()\n\n  return (async (...args: Array<any>) => {\n    try {\n      const res = await serverFn(...args)\n\n      if (isRedirect(res)) {\n        throw res\n      }\n\n      return res\n    } catch (err) {\n      if (isRedirect(err)) {\n        const resolvedRedirect = router.resolveRedirect({\n          ...err,\n          _fromLocation: router.state.location,\n        })\n\n        return router.navigate(resolvedRedirect)\n      }\n\n      throw err\n    }\n  }) as any\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,IAAM,OAAO,MAAM;AACpB,MAAA,MAAwC;AAClC,YAAA;MACN;IACF;EAAA;AAEF,aAAA,wBAAQ,aAAY,CAAA,CAAA;AACtB;A;;;ACPO,IAAMA,WAAU,MAAM;AACvB,MAAA,MAAwC;AAC1C,YAAQ,KAAK,6DAA6D;EAAA;AAE5E,aAAA,yBAAQC,SAAc,CAAA,CAAA;AACxB;A;;;ACHA,IAAI;AAEG,SAAS,YAAY,OAA8B;AACxD,MAAI,CAAC,kBAAkB;AACrB,QAAI,CAAC,MAAM,OAAO,MAAM,QAAQ,QAAQ;AACnB,yBAAA,QAAQ,MAAM,MAAM;IAAA,OAClC;AACL,yBAAmB,QAAQ,QAAQ;IAAA;EACrC;AAGA,aAAA;IAAC;IAAA;MACC,SAAS;MACT,UAAU,UAAM,yBAAC,gBAAe,EAAA,QAAQ,MAAM,OAAQ,CAAA;IAAA;EACxD;AAEJ;A;;;ACbO,SAAS,UAAU,OAA+B;AACnD,UAAA,6BAAe,KAAK,GAAG;AAClB,WAAA;EAAA;AAGT,MAAI,OAAO,UAAU,YAAY,CAAC,MAAM,OAAO;AAC7C,UAAM,QAAQ;MACZ,QAAQ;MACR,SAAS,QAAQ,QAAQ,EACtB,KAAK,MAAM;AAwDV,kBAAU,OAAO,6BAA6B;MAAA,CAC/C,EACA,KAAK,CAAC,YAAY;AACjB,cAAM,MAAM,QAAQ;AACpB,cAAM,MAAM,SAAS;MAAA,CACtB,EACA,MAAM,CAAC,QAAQ;AACd,cAAM,MAAM,SAAS;AACrB,cAAM,MAAM,QAAQ;MACrB,CAAA;IACL;EAAA;AAGE,MAAA,MAAM,MAAM,WAAW,WAAW;AACpC,UAAM,MAAM,MAAM;EAAA;AAGpB,SAAO,MAAM,MAAM;AACrB;;;ACvFO,SAAS,YACd,UAC2C;AAC3C,QAAM,SAAS,UAAU;AAEzB,SAAQ,UAAU,SAAqB;AACjC,QAAA;AACF,YAAM,MAAM,MAAM,SAAS,GAAG,IAAI;AAE9B,UAAA,WAAW,GAAG,GAAG;AACb,cAAA;MAAA;AAGD,aAAA;IAAA,SACA,KAAK;AACR,UAAA,WAAW,GAAG,GAAG;AACb,cAAA,mBAAmB,OAAO,gBAAgB;UAC9C,GAAG;UACH,eAAe,OAAO,MAAM;QAAA,CAC7B;AAEM,eAAA,OAAO,SAAS,gBAAgB;MAAA;AAGnC,YAAA;IAAA;EAEV;AACF;", "names": ["<PERSON><PERSON><PERSON>", "RouterScripts"]}