import { motion, AnimatePresence } from "framer-motion"
import { useState } from "react"
import { Plus, Calendar, Clock, MapPin, Users } from "lucide-react"
import { Button } from "./ui/button"
import { Badge } from "./ui/badge"
import { Card, CardContent } from "./ui/card"

interface TimelineEvent {
  id: string
  title: string
  date: string
  description: string
  type: "milestone" | "meeting" | "deadline" | "event"
  color: string
}

const initialEvents: TimelineEvent[] = [
  {
    id: "1",
    title: "Project Kickoff",
    date: "2024-01-15",
    description: "Initial team meeting and project planning",
    type: "milestone",
    color: "bg-blue-500"
  },
  {
    id: "2", 
    title: "Design Review",
    date: "2024-02-01",
    description: "Review wireframes and design mockups",
    type: "meeting",
    color: "bg-purple-500"
  },
  {
    id: "3",
    title: "MVP Launch",
    date: "2024-03-15",
    description: "Release minimum viable product",
    type: "deadline",
    color: "bg-green-500"
  }
]

const eventIcons = {
  milestone: Clock,
  meeting: Users,
  deadline: Calendar,
  event: MapPin
}

export function InteractiveTimelineDemo() {
  const [events, setEvents] = useState<TimelineEvent[]>(initialEvents)
  const [selectedEvent, setSelectedEvent] = useState<string | null>(null)

  const addEvent = () => {
    const newEvent: TimelineEvent = {
      id: Date.now().toString(),
      title: "New Event",
      date: "2024-04-01",
      description: "Click to edit this event",
      type: "event",
      color: "bg-orange-500"
    }
    setEvents([...events, newEvent])
  }

  const removeEvent = (id: string) => {
    setEvents(events.filter(e => e.id !== id))
  }

  return (
    <Card className="w-full max-w-4xl mx-auto overflow-hidden">
      <CardContent className="p-8">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-2xl font-bold">Interactive Timeline</h3>
          <Button onClick={addEvent} size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Add Event
          </Button>
        </div>

        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-border"></div>

          <AnimatePresence>
            {events.map((event, index) => {
              const Icon = eventIcons[event.type]
              return (
                <motion.div
                  key={event.id}
                  initial={{ opacity: 0, x: -50 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 50 }}
                  transition={{ delay: index * 0.1 }}
                  className="relative flex items-start mb-8 group"
                >
                  {/* Timeline dot */}
                  <motion.div
                    className={`relative z-10 flex items-center justify-center w-8 h-8 rounded-full ${event.color} text-white shadow-lg`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Icon className="w-4 h-4" />
                  </motion.div>

                  {/* Event content */}
                  <motion.div
                    className="ml-6 flex-1 cursor-pointer"
                    onClick={() => setSelectedEvent(selectedEvent === event.id ? null : event.id)}
                    whileHover={{ scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <div className="bg-card border rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-semibold text-foreground">{event.title}</h4>
                          <p className="text-sm text-muted-foreground">{event.date}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="capitalize">
                            {event.type}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              removeEvent(event.id)
                            }}
                            className="opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            ×
                          </Button>
                        </div>
                      </div>

                      <AnimatePresence>
                        {selectedEvent === event.id && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.2 }}
                            className="mt-3 pt-3 border-t"
                          >
                            <p className="text-sm text-muted-foreground">
                              {event.description}
                            </p>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </motion.div>
                </motion.div>
              )
            })}
          </AnimatePresence>

          {events.length === 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-12"
            >
              <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No events yet. Add your first event!</p>
            </motion.div>
          )}
        </div>

        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <p className="text-sm text-muted-foreground text-center">
            ✨ Click events to expand • Hover for interactions • Add/remove events to see animations
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
