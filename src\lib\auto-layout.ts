import type { TimelineElement } from '~/stores/timeline-store'

export interface LayoutOptions {
  algorithm: 'chronological' | 'hierarchical' | 'force-directed' | 'grid'
  spacing: {
    horizontal: number
    vertical: number
  }
  alignment: 'top' | 'center' | 'bottom'
  groupBy?: 'category' | 'type' | 'none'
  avoidOverlaps: boolean
  respectConstraints: boolean
}

export interface LayoutResult {
  elements: Array<{
    id: string
    position: { x: number; y: number }
    size: { width: number; height: number }
  }>
  bounds: {
    left: number
    right: number
    top: number
    bottom: number
  }
}

// Chronological layout - arrange elements by date
export function chronologicalLayout(
  elements: TimelineElement[],
  options: LayoutOptions
): LayoutResult {
  const sortedElements = [...elements].sort((a, b) => a.date.getTime() - b.date.getTime())
  
  const lanes: Array<Array<{ element: TimelineElement; x: number; width: number }>> = []
  const laneHeight = 100
  const elementHeight = 60
  
  // Group by category if specified
  const groups = options.groupBy === 'category' 
    ? groupElementsByCategory(sortedElements)
    : { default: sortedElements }

  let currentY = 50
  const result: LayoutResult['elements'] = []

  Object.entries(groups).forEach(([category, groupElements]) => {
    // Find available lanes for each element
    groupElements.forEach(element => {
      const elementWidth = element.width || 120
      const elementX = calculateXPosition(element.date, sortedElements)
      
      // Find the first available lane
      let laneIndex = 0
      let placed = false
      
      while (!placed) {
        if (!lanes[laneIndex]) {
          lanes[laneIndex] = []
        }
        
        // Check if element fits in this lane
        const canPlace = lanes[laneIndex].every(existing => 
          elementX >= existing.x + existing.width + options.spacing.horizontal ||
          elementX + elementWidth + options.spacing.horizontal <= existing.x
        )
        
        if (canPlace) {
          lanes[laneIndex].push({
            element,
            x: elementX,
            width: elementWidth
          })
          
          const y = currentY + laneIndex * (laneHeight + options.spacing.vertical)
          
          result.push({
            id: element.id,
            position: { x: elementX, y },
            size: { width: elementWidth, height: elementHeight }
          })
          
          placed = true
        } else {
          laneIndex++
        }
      }
    })
    
    // Move to next group
    currentY += (lanes.length + 1) * (laneHeight + options.spacing.vertical)
    lanes.length = 0 // Clear lanes for next group
  })

  // Calculate bounds
  const bounds = calculateBounds(result)
  
  return { elements: result, bounds }
}

// Hierarchical layout - arrange elements in a tree-like structure
export function hierarchicalLayout(
  elements: TimelineElement[],
  options: LayoutOptions
): LayoutResult {
  // Build hierarchy based on dependencies
  const hierarchy = buildHierarchy(elements)
  const result: LayoutResult['elements'] = []
  
  const levelWidth = 200 + options.spacing.horizontal
  const nodeHeight = 80 + options.spacing.vertical
  
  // Position elements level by level
  hierarchy.forEach((level, levelIndex) => {
    const x = levelIndex * levelWidth
    
    level.forEach((element, nodeIndex) => {
      const y = nodeIndex * nodeHeight
      
      result.push({
        id: element.id,
        position: { x, y },
        size: { 
          width: element.width || 120, 
          height: element.height || 60 
        }
      })
    })
  })
  
  const bounds = calculateBounds(result)
  return { elements: result, bounds }
}

// Force-directed layout - use physics simulation for natural positioning
export function forceDirectedLayout(
  elements: TimelineElement[],
  options: LayoutOptions
): LayoutResult {
  const nodes = elements.map(element => ({
    id: element.id,
    element,
    x: element.position.x,
    y: element.position.y,
    vx: 0,
    vy: 0,
    width: element.width || 120,
    height: element.height || 60
  }))
  
  const iterations = 100
  const repulsionStrength = 1000
  const attractionStrength = 0.1
  const damping = 0.9
  
  // Run simulation
  for (let i = 0; i < iterations; i++) {
    // Apply repulsion forces
    for (let j = 0; j < nodes.length; j++) {
      for (let k = j + 1; k < nodes.length; k++) {
        const nodeA = nodes[j]
        const nodeB = nodes[k]
        
        const dx = nodeB.x - nodeA.x
        const dy = nodeB.y - nodeA.y
        const distance = Math.sqrt(dx * dx + dy * dy) || 1
        
        const force = repulsionStrength / (distance * distance)
        const fx = (dx / distance) * force
        const fy = (dy / distance) * force
        
        nodeA.vx -= fx
        nodeA.vy -= fy
        nodeB.vx += fx
        nodeB.vy += fy
      }
    }
    
    // Apply chronological attraction (elements should be ordered by date)
    const sortedNodes = [...nodes].sort((a, b) => 
      a.element.date.getTime() - b.element.date.getTime()
    )
    
    for (let j = 0; j < sortedNodes.length - 1; j++) {
      const nodeA = sortedNodes[j]
      const nodeB = sortedNodes[j + 1]
      
      const idealDistance = options.spacing.horizontal
      const dx = nodeB.x - nodeA.x
      const dy = nodeB.y - nodeA.y
      const distance = Math.sqrt(dx * dx + dy * dy) || 1
      
      const force = attractionStrength * (distance - idealDistance)
      const fx = (dx / distance) * force
      const fy = (dy / distance) * force
      
      nodeA.vx += fx * 0.5
      nodeA.vy += fy * 0.5
      nodeB.vx -= fx * 0.5
      nodeB.vy -= fy * 0.5
    }
    
    // Update positions
    nodes.forEach(node => {
      node.vx *= damping
      node.vy *= damping
      node.x += node.vx
      node.y += node.vy
    })
  }
  
  const result = nodes.map(node => ({
    id: node.id,
    position: { x: node.x, y: node.y },
    size: { width: node.width, height: node.height }
  }))
  
  const bounds = calculateBounds(result)
  return { elements: result, bounds }
}

// Grid layout - arrange elements in a regular grid
export function gridLayout(
  elements: TimelineElement[],
  options: LayoutOptions
): LayoutResult {
  const columns = Math.ceil(Math.sqrt(elements.length))
  const cellWidth = 150 + options.spacing.horizontal
  const cellHeight = 100 + options.spacing.vertical
  
  const result = elements.map((element, index) => {
    const row = Math.floor(index / columns)
    const col = index % columns
    
    return {
      id: element.id,
      position: {
        x: col * cellWidth,
        y: row * cellHeight
      },
      size: {
        width: element.width || 120,
        height: element.height || 60
      }
    }
  })
  
  const bounds = calculateBounds(result)
  return { elements: result, bounds }
}

// Collision detection and resolution
export function detectCollisions(elements: LayoutResult['elements']): Array<{
  id1: string
  id2: string
  severity: 'warning' | 'error'
  overlap: { x: number; y: number; width: number; height: number }
}> {
  const collisions: Array<{
    id1: string
    id2: string
    severity: 'warning' | 'error'
    overlap: { x: number; y: number; width: number; height: number }
  }> = []
  
  for (let i = 0; i < elements.length; i++) {
    for (let j = i + 1; j < elements.length; j++) {
      const elementA = elements[i]
      const elementB = elements[j]
      
      const overlapX = Math.max(0, Math.min(
        elementA.position.x + elementA.size.width,
        elementB.position.x + elementB.size.width
      ) - Math.max(elementA.position.x, elementB.position.x))
      
      const overlapY = Math.max(0, Math.min(
        elementA.position.y + elementA.size.height,
        elementB.position.y + elementB.size.height
      ) - Math.max(elementA.position.y, elementB.position.y))
      
      if (overlapX > 0 && overlapY > 0) {
        const overlapArea = overlapX * overlapY
        const elementAArea = elementA.size.width * elementA.size.height
        const elementBArea = elementB.size.width * elementB.size.height
        const overlapPercentage = overlapArea / Math.min(elementAArea, elementBArea)
        
        collisions.push({
          id1: elementA.id,
          id2: elementB.id,
          severity: overlapPercentage > 0.5 ? 'error' : 'warning',
          overlap: {
            x: Math.max(elementA.position.x, elementB.position.x),
            y: Math.max(elementA.position.y, elementB.position.y),
            width: overlapX,
            height: overlapY
          }
        })
      }
    }
  }
  
  return collisions
}

// Helper functions
function groupElementsByCategory(elements: TimelineElement[]) {
  return elements.reduce((groups, element) => {
    const category = element.category || 'default'
    if (!groups[category]) {
      groups[category] = []
    }
    groups[category].push(element)
    return groups
  }, {} as Record<string, TimelineElement[]>)
}

function calculateXPosition(date: Date, allElements: TimelineElement[]): number {
  const dates = allElements.map(el => el.date.getTime()).sort((a, b) => a - b)
  const minDate = dates[0]
  const maxDate = dates[dates.length - 1]
  const dateRange = maxDate - minDate || 1
  
  const normalizedPosition = (date.getTime() - minDate) / dateRange
  return normalizedPosition * 1000 // Scale to reasonable pixel range
}

function buildHierarchy(elements: TimelineElement[]): TimelineElement[][] {
  const levels: TimelineElement[][] = []
  const processed = new Set<string>()
  
  // Start with elements that have no dependencies
  const rootElements = elements.filter(el => 
    !el.fromElementId || !elements.find(e => e.id === el.fromElementId)
  )
  
  if (rootElements.length > 0) {
    levels.push(rootElements)
    rootElements.forEach(el => processed.add(el.id))
  }
  
  // Build subsequent levels
  while (processed.size < elements.length) {
    const nextLevel = elements.filter(el => 
      !processed.has(el.id) && 
      (!el.fromElementId || processed.has(el.fromElementId))
    )
    
    if (nextLevel.length === 0) break // Prevent infinite loop
    
    levels.push(nextLevel)
    nextLevel.forEach(el => processed.add(el.id))
  }
  
  return levels
}

function calculateBounds(elements: LayoutResult['elements']) {
  if (elements.length === 0) {
    return { left: 0, right: 0, top: 0, bottom: 0 }
  }
  
  return elements.reduce((bounds, element) => ({
    left: Math.min(bounds.left, element.position.x),
    right: Math.max(bounds.right, element.position.x + element.size.width),
    top: Math.min(bounds.top, element.position.y),
    bottom: Math.max(bounds.bottom, element.position.y + element.size.height)
  }), {
    left: Infinity,
    right: -Infinity,
    top: Infinity,
    bottom: -Infinity
  })
}
