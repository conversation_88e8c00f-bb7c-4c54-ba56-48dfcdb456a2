import { motion } from 'framer-motion'
import { ZoomIn, ZoomOut, RotateCcw } from 'lucide-react'
import { Button } from '~/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '~/components/ui/tooltip'
import { useTimelineStore } from '~/stores/timeline-store'

interface ZoomControlsProps {
  zoom: number
  onZoomChange: (zoom: number) => void
  onResetView: () => void
}

export function ZoomControls({ zoom, onZoomChange, onResetView }: ZoomControlsProps) {
  const { minZoom, maxZoom } = useTimelineStore()

  const handleZoomIn = () => {
    const newZoom = Math.min(maxZoom, zoom * 1.2)
    onZoomChange(newZoom)
  }

  const handleZoomOut = () => {
    const newZoom = Math.max(minZoom, zoom * 0.8)
    onZoomChange(newZoom)
  }

  const zoomPercentage = Math.round(zoom * 100)

  return (
    <TooltipProvider>
      <motion.div
        initial={{ x: 100, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.4, ease: [0.23, 1, 0.32, 1] }}
        className="absolute bottom-6 right-6 z-50"
      >
        <div className="flex flex-col gap-2 bg-background/98 backdrop-blur-md border border-border/50 rounded-2xl shadow-2xl shadow-black/10 p-3">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-9 w-9 p-0 rounded-lg transition-all duration-200 hover:scale-105"
                onClick={handleZoomIn}
                disabled={zoom >= maxZoom}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left" className="bg-background/95 backdrop-blur-sm">
              <p className="text-xs font-medium">Zoom In (Ctrl/Cmd + =)</p>
            </TooltipContent>
          </Tooltip>

          <div className="px-2 py-1.5 text-xs text-center text-foreground font-medium min-w-[3rem] bg-muted/30 rounded-lg">
            {zoomPercentage}%
          </div>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-9 w-9 p-0 rounded-lg transition-all duration-200 hover:scale-105"
                onClick={handleZoomOut}
                disabled={zoom <= minZoom}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left" className="bg-background/95 backdrop-blur-sm">
              <p className="text-xs font-medium">Zoom Out (Ctrl/Cmd + -)</p>
            </TooltipContent>
          </Tooltip>

          <div className="w-full h-px bg-border/30 my-1" />

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-9 w-9 p-0 rounded-lg transition-all duration-200 hover:scale-105"
                onClick={onResetView}
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left" className="bg-background/95 backdrop-blur-sm">
              <p className="text-xs font-medium">Reset View (Ctrl/Cmd + 0)</p>
            </TooltipContent>
          </Tooltip>
        </div>
      </motion.div>
    </TooltipProvider>
  )
}
