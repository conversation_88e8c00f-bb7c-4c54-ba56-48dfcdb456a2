import { motion } from 'framer-motion'
import {
  MousePointer2,
  Hand,
  Calendar,
  Flag,
  Layers,
  ArrowRight,
  MessageSquare,
  Type,
  Settings,
  Users,
  Download,
  Share,
  AlignHorizontalJustifyCenter
} from 'lucide-react'
import { Button } from '~/components/ui/button'
import { Separator } from '~/components/ui/separator'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '~/components/ui/tooltip'
import { useTimelineStore, TimelineTool } from '~/stores/timeline-store'
import { cn } from '~/lib/utils'

const tools = [
  { id: 'select', icon: MousePointer2, label: 'Select (V)', shortcut: 'V' },
  { id: 'pan', icon: Hand, label: 'Pan (H)', shortcut: 'H' },
  { id: 'event', icon: Calendar, label: 'Add Event (E)', shortcut: 'E' },
  { id: 'milestone', icon: Flag, label: 'Add Milestone (M)', shortcut: 'M' },
  { id: 'phase', icon: Layers, label: 'Add Phase (P)', shortcut: 'P' },
  { id: 'dependency', icon: ArrowRight, label: 'Add Dependency (D)', shortcut: 'D' },
  { id: 'comment', icon: MessageSquare, label: 'Add Comment (C)', shortcut: 'C' },
  { id: 'text', icon: Type, label: 'Add Text (T)', shortcut: 'T' },
] as const

export function TimelineToolbar() {
  const { selectedTool, setSelectedTool, setPropertyPanelOpen, isPropertyPanelOpen, setCanvasMode, autoPositionElements } = useTimelineStore()

  const handleToolSelect = (tool: TimelineTool) => {
    setSelectedTool(tool)
    // Set canvas mode based on tool
    if (['event', 'milestone', 'phase', 'dependency'].includes(tool)) {
      setCanvasMode('creating')
    } else {
      setCanvasMode('idle')
    }
  }

  return (
    <TooltipProvider>
      <motion.div
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.4, ease: [0.23, 1, 0.32, 1] }}
        className="absolute top-6 left-1/2 -translate-x-1/2 z-50"
      >
        <div className="flex items-center gap-2 bg-background/98 backdrop-blur-md border border-border/50 rounded-2xl shadow-2xl shadow-black/10 p-3">
          {/* Selection Tools */}
          <div className="flex items-center gap-1 bg-muted/30 rounded-xl p-1">
            {tools.slice(0, 2).map((tool) => (
              <Tooltip key={tool.id}>
                <TooltipTrigger asChild>
                  <Button
                    variant={selectedTool === tool.id ? "default" : "ghost"}
                    size="sm"
                    className={cn(
                      "h-9 w-9 p-0 rounded-lg transition-all duration-200",
                      selectedTool === tool.id
                        ? "bg-primary text-primary-foreground shadow-md scale-105"
                        : "hover:bg-background/80 hover:scale-105"
                    )}
                    onClick={() => handleToolSelect(tool.id as TimelineTool)}
                  >
                    <tool.icon className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="bg-background/95 backdrop-blur-sm">
                  <p className="text-xs font-medium">{tool.label}</p>
                </TooltipContent>
              </Tooltip>
            ))}
          </div>

          {/* Separator */}
          <div className="w-px h-6 bg-border/30" />

          {/* Creation Tools */}
          <div className="flex items-center gap-1 bg-muted/30 rounded-xl p-1">
            {tools.slice(2, 6).map((tool) => (
              <Tooltip key={tool.id}>
                <TooltipTrigger asChild>
                  <Button
                    variant={selectedTool === tool.id ? "default" : "ghost"}
                    size="sm"
                    className={cn(
                      "h-9 w-9 p-0 rounded-lg transition-all duration-200",
                      selectedTool === tool.id
                        ? "bg-primary text-primary-foreground shadow-md scale-105"
                        : "hover:bg-background/80 hover:scale-105"
                    )}
                    onClick={() => handleToolSelect(tool.id as TimelineTool)}
                  >
                    <tool.icon className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="bg-background/95 backdrop-blur-sm">
                  <p className="text-xs font-medium">{tool.label}</p>
                </TooltipContent>
              </Tooltip>
            ))}
          </div>

          {/* Separator */}
          <div className="w-px h-6 bg-border/30" />

          {/* Annotation Tools */}
          <div className="flex items-center gap-1 bg-muted/30 rounded-xl p-1">
            {tools.slice(6).map((tool) => (
              <Tooltip key={tool.id}>
                <TooltipTrigger asChild>
                  <Button
                    variant={selectedTool === tool.id ? "default" : "ghost"}
                    size="sm"
                    className={cn(
                      "h-9 w-9 p-0 rounded-lg transition-all duration-200",
                      selectedTool === tool.id
                        ? "bg-primary text-primary-foreground shadow-md scale-105"
                        : "hover:bg-background/80 hover:scale-105"
                    )}
                    onClick={() => handleToolSelect(tool.id as TimelineTool)}
                  >
                    <tool.icon className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="bg-background/95 backdrop-blur-sm">
                  <p className="text-xs font-medium">{tool.label}</p>
                </TooltipContent>
              </Tooltip>
            ))}
          </div>

          {/* Separator */}
          <div className="w-px h-6 bg-border/30" />

          {/* Secondary Actions */}
          <div className="flex items-center gap-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-9 w-9 p-0 rounded-lg transition-all duration-200 hover:scale-105"
                  onClick={autoPositionElements}
                >
                  <AlignHorizontalJustifyCenter className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="bg-background/95 backdrop-blur-sm">
                <p className="text-xs font-medium">Auto-Position Elements</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "h-9 w-9 p-0 rounded-lg transition-all duration-200 hover:scale-105",
                    isPropertyPanelOpen && "bg-muted text-foreground"
                  )}
                  onClick={() => setPropertyPanelOpen(!isPropertyPanelOpen)}
                >
                  <Settings className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="bg-background/95 backdrop-blur-sm">
                <p className="text-xs font-medium">Properties Panel</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-9 w-9 p-0 rounded-lg transition-all duration-200 hover:scale-105"
                >
                  <Users className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="bg-background/95 backdrop-blur-sm">
                <p className="text-xs font-medium">Collaborators</p>
              </TooltipContent>
            </Tooltip>
          </div>

          {/* Separator */}
          <div className="w-px h-6 bg-border/30" />

          {/* Export Actions */}
          <div className="flex items-center gap-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-9 w-9 p-0 rounded-lg transition-all duration-200 hover:scale-105"
                >
                  <Share className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="bg-background/95 backdrop-blur-sm">
                <p className="text-xs font-medium">Share Timeline</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-9 w-9 p-0 rounded-lg transition-all duration-200 hover:scale-105"
                >
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="bg-background/95 backdrop-blur-sm">
                <p className="text-xs font-medium">Export Timeline</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
      </motion.div>
    </TooltipProvider>
  )
}
