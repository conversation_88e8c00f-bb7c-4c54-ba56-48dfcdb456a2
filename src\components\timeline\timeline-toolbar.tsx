import { motion } from 'framer-motion'
import { 
  MousePointer2, 
  Hand, 
  Calendar, 
  Flag, 
  Layers, 
  ArrowRight, 
  MessageSquare,
  Type,
  Settings,
  Users,
  Download,
  Share
} from 'lucide-react'
import { Button } from '~/components/ui/button'
import { Separator } from '~/components/ui/separator'
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '~/components/ui/tooltip'
import { useTimelineStore, TimelineTool } from '~/stores/timeline-store'
import { cn } from '~/lib/utils'

const tools = [
  { id: 'select', icon: MousePointer2, label: 'Select (V)', shortcut: 'V' },
  { id: 'pan', icon: Hand, label: 'Pan (H)', shortcut: 'H' },
  { id: 'event', icon: Calendar, label: 'Add Event (E)', shortcut: 'E' },
  { id: 'milestone', icon: Flag, label: 'Add Milestone (M)', shortcut: 'M' },
  { id: 'phase', icon: Layers, label: 'Add Phase (P)', shortcut: 'P' },
  { id: 'dependency', icon: ArrowRight, label: 'Add Dependency (D)', shortcut: 'D' },
  { id: 'comment', icon: MessageSquare, label: 'Add Comment (C)', shortcut: 'C' },
  { id: 'text', icon: Type, label: 'Add Text (T)', shortcut: 'T' },
] as const

export function TimelineToolbar() {
  const { selectedTool, setSelectedTool, setPropertyPanelOpen } = useTimelineStore()

  const handleToolSelect = (tool: TimelineTool) => {
    setSelectedTool(tool)
  }

  return (
    <TooltipProvider>
      <motion.div
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
        className="absolute top-4 left-1/2 -translate-x-1/2 z-50"
      >
        <div className="flex items-center gap-1 bg-background/95 backdrop-blur-sm border rounded-lg shadow-lg p-2">
          {/* Main Tools */}
          <div className="flex items-center gap-1">
            {tools.map((tool) => (
              <Tooltip key={tool.id}>
                <TooltipTrigger asChild>
                  <Button
                    variant={selectedTool === tool.id ? "default" : "ghost"}
                    size="sm"
                    className={cn(
                      "h-8 w-8 p-0",
                      selectedTool === tool.id && "bg-primary text-primary-foreground"
                    )}
                    onClick={() => handleToolSelect(tool.id as TimelineTool)}
                  >
                    <tool.icon className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{tool.label}</p>
                </TooltipContent>
              </Tooltip>
            ))}
          </div>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Secondary Actions */}
          <div className="flex items-center gap-1">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                  onClick={() => setPropertyPanelOpen(true)}
                >
                  <Settings className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Properties Panel</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                >
                  <Users className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Collaborators</p>
              </TooltipContent>
            </Tooltip>

            <Separator orientation="vertical" className="h-6 mx-1" />

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                >
                  <Share className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Share Timeline</p>
              </TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-8 w-8 p-0"
                >
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Export Timeline</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>
      </motion.div>
    </TooltipProvider>
  )
}
