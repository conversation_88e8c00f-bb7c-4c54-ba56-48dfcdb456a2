import { useMemo } from 'react'

interface CanvasGridProps {
  zoom: number
}

export function CanvasGrid({ zoom }: CanvasGridProps) {
  const gridSize = useMemo(() => {
    // Adjust grid size based on zoom level
    const baseSize = 20
    if (zoom < 0.5) return baseSize * 4
    if (zoom < 1) return baseSize * 2
    if (zoom > 2) return baseSize / 2
    return baseSize
  }, [zoom])

  const subGridSize = useMemo(() => {
    // Smaller grid for better visual hierarchy
    return gridSize / 4
  }, [gridSize])

  const opacity = useMemo(() => {
    // Fade grid at extreme zoom levels
    if (zoom < 0.3) return 0.1
    if (zoom < 0.5) return 0.15
    if (zoom > 3) return 0.08
    return 0.25
  }, [zoom])

  const subOpacity = useMemo(() => {
    // Sub-grid opacity
    if (zoom < 0.5) return 0
    if (zoom < 1) return 0.05
    if (zoom > 2) return 0.15
    return 0.1
  }, [zoom])

  return (
    <>
      {/* Subtle gradient background */}
      <div className="absolute inset-0 pointer-events-none bg-gradient-to-br from-background via-background to-muted/20" />

      {/* Sub-grid for fine detail */}
      {subOpacity > 0 && (
        <div
          className="absolute inset-0 pointer-events-none"
          style={{
            backgroundImage: `
              linear-gradient(to right, hsl(var(--border)) 0.5px, transparent 0.5px),
              linear-gradient(to bottom, hsl(var(--border)) 0.5px, transparent 0.5px)
            `,
            backgroundSize: `${subGridSize}px ${subGridSize}px`,
            opacity: subOpacity
          }}
        />
      )}

      {/* Main grid */}
      <div
        className="absolute inset-0 pointer-events-none"
        style={{
          backgroundImage: `
            linear-gradient(to right, hsl(var(--border)) 1px, transparent 1px),
            linear-gradient(to bottom, hsl(var(--border)) 1px, transparent 1px)
          `,
          backgroundSize: `${gridSize}px ${gridSize}px`,
          opacity
        }}
      />

      {/* Major grid lines every 5 units */}
      <div
        className="absolute inset-0 pointer-events-none"
        style={{
          backgroundImage: `
            linear-gradient(to right, hsl(var(--border)) 1.5px, transparent 1.5px),
            linear-gradient(to bottom, hsl(var(--border)) 1.5px, transparent 1.5px)
          `,
          backgroundSize: `${gridSize * 5}px ${gridSize * 5}px`,
          opacity: opacity * 0.6
        }}
      />

      {/* Timeline lane guides (horizontal lines every 80px) */}
      <div
        className="absolute pointer-events-none"
        style={{
          top: '120px', // Start below timeline axis
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: `linear-gradient(to bottom, hsl(var(--primary)) 1px, transparent 1px)`,
          backgroundSize: `100% 80px`,
          opacity: Math.min(0.1, zoom * 0.05)
        }}
      />
    </>
  )
}
