import { useMemo } from 'react'

interface CanvasGridProps {
  zoom: number
}

export function CanvasGrid({ zoom }: CanvasGridProps) {
  const gridSize = useMemo(() => {
    // Adjust grid size based on zoom level
    const baseSize = 20
    if (zoom < 0.5) return baseSize * 4
    if (zoom < 1) return baseSize * 2
    if (zoom > 2) return baseSize / 2
    return baseSize
  }, [zoom])

  const opacity = useMemo(() => {
    // Fade grid at extreme zoom levels
    if (zoom < 0.3) return 0.1
    if (zoom < 0.5) return 0.2
    if (zoom > 3) return 0.1
    return 0.3
  }, [zoom])

  return (
    <div 
      className="absolute inset-0 pointer-events-none"
      style={{
        backgroundImage: `
          linear-gradient(to right, hsl(var(--border)) 1px, transparent 1px),
          linear-gradient(to bottom, hsl(var(--border)) 1px, transparent 1px)
        `,
        backgroundSize: `${gridSize}px ${gridSize}px`,
        opacity
      }}
    />
  )
}
