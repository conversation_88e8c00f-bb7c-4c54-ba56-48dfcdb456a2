import { useState, useCallback, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Copy, 
  Trash2, 
  Edit, 
  Duplicate, 
  Lock, 
  Unlock, 
  Eye, 
  EyeOff, 
  Palette, 
  Link, 
  Unlink,
  ArrowUp,
  ArrowDown,
  Settings,
  Info
} from 'lucide-react'
import { useTimelineStore } from '~/stores/timeline-store'

interface ContextMenuProps {
  isOpen: boolean
  position: { x: number; y: number }
  targetElementId: string | null
  onClose: () => void
}

export function ContextMenu({ isOpen, position, targetElementId, onClose }: ContextMenuProps) {
  const {
    elements,
    selectedElements,
    updateElement,
    deleteElement,
    setSelectedElements,
    setPropertyPanelOpen
  } = useTimelineStore()

  const menuRef = useRef<HTMLDivElement>(null)
  const targetElement = targetElementId ? elements.find(el => el.id === targetElementId) : null
  const isMultiSelection = selectedElements.length > 1

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen, onClose])

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      return () => document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  const handleAction = useCallback((action: string) => {
    if (!targetElement && !isMultiSelection) return

    switch (action) {
      case 'edit':
        setPropertyPanelOpen(true)
        break

      case 'duplicate':
        if (targetElement) {
          const newElement = {
            ...targetElement,
            id: `${targetElement.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            title: `${targetElement.title} Copy`,
            position: {
              x: targetElement.position.x + 20,
              y: targetElement.position.y + 20
            }
          }
          // Note: Would need addElement method in store
        }
        break

      case 'delete':
        if (isMultiSelection) {
          selectedElements.forEach(id => deleteElement(id))
        } else if (targetElementId) {
          deleteElement(targetElementId)
        }
        setSelectedElements([])
        break

      case 'copy':
        if (targetElement) {
          navigator.clipboard.writeText(JSON.stringify(targetElement))
        }
        break

      case 'lock':
        if (targetElement) {
          updateElement(targetElement.id, { 
            metadata: { ...targetElement.metadata, locked: true }
          })
        }
        break

      case 'unlock':
        if (targetElement) {
          updateElement(targetElement.id, { 
            metadata: { ...targetElement.metadata, locked: false }
          })
        }
        break

      case 'hide':
        if (targetElement) {
          updateElement(targetElement.id, { 
            metadata: { ...targetElement.metadata, hidden: true }
          })
        }
        break

      case 'show':
        if (targetElement) {
          updateElement(targetElement.id, { 
            metadata: { ...targetElement.metadata, hidden: false }
          })
        }
        break

      case 'bring-forward':
        if (targetElement) {
          updateElement(targetElement.id, { 
            metadata: { ...targetElement.metadata, zIndex: (targetElement.metadata?.zIndex || 0) + 1 }
          })
        }
        break

      case 'send-backward':
        if (targetElement) {
          updateElement(targetElement.id, { 
            metadata: { ...targetElement.metadata, zIndex: (targetElement.metadata?.zIndex || 0) - 1 }
          })
        }
        break
    }

    onClose()
  }, [targetElement, isMultiSelection, selectedElements, updateElement, deleteElement, setSelectedElements, setPropertyPanelOpen, targetElementId, onClose])

  if (!isOpen) return null

  const menuItems = [
    ...(targetElement ? [
      {
        id: 'edit',
        label: 'Edit Properties',
        icon: Edit,
        shortcut: 'Enter',
        action: () => handleAction('edit')
      },
      {
        id: 'duplicate',
        label: 'Duplicate',
        icon: Duplicate,
        shortcut: 'Ctrl+D',
        action: () => handleAction('duplicate')
      },
      { type: 'separator' as const },
      {
        id: 'copy',
        label: 'Copy',
        icon: Copy,
        shortcut: 'Ctrl+C',
        action: () => handleAction('copy')
      }
    ] : []),
    
    {
      id: 'delete',
      label: isMultiSelection ? `Delete ${selectedElements.length} items` : 'Delete',
      icon: Trash2,
      shortcut: 'Del',
      action: () => handleAction('delete'),
      destructive: true
    },

    ...(targetElement ? [
      { type: 'separator' as const },
      {
        id: targetElement.metadata?.locked ? 'unlock' : 'lock',
        label: targetElement.metadata?.locked ? 'Unlock' : 'Lock',
        icon: targetElement.metadata?.locked ? Unlock : Lock,
        action: () => handleAction(targetElement.metadata?.locked ? 'unlock' : 'lock')
      },
      {
        id: targetElement.metadata?.hidden ? 'show' : 'hide',
        label: targetElement.metadata?.hidden ? 'Show' : 'Hide',
        icon: targetElement.metadata?.hidden ? Eye : EyeOff,
        action: () => handleAction(targetElement.metadata?.hidden ? 'show' : 'hide')
      },
      { type: 'separator' as const },
      {
        id: 'bring-forward',
        label: 'Bring Forward',
        icon: ArrowUp,
        action: () => handleAction('bring-forward')
      },
      {
        id: 'send-backward',
        label: 'Send Backward',
        icon: ArrowDown,
        action: () => handleAction('send-backward')
      }
    ] : [])
  ]

  // Adjust menu position to stay within viewport
  const adjustedPosition = {
    x: Math.min(position.x, window.innerWidth - 200),
    y: Math.min(position.y, window.innerHeight - menuItems.length * 40)
  }

  return (
    <AnimatePresence>
      <motion.div
        ref={menuRef}
        initial={{ opacity: 0, scale: 0.95, y: -10 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95, y: -10 }}
        transition={{ duration: 0.15, ease: [0.23, 1, 0.32, 1] }}
        className="fixed z-50 min-w-48 bg-background border border-border rounded-lg shadow-lg py-1"
        style={{
          left: adjustedPosition.x,
          top: adjustedPosition.y
        }}
      >
        {menuItems.map((item, index) => {
          if (item.type === 'separator') {
            return <div key={index} className="h-px bg-border my-1" />
          }

          const Icon = item.icon

          return (
            <motion.button
              key={item.id}
              className={`w-full px-3 py-2 text-left text-sm hover:bg-muted transition-colors flex items-center justify-between ${
                item.destructive ? 'text-destructive hover:bg-destructive/10' : ''
              }`}
              onClick={item.action}
              whileHover={{ backgroundColor: 'rgba(var(--muted), 0.8)' }}
              whileTap={{ scale: 0.98 }}
            >
              <div className="flex items-center gap-2">
                <Icon className="h-4 w-4" />
                <span>{item.label}</span>
              </div>
              {item.shortcut && (
                <span className="text-xs text-muted-foreground">{item.shortcut}</span>
              )}
            </motion.button>
          )
        })}
      </motion.div>
    </AnimatePresence>
  )
}

// Hook to manage context menu state
export function useContextMenu() {
  const [contextMenu, setContextMenu] = useState<{
    isOpen: boolean
    position: { x: number; y: number }
    targetElementId: string | null
  }>({
    isOpen: false,
    position: { x: 0, y: 0 },
    targetElementId: null
  })

  const openContextMenu = useCallback((
    event: React.MouseEvent,
    targetElementId?: string
  ) => {
    event.preventDefault()
    event.stopPropagation()

    setContextMenu({
      isOpen: true,
      position: { x: event.clientX, y: event.clientY },
      targetElementId: targetElementId || null
    })
  }, [])

  const closeContextMenu = useCallback(() => {
    setContextMenu(prev => ({ ...prev, isOpen: false }))
  }, [])

  return {
    contextMenu,
    openContextMenu,
    closeContextMenu
  }
}
