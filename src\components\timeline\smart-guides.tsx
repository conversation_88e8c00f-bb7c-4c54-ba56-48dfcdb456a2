import { motion, AnimatePresence } from 'framer-motion'
import { useTimelineStore } from '~/stores/timeline-store'

interface SmartGuidesProps {
  isVisible: boolean
  draggedElementIds: string[]
}

interface Guide {
  id: string
  type: 'vertical' | 'horizontal'
  position: number
  label?: string
}

export function SmartGuides({ isVisible, draggedElementIds }: SmartGuidesProps) {
  const { elements, zoom } = useTimelineStore()

  if (!isVisible || draggedElementIds.length === 0) return null

  // Get all non-dragged elements for guide calculation
  const staticElements = elements.filter(el => !draggedElementIds.includes(el.id))
  const draggedElements = elements.filter(el => draggedElementIds.includes(el.id))

  // Calculate alignment guides
  const guides: Guide[] = []

  // Vertical guides (for horizontal alignment)
  const verticalPositions = new Set<number>()
  staticElements.forEach(el => {
    const left = el.position.x
    const center = el.position.x + (el.width || 120) / 2
    const right = el.position.x + (el.width || 120)
    
    verticalPositions.add(left)
    verticalPositions.add(center)
    verticalPositions.add(right)
  })

  // Horizontal guides (for vertical alignment)
  const horizontalPositions = new Set<number>()
  staticElements.forEach(el => {
    const top = el.position.y
    const center = el.position.y + (el.height || 60) / 2
    const bottom = el.position.y + (el.height || 60)
    
    horizontalPositions.add(top)
    horizontalPositions.add(center)
    horizontalPositions.add(bottom)
  })

  // Check if dragged elements are close to any guide positions
  const snapThreshold = 8 / zoom // Adjust snap threshold based on zoom

  draggedElements.forEach(draggedEl => {
    const draggedLeft = draggedEl.position.x
    const draggedCenter = draggedEl.position.x + (draggedEl.width || 120) / 2
    const draggedRight = draggedEl.position.x + (draggedEl.width || 120)
    const draggedTop = draggedEl.position.y
    const draggedVCenter = draggedEl.position.y + (draggedEl.height || 60) / 2
    const draggedBottom = draggedEl.position.y + (draggedEl.height || 60)

    // Check vertical guides
    verticalPositions.forEach(pos => {
      if (Math.abs(draggedLeft - pos) < snapThreshold) {
        guides.push({ id: `v-left-${pos}`, type: 'vertical', position: pos, label: 'Left' })
      }
      if (Math.abs(draggedCenter - pos) < snapThreshold) {
        guides.push({ id: `v-center-${pos}`, type: 'vertical', position: pos, label: 'Center' })
      }
      if (Math.abs(draggedRight - pos) < snapThreshold) {
        guides.push({ id: `v-right-${pos}`, type: 'vertical', position: pos, label: 'Right' })
      }
    })

    // Check horizontal guides
    horizontalPositions.forEach(pos => {
      if (Math.abs(draggedTop - pos) < snapThreshold) {
        guides.push({ id: `h-top-${pos}`, type: 'horizontal', position: pos, label: 'Top' })
      }
      if (Math.abs(draggedVCenter - pos) < snapThreshold) {
        guides.push({ id: `h-center-${pos}`, type: 'horizontal', position: pos, label: 'Center' })
      }
      if (Math.abs(draggedBottom - pos) < snapThreshold) {
        guides.push({ id: `h-bottom-${pos}`, type: 'horizontal', position: pos, label: 'Bottom' })
      }
    })
  })

  // Remove duplicate guides
  const uniqueGuides = guides.filter((guide, index, self) => 
    index === self.findIndex(g => g.id === guide.id)
  )

  return (
    <div className="absolute inset-0 pointer-events-none z-30">
      <AnimatePresence>
        {uniqueGuides.map(guide => (
          <motion.div
            key={guide.id}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.1 }}
            className={`absolute ${
              guide.type === 'vertical' 
                ? 'w-0.5 h-full bg-primary/60' 
                : 'h-0.5 w-full bg-primary/60'
            }`}
            style={{
              [guide.type === 'vertical' ? 'left' : 'top']: `${guide.position}px`,
              [guide.type === 'vertical' ? 'top' : 'left']: 0
            }}
          >
            {/* Guide label */}
            {guide.label && (
              <div className={`absolute text-xs text-primary font-medium bg-background/90 px-2 py-1 rounded shadow-sm ${
                guide.type === 'vertical' 
                  ? 'top-2 left-2 -translate-x-1/2' 
                  : 'left-2 top-2 -translate-y-1/2'
              }`}>
                {guide.label}
              </div>
            )}
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  )
}

// Enhanced snap to guides function
export function snapToGuides(
  position: { x: number; y: number },
  elementSize: { width: number; height: number },
  staticElements: any[],
  zoom: number,
  snapThreshold = 8
): { x: number; y: number; snapped: boolean } {
  const adjustedThreshold = snapThreshold / zoom
  let snappedX = position.x
  let snappedY = position.y
  let hasSnapped = false

  // Calculate snap positions for static elements
  const verticalSnapPositions: number[] = []
  const horizontalSnapPositions: number[] = []

  staticElements.forEach(el => {
    // Vertical snap positions (for X alignment)
    verticalSnapPositions.push(el.position.x) // Left edge
    verticalSnapPositions.push(el.position.x + (el.width || 120) / 2) // Center
    verticalSnapPositions.push(el.position.x + (el.width || 120)) // Right edge

    // Horizontal snap positions (for Y alignment)
    horizontalSnapPositions.push(el.position.y) // Top edge
    horizontalSnapPositions.push(el.position.y + (el.height || 60) / 2) // Center
    horizontalSnapPositions.push(el.position.y + (el.height || 60)) // Bottom edge
  })

  // Check for X snapping
  const elementLeft = position.x
  const elementCenter = position.x + elementSize.width / 2
  const elementRight = position.x + elementSize.width

  for (const snapPos of verticalSnapPositions) {
    if (Math.abs(elementLeft - snapPos) < adjustedThreshold) {
      snappedX = snapPos
      hasSnapped = true
      break
    }
    if (Math.abs(elementCenter - snapPos) < adjustedThreshold) {
      snappedX = snapPos - elementSize.width / 2
      hasSnapped = true
      break
    }
    if (Math.abs(elementRight - snapPos) < adjustedThreshold) {
      snappedX = snapPos - elementSize.width
      hasSnapped = true
      break
    }
  }

  // Check for Y snapping
  const elementTop = position.y
  const elementVCenter = position.y + elementSize.height / 2
  const elementBottom = position.y + elementSize.height

  for (const snapPos of horizontalSnapPositions) {
    if (Math.abs(elementTop - snapPos) < adjustedThreshold) {
      snappedY = snapPos
      hasSnapped = true
      break
    }
    if (Math.abs(elementVCenter - snapPos) < adjustedThreshold) {
      snappedY = snapPos - elementSize.height / 2
      hasSnapped = true
      break
    }
    if (Math.abs(elementBottom - snapPos) < adjustedThreshold) {
      snappedY = snapPos - elementSize.height
      hasSnapped = true
      break
    }
  }

  return { x: snappedX, y: snappedY, snapped: hasSnapped }
}
