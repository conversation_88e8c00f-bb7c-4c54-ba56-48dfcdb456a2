{"version": 3, "sources": ["../../../../@tanstack/start-server-functions-fetcher/src/index.ts", "../../../../@tanstack/start-server-functions-client/src/index.ts"], "sourcesContent": ["import {\n  encode,\n  isNotFound,\n  isPlainObject,\n  isRedirect,\n} from '@tanstack/router-core'\nimport { startSerializer } from '@tanstack/start-client-core'\nimport type { MiddlewareClientFnOptions } from '@tanstack/start-client-core'\n\nexport async function serverFnFetcher(\n  url: string,\n  args: Array<any>,\n  handler: (url: string, requestInit: RequestInit) => Promise<Response>,\n) {\n  const _first = args[0]\n\n  // If createServerFn was used to wrap the fetcher,\n  // We need to handle the arguments differently\n  if (isPlainObject(_first) && _first.method) {\n    const first = _first as MiddlewareClientFnOptions<any, any, any> & {\n      headers: HeadersInit\n    }\n    const type = first.data instanceof FormData ? 'formData' : 'payload'\n\n    // Arrange the headers\n    const headers = new Headers({\n      ...(type === 'payload'\n        ? {\n            'content-type': 'application/json',\n            accept: 'application/json',\n          }\n        : {}),\n      ...(first.headers instanceof Headers\n        ? Object.fromEntries(first.headers.entries())\n        : first.headers),\n    })\n\n    // If the method is GET, we need to move the payload to the query string\n    if (first.method === 'GET') {\n      // If the method is GET, we need to move the payload to the query string\n      const encodedPayload = encode({\n        payload: startSerializer.stringify({\n          data: first.data,\n          context: first.context,\n        }),\n      })\n\n      if (encodedPayload) {\n        if (url.includes('?')) {\n          url += `&${encodedPayload}`\n        } else {\n          url += `?${encodedPayload}`\n        }\n      }\n    }\n\n    if (url.includes('?')) {\n      url += `&createServerFn`\n    } else {\n      url += `?createServerFn`\n    }\n    if (first.response === 'raw') {\n      url += `&raw`\n    }\n\n    const handlerResponse = await handler(url, {\n      method: first.method,\n      headers,\n      signal: first.signal,\n      ...getFetcherRequestOptions(first),\n    })\n\n    const response = await handleResponseErrors(handlerResponse)\n\n    // Check if the response is JSON\n    if (response.headers.get('content-type')?.includes('application/json')) {\n      // Even though the response is JSON, we need to decode it\n      // because the server may have transformed it\n      const json = startSerializer.decode(await response.json())\n\n      // If the response is a redirect or not found, throw it\n      // for the router to handle\n      if (isRedirect(json) || isNotFound(json) || json instanceof Error) {\n        throw json\n      }\n\n      return json\n    }\n\n    // Must be a raw response\n    return response\n  }\n\n  // If not a custom fetcher, it was probably\n  // a `use server` function, so just proxy the arguments\n  // through as a POST request\n  const response = await handleResponseErrors(\n    await handler(url, {\n      method: 'POST',\n      headers: {\n        Accept: 'application/json',\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify(args),\n    }),\n  )\n\n  // If the response is JSON, return it parsed\n  const contentType = response.headers.get('content-type')\n  if (contentType && contentType.includes('application/json')) {\n    return startSerializer.decode(await response.json())\n  } else {\n    // Otherwise, return the text as a fallback\n    // If the user wants more than this, they can pass a\n    // request instead\n    return response.text()\n  }\n}\n\nfunction getFetcherRequestOptions(\n  opts: MiddlewareClientFnOptions<any, any, any>,\n) {\n  if (opts.method === 'POST') {\n    if (opts.data instanceof FormData) {\n      opts.data.set('__TSR_CONTEXT', startSerializer.stringify(opts.context))\n      return {\n        body: opts.data,\n      }\n    }\n\n    return {\n      body: startSerializer.stringify({\n        data: opts.data ?? null,\n        context: opts.context,\n      }),\n    }\n  }\n\n  return {}\n}\n\nasync function handleResponseErrors(response: Response) {\n  if (!response.ok) {\n    const contentType = response.headers.get('content-type')\n    const isJson = contentType && contentType.includes('application/json')\n\n    if (isJson) {\n      throw startSerializer.decode(await response.json())\n    }\n\n    throw new Error(await response.text())\n  }\n\n  return response\n}\n", "import { serverFnFetcher } from '@tanstack/start-server-functions-fetcher'\nimport type { CreateRpcFn } from '@tanstack/server-functions-plugin'\n\nfunction sanitizeBase(base: string) {\n  return base.replace(/^\\/|\\/$/g, '')\n}\n\nexport const createClientRpc: CreateRpcFn = (functionId, serverBase) => {\n  const url = `/${sanitizeBase(serverBase)}/${functionId}`\n\n  const clientFn = (...args: Array<any>) => {\n    return serverFnFetcher(url, args, fetch)\n  }\n\n  return Object.assign(clientFn, {\n    url,\n    functionId,\n  })\n}\n"], "mappings": ";;;;;;;;;;;;;AASsB,eAAA,gBACpB,KACA,MACA,SACA;;AACM,QAAA,SAAS,KAAK,CAAC;AAIrB,MAAI,cAAc,MAAM,KAAK,OAAO,QAAQ;AAC1C,UAAM,QAAQ;AAGd,UAAM,OAAO,MAAM,gBAAgB,WAAW,aAAa;AAGrD,UAAA,UAAU,IAAI,QAAQ;MAC1B,GAAI,SAAS,YACT;QACE,gBAAgB;QAChB,QAAQ;MAAA,IAEV,CAAC;MACL,GAAI,MAAM,mBAAmB,UACzB,OAAO,YAAY,MAAM,QAAQ,QAAA,CAAS,IAC1C,MAAM;IAAA,CACX;AAGG,QAAA,MAAM,WAAW,OAAO;AAE1B,YAAM,iBAAiB,OAAO;QAC5B,SAAS,gBAAgB,UAAU;UACjC,MAAM,MAAM;UACZ,SAAS,MAAM;QAChB,CAAA;MAAA,CACF;AAED,UAAI,gBAAgB;AACd,YAAA,IAAI,SAAS,GAAG,GAAG;AACrB,iBAAO,IAAI,cAAc;QAAA,OACpB;AACL,iBAAO,IAAI,cAAc;QAAA;MAC3B;IACF;AAGE,QAAA,IAAI,SAAS,GAAG,GAAG;AACd,aAAA;IAAA,OACF;AACE,aAAA;IAAA;AAEL,QAAA,MAAM,aAAa,OAAO;AACrB,aAAA;IAAA;AAGH,UAAA,kBAAkB,MAAM,QAAQ,KAAK;MACzC,QAAQ,MAAM;MACd;MACA,QAAQ,MAAM;MACd,GAAG,yBAAyB,KAAK;IAAA,CAClC;AAEKA,UAAAA,YAAW,MAAM,qBAAqB,eAAe;AAG3D,SAAIA,KAAAA,UAAS,QAAQ,IAAI,cAAc,MAAnCA,OAAAA,SAAAA,GAAsC,SAAS,kBAAA,GAAqB;AAGtE,YAAM,OAAO,gBAAgB,OAAO,MAAMA,UAAS,KAAA,CAAM;AAIzD,UAAI,WAAW,IAAI,KAAK,WAAW,IAAI,KAAK,gBAAgB,OAAO;AAC3D,cAAA;MAAA;AAGD,aAAA;IAAA;AAIFA,WAAAA;EAAA;AAMT,QAAM,WAAW,MAAM;IACrB,MAAM,QAAQ,KAAK;MACjB,QAAQ;MACR,SAAS;QACP,QAAQ;QACR,gBAAgB;MAClB;MACA,MAAM,KAAK,UAAU,IAAI;IAC1B,CAAA;EACH;AAGA,QAAM,cAAc,SAAS,QAAQ,IAAI,cAAc;AACvD,MAAI,eAAe,YAAY,SAAS,kBAAkB,GAAG;AAC3D,WAAO,gBAAgB,OAAO,MAAM,SAAS,KAAA,CAAM;EAAA,OAC9C;AAIL,WAAO,SAAS,KAAK;EAAA;AAEzB;AAEA,SAAS,yBACP,MACA;AACI,MAAA,KAAK,WAAW,QAAQ;AACtB,QAAA,KAAK,gBAAgB,UAAU;AACjC,WAAK,KAAK,IAAI,iBAAiB,gBAAgB,UAAU,KAAK,OAAO,CAAC;AAC/D,aAAA;QACL,MAAM,KAAK;MACb;IAAA;AAGK,WAAA;MACL,MAAM,gBAAgB,UAAU;QAC9B,MAAM,KAAK,QAAQ;QACnB,SAAS,KAAK;MACf,CAAA;IACH;EAAA;AAGF,SAAO,CAAC;AACV;AAEA,eAAe,qBAAqB,UAAoB;AAClD,MAAA,CAAC,SAAS,IAAI;AAChB,UAAM,cAAc,SAAS,QAAQ,IAAI,cAAc;AACvD,UAAM,SAAS,eAAe,YAAY,SAAS,kBAAkB;AAErE,QAAI,QAAQ;AACV,YAAM,gBAAgB,OAAO,MAAM,SAAS,KAAA,CAAM;IAAA;AAGpD,UAAM,IAAI,MAAM,MAAM,SAAS,KAAA,CAAM;EAAA;AAGhC,SAAA;AACT;;;ACvJA,SAAS,aAAa,MAAc;AAC3B,SAAA,KAAK,QAAQ,YAAY,EAAE;AACpC;AAEa,IAAA,kBAA+B,CAAC,YAAY,eAAe;AACtE,QAAM,MAAM,IAAI,aAAa,UAAU,CAAC,IAAI,UAAU;AAEhD,QAAA,WAAW,IAAI,SAAqB;AACjC,WAAA,gBAAgB,KAAK,MAAM,KAAK;EACzC;AAEO,SAAA,OAAO,OAAO,UAAU;IAC7B;IACA;EAAA,CACD;AACH;", "names": ["response"]}