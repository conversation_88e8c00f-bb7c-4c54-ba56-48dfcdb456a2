import { motion } from 'framer-motion'
import { Calendar, Flag, Layers, ArrowRight, Sparkles } from 'lucide-react'
import { Button } from '~/components/ui/button'
import { useTimelineStore } from '~/stores/timeline-store'

export function CanvasWelcome() {
  const { setSelectedTool } = useTimelineStore()

  const quickActions = [
    {
      id: 'event',
      icon: Calendar,
      label: 'Add Event',
      description: 'Create a timeline event',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'milestone',
      icon: Flag,
      label: 'Add Milestone',
      description: 'Mark important moments',
      color: 'from-purple-500 to-pink-500'
    },
    {
      id: 'phase',
      icon: Layers,
      label: 'Add Phase',
      description: 'Define project phases',
      color: 'from-green-500 to-emerald-500'
    },
    {
      id: 'dependency',
      icon: ArrowRight,
      label: 'Add Dependency',
      description: 'Connect related items',
      color: 'from-orange-500 to-red-500'
    }
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: [0.23, 1, 0.32, 1] }}
      className="absolute inset-0 flex items-center justify-center z-20"
    >
      <div className="text-center max-w-2xl mx-auto p-8">
        {/* Welcome Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <div className="relative mb-4">
            <Sparkles className="h-16 w-16 mx-auto text-primary/60" />
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              className="absolute inset-0 h-16 w-16 mx-auto"
            >
              <div className="w-full h-full border-2 border-dashed border-primary/20 rounded-full" />
            </motion.div>
          </div>
          
          <h2 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent mb-2">
            Welcome to Timeline Creator
          </h2>
          <p className="text-muted-foreground text-lg">
            Start building your timeline by adding events, milestones, and phases
          </p>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-2 gap-4 mb-8"
        >
          {quickActions.map((action, index) => (
            <motion.div
              key={action.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.3 + index * 0.1 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                variant="outline"
                className="w-full h-auto p-6 flex flex-col items-center gap-3 bg-background/50 backdrop-blur-sm border-border/50 hover:bg-background/80 transition-all duration-200"
                onClick={() => setSelectedTool(action.id as any)}
              >
                <div className={`p-3 rounded-xl bg-gradient-to-r ${action.color} text-white`}>
                  <action.icon className="h-6 w-6" />
                </div>
                <div className="text-center">
                  <div className="font-semibold text-foreground">{action.label}</div>
                  <div className="text-xs text-muted-foreground">{action.description}</div>
                </div>
              </Button>
            </motion.div>
          ))}
        </motion.div>

        {/* Tips */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="bg-muted/30 backdrop-blur-sm rounded-2xl p-6 border border-border/30"
        >
          <h3 className="font-semibold text-foreground mb-3">Quick Tips</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-primary rounded-full" />
              <span>Use keyboard shortcuts for faster workflow</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-primary rounded-full" />
              <span>Hold Space + drag to pan around</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-primary rounded-full" />
              <span>Ctrl/Cmd + wheel to zoom in/out</span>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  )
}
