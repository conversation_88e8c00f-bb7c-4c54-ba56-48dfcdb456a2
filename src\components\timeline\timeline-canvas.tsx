import { useCallback, useRef, useState, useEffect } from 'react'
import { motion, useMotionValue, useTransform, PanInfo } from 'framer-motion'
import { TimelineToolbar } from './timeline-toolbar'
import { TimelinePropertyPanel } from './timeline-property-panel'
import { ZoomControls } from './zoom-controls'
import { MiniMap } from './mini-map'
import { CanvasGrid } from './canvas-grid'
import { TimelineAxis } from './timeline-axis'
import { CanvasWelcome } from './canvas-welcome'
import { TimelineElements } from './timeline-elements'
import { SmartGuides } from './smart-guides'
import { SelectionBox, MultiSelectionBounds, MagneticSnapIndicators, CollisionWarnings } from './selection-box'
import { EnhancedNavigationControls, EnhancedMiniMap } from './enhanced-navigation'
import { ContextMenu, useContextMenu } from './context-menu'
import { useTimelineStore } from '~/stores/timeline-store'
import { screenToTimeline, timelineToScreen, snapToGrid } from '~/lib/timeline-utils'
import { useKeyboardShortcuts } from '~/hooks/use-keyboard-shortcuts'
import { detectCollisions } from '~/lib/auto-layout'
import { cn } from '~/lib/utils'

export function TimelineCanvas() {
  const canvasRef = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [isSpacePressed, setIsSpacePressed] = useState(false)
  const [mousePosition, setMousePosition] = useState<{ x: number; y: number } | null>(null)
  const [snapTargets, setSnapTargets] = useState<Array<any>>([])
  const [collisions, setCollisions] = useState<Array<any>>([])

  // Context menu
  const { contextMenu, openContextMenu, closeContextMenu } = useContextMenu()

  // Canvas transform values
  const x = useMotionValue(0)
  const y = useMotionValue(0)
  const scale = useMotionValue(1)

  // Transform for the canvas content
  const transform = useTransform(
    [x, y, scale],
    ([xVal, yVal, scaleVal]) => `translate(${xVal}px, ${yVal}px) scale(${scaleVal})`
  )

  const {
    selectedTool,
    selectedElements,
    zoom,
    setZoom,
    panPosition,
    setPanPosition,
    isPropertyPanelOpen,
    elements,
    createElement,
    canvasMode,
    setCanvasMode,
    setSelectedElements,
    minZoom,
    maxZoom,
    timelineRange,
    resetView
  } = useTimelineStore()

  // Enable enhanced keyboard shortcuts
  useKeyboardShortcuts()

  // Handle wheel events for zooming
  const handleWheel = useCallback((e: WheelEvent) => {
    e.preventDefault()

    if (e.ctrlKey || e.metaKey) {
      // Zoom with proper limits
      const delta = e.deltaY > 0 ? 0.9 : 1.1
      const newZoom = Math.max(minZoom, Math.min(maxZoom, zoom * delta))
      setZoom(newZoom)
      scale.set(newZoom)
    } else {
      // Pan with smoother movement
      const deltaX = e.deltaX * 0.5
      const deltaY = e.deltaY * 0.5
      const currentX = x.get()
      const currentY = y.get()

      x.set(currentX - deltaX)
      y.set(currentY - deltaY)
      setPanPosition({ x: currentX - deltaX, y: currentY - deltaY })
    }
  }, [zoom, setZoom, setPanPosition, x, y, scale, minZoom, maxZoom])

  // Handle pan gestures
  const handlePan = useCallback((event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    if (!isSpacePressed && selectedTool !== 'pan') return

    const currentX = x.get()
    const currentY = y.get()

    x.set(currentX + info.delta.x)
    y.set(currentY + info.delta.y)
    setPanPosition({ x: currentX + info.delta.x, y: currentY + info.delta.y })
  }, [isSpacePressed, selectedTool, setPanPosition, x, y])

  // Handle canvas clicks for element creation
  const handleCanvasClick = useCallback((event: React.MouseEvent) => {
    if (canvasMode === 'dragging') return

    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    const screenX = event.clientX - rect.left
    const screenY = event.clientY - rect.top

    // Only allow creation in visible canvas area (above timeline axis)
    if (screenY > rect.height - 80) return // 80px timeline axis height

    // Convert to timeline coordinates
    const viewport = { zoom, panPosition, timelineRange }
    const timelinePos = screenToTimeline({ x: screenX, y: screenY }, viewport)

    // Ensure the element is within reasonable bounds
    if (timelinePos.date < timelineRange.start || timelinePos.date > timelineRange.end) {
      return // Don't create elements outside timeline range
    }

    // Snap to grid for better positioning
    const snappedCoords = snapToGrid({ x: screenX, y: screenY }, viewport, true, true)

    // Create element based on selected tool
    if (['event', 'milestone', 'phase', 'dependency'].includes(selectedTool)) {
      createElement(selectedTool as any, snappedCoords)
    } else if (selectedTool === 'select') {
      // Clear selection when clicking empty space
      setSelectedElements([])
    }
  }, [canvasMode, zoom, panPosition, timelineRange, selectedTool, createElement, setSelectedElements])

  // Handle mouse movement for visual feedback
  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!canvasRef.current) return

    const rect = canvasRef.current.getBoundingClientRect()
    const x = event.clientX - rect.left
    const y = event.clientY - rect.top

    // Only show preview if we're in creation mode and above timeline axis
    if (['event', 'milestone', 'phase', 'dependency'].includes(selectedTool) && y < rect.height - 80) {
      setMousePosition({ x, y })
    } else {
      setMousePosition(null)
    }
  }, [selectedTool])

  const handleMouseLeave = useCallback(() => {
    setMousePosition(null)
  }, [])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.code === 'Space') {
        e.preventDefault()
        setIsSpacePressed(true)
      }

      // Zoom shortcuts
      if ((e.ctrlKey || e.metaKey) && e.key === '0') {
        e.preventDefault()
        resetView()
        scale.set(1)
        x.set(0)
        y.set(0)
      }

      if ((e.ctrlKey || e.metaKey) && e.key === '=') {
        e.preventDefault()
        const newZoom = Math.min(maxZoom, zoom * 1.2)
        setZoom(newZoom)
        scale.set(newZoom)
      }

      if ((e.ctrlKey || e.metaKey) && e.key === '-') {
        e.preventDefault()
        const newZoom = Math.max(minZoom, zoom * 0.8)
        setZoom(newZoom)
        scale.set(newZoom)
      }
    }

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.code === 'Space') {
        setIsSpacePressed(false)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    window.addEventListener('keyup', handleKeyUp)

    return () => {
      window.removeEventListener('keydown', handleKeyDown)
      window.removeEventListener('keyup', handleKeyUp)
    }
  }, [zoom, setZoom, setPanPosition, scale, x, y])

  // Add wheel event listener
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    canvas.addEventListener('wheel', handleWheel, { passive: false })
    return () => canvas.removeEventListener('wheel', handleWheel)
  }, [handleWheel])

  return (
    <div className="relative h-full w-full overflow-hidden bg-background">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted/5" />

      {/* Main Canvas */}
      <div
        ref={canvasRef}
        className={cn(
          "relative h-full w-full",
          (isSpacePressed || selectedTool === 'pan') && "cursor-grab",
          isDragging && "cursor-grabbing",
          ['event', 'milestone', 'phase', 'dependency'].includes(selectedTool) && "cursor-crosshair"
        )}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
      >
        {/* Canvas Content */}
        <motion.div
          className="relative h-full w-full origin-center"
          style={{ transform }}
          onPan={handlePan}
          onPanStart={() => setIsDragging(true)}
          onPanEnd={() => setIsDragging(false)}
          onClick={handleCanvasClick}
        >
          {/* Grid Background */}
          <CanvasGrid zoom={zoom} />

          {/* Timeline Axis */}
          <TimelineAxis />

          {/* Timeline Elements */}
          <TimelineElements />

          {/* Smart Guides */}
          <SmartGuides
            isVisible={canvasMode === 'dragging'}
            draggedElementIds={selectedElements}
          />

          {/* Multi-Selection Bounds */}
          <MultiSelectionBounds selectedElementIds={selectedElements} />

          {/* Magnetic Snap Indicators */}
          <MagneticSnapIndicators
            draggedElementId={canvasMode === 'dragging' ? selectedElements[0] : null}
            snapTargets={snapTargets}
          />

          {/* Collision Warnings */}
          <CollisionWarnings collidingElements={collisions} />

          {/* Selection Box */}
          <SelectionBox
            isActive={selectedTool === 'select'}
            onSelectionComplete={setSelectedElements}
          />

          {/* Welcome Screen */}
          {elements.length === 0 && <CanvasWelcome />}

          {/* Element Creation Preview */}
          {mousePosition && ['event', 'milestone', 'phase', 'dependency'].includes(selectedTool) && (
            <div
              className="absolute pointer-events-none z-50"
              style={{
                left: mousePosition.x - 60,
                top: mousePosition.y - 30,
                transform: `scale(${zoom})`
              }}
            >
              <div className="w-32 h-16 border-2 border-primary border-dashed rounded-lg bg-primary/10 backdrop-blur-sm flex items-center justify-center">
                <span className="text-xs text-primary font-medium">
                  {selectedTool.charAt(0).toUpperCase() + selectedTool.slice(1)}
                </span>
              </div>
            </div>
          )}
        </motion.div>
      </div>

      {/* UI Overlays */}
      <TimelineToolbar />

      {/* Enhanced Navigation Controls */}
      <EnhancedNavigationControls />

      {/* Enhanced Mini Map */}
      <EnhancedMiniMap />

      {/* Property Panel */}
      {isPropertyPanelOpen && <TimelinePropertyPanel />}

      {/* Context Menu */}
      <ContextMenu
        isOpen={contextMenu.isOpen}
        position={contextMenu.position}
        targetElementId={contextMenu.targetElementId}
        onClose={closeContextMenu}
      />

      {/* Instructions */}
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.4, ease: [0.23, 1, 0.32, 1], delay: 0.2 }}
        className="absolute bottom-6 left-1/2 -translate-x-1/2 z-40"
      >
        <div className="text-xs text-muted-foreground bg-background/98 backdrop-blur-md border border-border/50 rounded-2xl px-6 py-3 shadow-lg">
          <div className="flex items-center gap-4 flex-wrap justify-center">
            <span className="font-medium">Enhanced Timeline Creator</span>
            <span className="text-border">•</span>
            <span className="font-medium">Multi-select with Ctrl+Click</span>
            <span className="text-border">•</span>
            <span className="font-medium">Right-click for context menu</span>
            <span className="text-border">•</span>
            <span className="font-medium">Drag to select multiple</span>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
