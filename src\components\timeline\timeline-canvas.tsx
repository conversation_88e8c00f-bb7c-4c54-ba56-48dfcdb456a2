import { useCallback, useRef, useState, useEffect } from 'react'
import { motion, useMotionValue, useTransform, PanInfo } from 'framer-motion'
import { TimelineToolbar } from './timeline-toolbar'
import { TimelinePropertyPanel } from './timeline-property-panel'
import { ZoomControls } from './zoom-controls'
import { MiniMap } from './mini-map'
import { CanvasGrid } from './canvas-grid'
import { TimelineAxis } from './timeline-axis'
import { CanvasWelcome } from './canvas-welcome'
import { TimelineElements } from './timeline-elements'
import { useTimelineStore } from '~/stores/timeline-store'
import { cn } from '~/lib/utils'

export function TimelineCanvas() {
  const canvasRef = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [isSpacePressed, setIsSpacePressed] = useState(false)

  // Canvas transform values
  const x = useMotionValue(0)
  const y = useMotionValue(0)
  const scale = useMotionValue(1)

  // Transform for the canvas content
  const transform = useTransform(
    [x, y, scale],
    ([xVal, yVal, scaleVal]) => `translate(${xVal}px, ${yVal}px) scale(${scaleVal})`
  )

  const {
    selectedTool,
    selectedElements,
    zoom,
    setZoom,
    panPosition,
    setPanPosition,
    isPropertyPanelOpen,
    elements,
    createElement,
    canvasMode,
    setCanvasMode,
    setSelectedElements
  } = useTimelineStore()

  // Handle wheel events for zooming
  const handleWheel = useCallback((e: WheelEvent) => {
    e.preventDefault()

    if (e.ctrlKey || e.metaKey) {
      // Zoom
      const delta = e.deltaY > 0 ? 0.9 : 1.1
      const newZoom = Math.max(0.1, Math.min(5, zoom * delta))
      setZoom(newZoom)
      scale.set(newZoom)
    } else {
      // Pan
      const deltaX = e.deltaX
      const deltaY = e.deltaY
      const currentX = x.get()
      const currentY = y.get()

      x.set(currentX - deltaX)
      y.set(currentY - deltaY)
      setPanPosition({ x: currentX - deltaX, y: currentY - deltaY })
    }
  }, [zoom, setZoom, setPanPosition, x, y, scale])

  // Handle pan gestures
  const handlePan = useCallback((event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    if (!isSpacePressed && selectedTool !== 'pan') return

    const currentX = x.get()
    const currentY = y.get()

    x.set(currentX + info.delta.x)
    y.set(currentY + info.delta.y)
    setPanPosition({ x: currentX + info.delta.x, y: currentY + info.delta.y })
  }, [isSpacePressed, selectedTool, setPanPosition, x, y])

  // Handle canvas clicks for element creation
  const handleCanvasClick = useCallback((event: React.MouseEvent) => {
    if (canvasMode === 'dragging') return

    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    const clickX = event.clientX - rect.left - panPosition.x
    const clickY = event.clientY - rect.top - panPosition.y

    // Create element based on selected tool
    if (['event', 'milestone', 'phase', 'dependency'].includes(selectedTool)) {
      createElement(selectedTool as any, { x: clickX, y: clickY })
    } else if (selectedTool === 'select') {
      // Clear selection when clicking empty space
      setSelectedElements([])
    }
  }, [canvasMode, panPosition, selectedTool, createElement, setSelectedElements])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.code === 'Space') {
        e.preventDefault()
        setIsSpacePressed(true)
      }

      // Zoom shortcuts
      if ((e.ctrlKey || e.metaKey) && e.key === '0') {
        e.preventDefault()
        setZoom(1)
        scale.set(1)
        x.set(0)
        y.set(0)
        setPanPosition({ x: 0, y: 0 })
      }

      if ((e.ctrlKey || e.metaKey) && e.key === '=') {
        e.preventDefault()
        const newZoom = Math.min(5, zoom * 1.2)
        setZoom(newZoom)
        scale.set(newZoom)
      }

      if ((e.ctrlKey || e.metaKey) && e.key === '-') {
        e.preventDefault()
        const newZoom = Math.max(0.1, zoom * 0.8)
        setZoom(newZoom)
        scale.set(newZoom)
      }
    }

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.code === 'Space') {
        setIsSpacePressed(false)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    window.addEventListener('keyup', handleKeyUp)

    return () => {
      window.removeEventListener('keydown', handleKeyDown)
      window.removeEventListener('keyup', handleKeyUp)
    }
  }, [zoom, setZoom, setPanPosition, scale, x, y])

  // Add wheel event listener
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    canvas.addEventListener('wheel', handleWheel, { passive: false })
    return () => canvas.removeEventListener('wheel', handleWheel)
  }, [handleWheel])

  return (
    <div className="relative h-full w-full overflow-hidden bg-background">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-muted/5" />

      {/* Main Canvas */}
      <div
        ref={canvasRef}
        className={cn(
          "relative h-full w-full",
          (isSpacePressed || selectedTool === 'pan') && "cursor-grab",
          isDragging && "cursor-grabbing"
        )}
      >
        {/* Canvas Content */}
        <motion.div
          className="relative h-full w-full origin-center"
          style={{ transform }}
          onPan={handlePan}
          onPanStart={() => setIsDragging(true)}
          onPanEnd={() => setIsDragging(false)}
          onClick={handleCanvasClick}
        >
          {/* Grid Background */}
          <CanvasGrid zoom={zoom} />

          {/* Timeline Axis */}
          <TimelineAxis />

          {/* Timeline Elements */}
          <TimelineElements />

          {/* Welcome Screen */}
          {elements.length === 0 && <CanvasWelcome />}
        </motion.div>
      </div>

      {/* UI Overlays */}
      <TimelineToolbar />

      {/* Zoom Controls */}
      <ZoomControls
        zoom={zoom}
        onZoomChange={(newZoom) => {
          setZoom(newZoom)
          scale.set(newZoom)
        }}
        onResetView={() => {
          setZoom(1)
          scale.set(1)
          x.set(0)
          y.set(0)
          setPanPosition({ x: 0, y: 0 })
        }}
      />

      {/* Mini Map */}
      <MiniMap />

      {/* Property Panel */}
      {isPropertyPanelOpen && <TimelinePropertyPanel />}

      {/* Instructions */}
      <motion.div
        initial={{ y: 100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.4, ease: [0.23, 1, 0.32, 1], delay: 0.2 }}
        className="absolute bottom-6 left-1/2 -translate-x-1/2 z-40"
      >
        <div className="text-xs text-muted-foreground bg-background/95 backdrop-blur-md border border-border/50 rounded-2xl px-4 py-2 shadow-lg">
          <span className="font-medium">Hold Space + drag to pan</span>
          <span className="mx-2 text-border">•</span>
          <span className="font-medium">Ctrl/Cmd + wheel to zoom</span>
          <span className="mx-2 text-border">•</span>
          <span className="font-medium">Ctrl/Cmd + 0 to reset</span>
        </div>
      </motion.div>
    </div>
  )
}
