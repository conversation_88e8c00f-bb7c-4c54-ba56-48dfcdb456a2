import { useCallback, useRef, useState, useEffect } from 'react'
import { motion, useMotionValue, useTransform, PanInfo } from 'framer-motion'
import { TimelineToolbar } from './timeline-toolbar'
import { TimelinePropertyPanel } from './timeline-property-panel'
import { ZoomControls } from './zoom-controls'
import { MiniMap } from './mini-map'
import { CanvasGrid } from './canvas-grid'
import { TimelineAxis } from './timeline-axis'
import { useTimelineStore } from '~/stores/timeline-store'
import { cn } from '~/lib/utils'

export function TimelineCanvas() {
  const canvasRef = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const [isSpacePressed, setIsSpacePressed] = useState(false)
  
  // Canvas transform values
  const x = useMotionValue(0)
  const y = useMotionValue(0)
  const scale = useMotionValue(1)
  
  // Transform for the canvas content
  const transform = useTransform(
    [x, y, scale],
    ([xVal, yVal, scaleVal]) => `translate(${xVal}px, ${yVal}px) scale(${scaleVal})`
  )

  const {
    selectedTool,
    selectedElements,
    zoom,
    setZoom,
    panPosition,
    setPanPosition,
    isPropertyPanelOpen
  } = useTimelineStore()

  // Handle wheel events for zooming
  const handleWheel = useCallback((e: WheelEvent) => {
    e.preventDefault()
    
    if (e.ctrlKey || e.metaKey) {
      // Zoom
      const delta = e.deltaY > 0 ? 0.9 : 1.1
      const newZoom = Math.max(0.1, Math.min(5, zoom * delta))
      setZoom(newZoom)
      scale.set(newZoom)
    } else {
      // Pan
      const deltaX = e.deltaX
      const deltaY = e.deltaY
      const currentX = x.get()
      const currentY = y.get()
      
      x.set(currentX - deltaX)
      y.set(currentY - deltaY)
      setPanPosition({ x: currentX - deltaX, y: currentY - deltaY })
    }
  }, [zoom, setZoom, setPanPosition, x, y, scale])

  // Handle pan gestures
  const handlePan = useCallback((event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    if (!isSpacePressed && selectedTool !== 'pan') return
    
    const currentX = x.get()
    const currentY = y.get()
    
    x.set(currentX + info.delta.x)
    y.set(currentY + info.delta.y)
    setPanPosition({ x: currentX + info.delta.x, y: currentY + info.delta.y })
  }, [isSpacePressed, selectedTool, setPanPosition, x, y])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.code === 'Space') {
        e.preventDefault()
        setIsSpacePressed(true)
      }
      
      // Zoom shortcuts
      if ((e.ctrlKey || e.metaKey) && e.key === '0') {
        e.preventDefault()
        setZoom(1)
        scale.set(1)
        x.set(0)
        y.set(0)
        setPanPosition({ x: 0, y: 0 })
      }
      
      if ((e.ctrlKey || e.metaKey) && e.key === '=') {
        e.preventDefault()
        const newZoom = Math.min(5, zoom * 1.2)
        setZoom(newZoom)
        scale.set(newZoom)
      }
      
      if ((e.ctrlKey || e.metaKey) && e.key === '-') {
        e.preventDefault()
        const newZoom = Math.max(0.1, zoom * 0.8)
        setZoom(newZoom)
        scale.set(newZoom)
      }
    }

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.code === 'Space') {
        setIsSpacePressed(false)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    window.addEventListener('keyup', handleKeyUp)
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
      window.removeEventListener('keyup', handleKeyUp)
    }
  }, [zoom, setZoom, setPanPosition, scale, x, y])

  // Add wheel event listener
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    canvas.addEventListener('wheel', handleWheel, { passive: false })
    return () => canvas.removeEventListener('wheel', handleWheel)
  }, [handleWheel])

  return (
    <div className="relative h-full w-full overflow-hidden bg-background">
      {/* Main Canvas */}
      <div
        ref={canvasRef}
        className={cn(
          "relative h-full w-full",
          (isSpacePressed || selectedTool === 'pan') && "cursor-grab",
          isDragging && "cursor-grabbing"
        )}
      >
        {/* Canvas Content */}
        <motion.div
          className="relative h-full w-full origin-center"
          style={{ transform }}
          onPan={handlePan}
          onPanStart={() => setIsDragging(true)}
          onPanEnd={() => setIsDragging(false)}
        >
          {/* Grid Background */}
          <CanvasGrid zoom={zoom} />
          
          {/* Timeline Axis */}
          <TimelineAxis />
          
          {/* Timeline Elements will be rendered here */}
          <div className="absolute inset-0">
            {/* Timeline elements will be added here */}
          </div>
        </motion.div>
      </div>

      {/* UI Overlays */}
      <TimelineToolbar />
      
      {/* Zoom Controls */}
      <ZoomControls 
        zoom={zoom}
        onZoomChange={(newZoom) => {
          setZoom(newZoom)
          scale.set(newZoom)
        }}
        onResetView={() => {
          setZoom(1)
          scale.set(1)
          x.set(0)
          y.set(0)
          setPanPosition({ x: 0, y: 0 })
        }}
      />
      
      {/* Mini Map */}
      <MiniMap />
      
      {/* Property Panel */}
      {isPropertyPanelOpen && <TimelinePropertyPanel />}
      
      {/* Instructions */}
      <div className="absolute bottom-4 left-4 text-xs text-muted-foreground bg-background/80 backdrop-blur-sm rounded-md px-2 py-1">
        Hold Space + drag to pan • Ctrl/Cmd + wheel to zoom • Ctrl/Cmd + 0 to reset
      </div>
    </div>
  )
}
