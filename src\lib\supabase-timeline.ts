import { supabase } from './supabase'
import type { TimelineElement } from '~/stores/timeline-store'

export interface TimelineProject {
  id: string
  title: string
  description?: string
  user_id: string
  timeline_range_start: string
  timeline_range_end: string
  settings: {
    zoom: number
    pan_position: { x: number; y: number }
    theme?: string
  }
  created_at: string
  updated_at: string
}

export interface TimelineElementDB {
  id: string
  project_id: string
  type: string
  title: string
  description?: string
  date: string
  end_date?: string
  position: { x: number; y: number }
  color: string
  category?: string
  metadata?: Record<string, any>
  width?: number
  height?: number
  from_element_id?: string
  to_element_id?: string
  created_at: string
  updated_at: string
}

// Timeline Projects
export async function createTimelineProject(project: Omit<TimelineProject, 'id' | 'created_at' | 'updated_at'>) {
  const { data, error } = await supabase
    .from('timeline_projects')
    .insert([project])
    .select()
    .single()

  if (error) throw error
  return data
}

export async function getTimelineProjects(userId: string) {
  const { data, error } = await supabase
    .from('timeline_projects')
    .select('*')
    .eq('user_id', userId)
    .order('updated_at', { ascending: false })

  if (error) throw error
  return data
}

export async function getTimelineProject(projectId: string) {
  const { data, error } = await supabase
    .from('timeline_projects')
    .select('*')
    .eq('id', projectId)
    .single()

  if (error) throw error
  return data
}

export async function updateTimelineProject(projectId: string, updates: Partial<TimelineProject>) {
  const { data, error } = await supabase
    .from('timeline_projects')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', projectId)
    .select()
    .single()

  if (error) throw error
  return data
}

export async function deleteTimelineProject(projectId: string) {
  const { error } = await supabase
    .from('timeline_projects')
    .delete()
    .eq('id', projectId)

  if (error) throw error
}

// Timeline Elements
export async function createTimelineElement(element: Omit<TimelineElementDB, 'id' | 'created_at' | 'updated_at'>) {
  const { data, error } = await supabase
    .from('timeline_elements')
    .insert([element])
    .select()
    .single()

  if (error) throw error
  return data
}

export async function getTimelineElements(projectId: string) {
  const { data, error } = await supabase
    .from('timeline_elements')
    .select('*')
    .eq('project_id', projectId)
    .order('date', { ascending: true })

  if (error) throw error
  return data
}

export async function updateTimelineElement(elementId: string, updates: Partial<TimelineElementDB>) {
  const { data, error } = await supabase
    .from('timeline_elements')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', elementId)
    .select()
    .single()

  if (error) throw error
  return data
}

export async function deleteTimelineElement(elementId: string) {
  const { error } = await supabase
    .from('timeline_elements')
    .delete()
    .eq('id', elementId)

  if (error) throw error
}

export async function bulkUpdateTimelineElements(elements: Array<{ id: string; updates: Partial<TimelineElementDB> }>) {
  const promises = elements.map(({ id, updates }) => 
    updateTimelineElement(id, updates)
  )
  
  return Promise.all(promises)
}

// Real-time subscriptions
export function subscribeToTimelineProject(projectId: string, callback: (payload: any) => void) {
  return supabase
    .channel(`timeline_project_${projectId}`)
    .on('postgres_changes', 
      { 
        event: '*', 
        schema: 'public', 
        table: 'timeline_elements',
        filter: `project_id=eq.${projectId}`
      }, 
      callback
    )
    .subscribe()
}

// Collaboration features
export async function updateUserPresence(projectId: string, userId: string, presence: {
  cursor?: { x: number; y: number }
  selected_elements?: string[]
  current_tool?: string
}) {
  const { error } = await supabase
    .from('user_presence')
    .upsert({
      project_id: projectId,
      user_id: userId,
      presence,
      last_seen: new Date().toISOString()
    })

  if (error) throw error
}

export function subscribeToUserPresence(projectId: string, callback: (payload: any) => void) {
  return supabase
    .channel(`presence_${projectId}`)
    .on('postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'user_presence',
        filter: `project_id=eq.${projectId}`
      },
      callback
    )
    .subscribe()
}

// Auto-save functionality
export class TimelineAutoSaver {
  private saveQueue: Map<string, any> = new Map()
  private saveTimer: NodeJS.Timeout | null = null
  private readonly SAVE_DELAY = 2000 // 2 seconds

  queueSave(elementId: string, updates: any) {
    this.saveQueue.set(elementId, { ...this.saveQueue.get(elementId), ...updates })
    this.debounceSave()
  }

  private debounceSave() {
    if (this.saveTimer) {
      clearTimeout(this.saveTimer)
    }

    this.saveTimer = setTimeout(async () => {
      await this.flushSaves()
    }, this.SAVE_DELAY)
  }

  private async flushSaves() {
    if (this.saveQueue.size === 0) return

    const updates = Array.from(this.saveQueue.entries()).map(([id, updates]) => ({
      id,
      updates
    }))

    this.saveQueue.clear()

    try {
      await bulkUpdateTimelineElements(updates)
    } catch (error) {
      console.error('Failed to auto-save timeline elements:', error)
      // Re-queue failed saves
      updates.forEach(({ id, updates }) => {
        this.saveQueue.set(id, updates)
      })
    }
  }

  async forceSave() {
    if (this.saveTimer) {
      clearTimeout(this.saveTimer)
      this.saveTimer = null
    }
    await this.flushSaves()
  }
}
