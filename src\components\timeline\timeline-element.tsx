import { motion } from 'framer-motion'
import { Calendar, Flag, Layers, ArrowRight, MessageSquare, Type } from 'lucide-react'
import { TimelineElement as TimelineElementType, useTimelineStore } from '~/stores/timeline-store'
import { cn } from '~/lib/utils'

interface TimelineElementProps {
  element: TimelineElementType
  isSelected: boolean
  onSelect: (id: string) => void
  onDragStart: (id: string, event: React.MouseEvent) => void
}

export function TimelineElement({ element, isSelected, onSelect, onDragStart }: TimelineElementProps) {
  const { zoom } = useTimelineStore()

  const getIcon = () => {
    switch (element.type) {
      case 'event':
        return Calendar
      case 'milestone':
        return Flag
      case 'phase':
        return Layers
      case 'dependency':
        return ArrowRight
      case 'comment':
        return MessageSquare
      case 'text':
        return Type
      default:
        return Calendar
    }
  }

  const Icon = getIcon()

  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation()
    onSelect(element.id)
    onDragStart(element.id, e)
  }

  const scale = Math.max(0.5, Math.min(1.5, zoom))
  const width = (element.width || 120) * scale
  const height = (element.height || 60) * scale

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      whileHover={{ scale: 1.02 }}
      className={cn(
        "absolute cursor-pointer select-none",
        "bg-background border-2 rounded-lg shadow-lg",
        "transition-all duration-200",
        isSelected ? "border-primary shadow-primary/20" : "border-border hover:border-primary/50"
      )}
      style={{
        left: element.position.x,
        top: element.position.y, // No restriction needed with bottom timeline
        width,
        height,
        transform: `scale(${scale})`,
        transformOrigin: 'top left',
        zIndex: isSelected ? 30 : 10
      }}
      onMouseDown={handleMouseDown}
    >
      {/* Element Header */}
      <div
        className="flex items-center gap-2 p-2 rounded-t-md"
        style={{ backgroundColor: element.color + '20' }}
      >
        <div
          className="p-1 rounded"
          style={{ backgroundColor: element.color }}
        >
          <Icon className="h-3 w-3 text-white" />
        </div>
        <span className="text-xs font-medium text-foreground truncate flex-1">
          {element.title}
        </span>
      </div>

      {/* Element Content */}
      <div className="p-2 pt-1">
        <div className="text-xs text-muted-foreground mb-1">
          {element.date.toLocaleDateString()}
          {element.endDate && element.type === 'phase' && (
            <span> - {element.endDate.toLocaleDateString()}</span>
          )}
        </div>
        {element.description && (
          <div className="text-xs text-muted-foreground truncate">
            {element.description}
          </div>
        )}
      </div>

      {/* Selection Indicator */}
      {isSelected && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute -inset-1 border-2 border-primary rounded-lg pointer-events-none"
        />
      )}

      {/* Resize Handles (when selected) */}
      {isSelected && element.type === 'phase' && (
        <>
          <div className="absolute -right-1 top-1/2 -translate-y-1/2 w-2 h-4 bg-primary rounded-sm cursor-ew-resize" />
          <div className="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-4 bg-primary rounded-sm cursor-ew-resize" />
        </>
      )}
    </motion.div>
  )
}

// Phase element with duration bar
export function PhaseElement({ element, isSelected, onSelect, onDragStart }: TimelineElementProps) {
  if (element.type !== 'phase') return null

  const { zoom } = useTimelineStore()
  const scale = Math.max(0.5, Math.min(1.5, zoom))
  const width = (element.width || 200) * scale
  const height = (element.height || 60) * scale

  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation()
    onSelect(element.id)
    onDragStart(element.id, e)
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className={cn(
        "absolute cursor-pointer select-none",
        "bg-background border-2 rounded-lg shadow-lg",
        "transition-all duration-200",
        isSelected ? "border-primary shadow-primary/20" : "border-border hover:border-primary/50"
      )}
      style={{
        left: element.position.x,
        top: element.position.y, // No restriction needed with bottom timeline
        width,
        height,
        transform: `scale(${scale})`,
        transformOrigin: 'top left',
        zIndex: isSelected ? 30 : 10
      }}
      onMouseDown={handleMouseDown}
    >
      {/* Phase Bar */}
      <div
        className="h-full rounded-lg flex items-center px-3"
        style={{ backgroundColor: element.color + '20' }}
      >
        <div
          className="p-1.5 rounded mr-2"
          style={{ backgroundColor: element.color }}
        >
          <Layers className="h-4 w-4 text-white" />
        </div>
        <div className="flex-1">
          <div className="text-sm font-medium text-foreground">
            {element.title}
          </div>
          <div className="text-xs text-muted-foreground">
            {element.date.toLocaleDateString()} - {element.endDate?.toLocaleDateString()}
          </div>
        </div>
      </div>

      {/* Selection Indicator */}
      {isSelected && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute -inset-1 border-2 border-primary rounded-lg pointer-events-none"
        />
      )}

      {/* Resize Handles */}
      {isSelected && (
        <>
          <div className="absolute -right-1 top-1/2 -translate-y-1/2 w-2 h-4 bg-primary rounded-sm cursor-ew-resize" />
          <div className="absolute -left-1 top-1/2 -translate-y-1/2 w-2 h-4 bg-primary rounded-sm cursor-ew-resize" />
        </>
      )}
    </motion.div>
  )
}

// Milestone element (diamond shape)
export function MilestoneElement({ element, isSelected, onSelect, onDragStart }: TimelineElementProps) {
  if (element.type !== 'milestone') return null

  const { zoom } = useTimelineStore()
  const scale = Math.max(0.5, Math.min(1.5, zoom))
  const size = 40 * scale

  const handleMouseDown = (e: React.MouseEvent) => {
    e.stopPropagation()
    onSelect(element.id)
    onDragStart(element.id, e)
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.8 }}
      className="absolute cursor-pointer select-none"
      style={{
        left: element.position.x - size / 2,
        top: element.position.y - size / 2, // No restriction needed with bottom timeline
        width: size,
        height: size,
        transform: `scale(${scale})`,
        transformOrigin: 'center',
        zIndex: isSelected ? 30 : 10
      }}
      onMouseDown={handleMouseDown}
    >
      {/* Diamond Shape */}
      <div
        className={cn(
          "w-full h-full rotate-45 rounded-lg shadow-lg border-2 flex items-center justify-center",
          "transition-all duration-200",
          isSelected ? "border-primary shadow-primary/20" : "border-border hover:border-primary/50"
        )}
        style={{ backgroundColor: element.color }}
      >
        <Flag className="h-4 w-4 text-white -rotate-45" />
      </div>

      {/* Label */}
      <div className="absolute top-full left-1/2 -translate-x-1/2 mt-1 text-xs font-medium text-foreground whitespace-nowrap bg-background/80 backdrop-blur-sm px-2 py-1 rounded">
        {element.title}
      </div>

      {/* Selection Indicator */}
      {isSelected && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute -inset-2 border-2 border-primary rounded-lg pointer-events-none"
        />
      )}
    </motion.div>
  )
}
