import { create } from 'zustand'
import { devtools } from 'zustand/middleware'

export type TimelineElement = {
  id: string
  type: 'event' | 'milestone' | 'phase' | 'dependency'
  title: string
  description?: string
  date: Date
  endDate?: Date
  position: { x: number; y: number }
  color: string
  category?: string
  metadata?: Record<string, any>
  // Visual properties
  width?: number
  height?: number
  // Dependency specific
  fromElementId?: string
  toElementId?: string
}

export type TimelineTool =
  | 'select'
  | 'pan'
  | 'event'
  | 'milestone'
  | 'phase'
  | 'dependency'
  | 'comment'
  | 'text'

export type User = {
  id: string
  name: string
  email: string
  color: string
  cursor?: { x: number; y: number }
}

interface TimelineState {
  // Canvas state
  zoom: number
  panPosition: { x: number; y: number }
  canvasMode: 'idle' | 'creating' | 'dragging' | 'selecting'

  // Tools and selection
  selectedTool: TimelineTool
  selectedElements: string[]

  // Timeline data
  elements: TimelineElement[]
  timelineRange: { start: Date; end: Date }

  // UI state
  isPropertyPanelOpen: boolean
  isToolbarVisible: boolean

  // Collaboration
  users: User[]
  currentUser: User | null

  // Actions
  setZoom: (zoom: number) => void
  setPanPosition: (position: { x: number; y: number }) => void
  setCanvasMode: (mode: 'idle' | 'creating' | 'dragging' | 'selecting') => void
  setSelectedTool: (tool: TimelineTool) => void
  setSelectedElements: (elementIds: string[]) => void
  addElement: (element: Omit<TimelineElement, 'id'>) => void
  updateElement: (id: string, updates: Partial<TimelineElement>) => void
  deleteElement: (id: string) => void
  createElement: (type: TimelineElement['type'], position: { x: number; y: number }) => void
  setTimelineRange: (range: { start: Date; end: Date }) => void
  setPropertyPanelOpen: (open: boolean) => void
  setToolbarVisible: (visible: boolean) => void
  addUser: (user: User) => void
  updateUser: (id: string, updates: Partial<User>) => void
  removeUser: (id: string) => void
  setCurrentUser: (user: User) => void
}

export const useTimelineStore = create<TimelineState>()(
  devtools(
    (set, get) => ({
      // Initial state
      zoom: 1,
      panPosition: { x: 0, y: 0 },
      canvasMode: 'idle',
      selectedTool: 'select',
      selectedElements: [],
      elements: [],
      timelineRange: {
        start: new Date(new Date().getFullYear(), 0, 1),
        end: new Date(new Date().getFullYear() + 1, 11, 31)
      },
      isPropertyPanelOpen: false,
      isToolbarVisible: true,
      users: [],
      currentUser: null,

      // Actions
      setZoom: (zoom) => set({ zoom }),
      setPanPosition: (panPosition) => set({ panPosition }),
      setCanvasMode: (canvasMode) => set({ canvasMode }),
      setSelectedTool: (selectedTool) => set({ selectedTool }),
      setSelectedElements: (selectedElements) => set({ selectedElements }),

      addElement: (elementData) => {
        const element: TimelineElement = {
          ...elementData,
          id: `element-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
        }
        set((state) => ({
          elements: [...state.elements, element]
        }))
      },

      updateElement: (id, updates) => {
        set((state) => ({
          elements: state.elements.map((element) =>
            element.id === id ? { ...element, ...updates } : element
          )
        }))
      },

      deleteElement: (id) => {
        set((state) => ({
          elements: state.elements.filter((element) => element.id !== id),
          selectedElements: state.selectedElements.filter((elementId) => elementId !== id)
        }))
      },

      createElement: (type, position) => {
        const now = new Date()
        const colors = {
          event: '#3b82f6',
          milestone: '#8b5cf6',
          phase: '#10b981',
          dependency: '#f59e0b'
        }

        const element: TimelineElement = {
          id: `${type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          type,
          title: `New ${type.charAt(0).toUpperCase() + type.slice(1)}`,
          description: '',
          date: now,
          endDate: type === 'phase' ? new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000) : undefined,
          position,
          color: colors[type],
          category: '',
          width: type === 'phase' ? 200 : 120,
          height: 60
        }

        set((state) => ({
          elements: [...state.elements, element],
          selectedElements: [element.id],
          canvasMode: 'idle'
        }))
      },

      setTimelineRange: (timelineRange) => set({ timelineRange }),
      setPropertyPanelOpen: (isPropertyPanelOpen) => set({ isPropertyPanelOpen }),
      setToolbarVisible: (isToolbarVisible) => set({ isToolbarVisible }),

      addUser: (user) => {
        set((state) => ({
          users: [...state.users.filter(u => u.id !== user.id), user]
        }))
      },

      updateUser: (id, updates) => {
        set((state) => ({
          users: state.users.map((user) =>
            user.id === id ? { ...user, ...updates } : user
          )
        }))
      },

      removeUser: (id) => {
        set((state) => ({
          users: state.users.filter((user) => user.id !== id)
        }))
      },

      setCurrentUser: (currentUser) => set({ currentUser })
    }),
    {
      name: 'timeline-store'
    }
  )
)
