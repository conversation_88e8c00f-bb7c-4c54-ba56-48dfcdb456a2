import { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { useTimelineStore } from '~/stores/timeline-store'
import { screenToTimeline, timelineToScreen } from '~/lib/timeline-utils'

interface ResizeHandlesProps {
  elementId: string
  isVisible: boolean
}

type ResizeDirection = 'n' | 'ne' | 'e' | 'se' | 's' | 'sw' | 'w' | 'nw'

export function ResizeHandles({ elementId, isVisible }: ResizeHandlesProps) {
  const { elements, updateElement, zoom, panPosition, timelineRange } = useTimelineStore()
  const [isResizing, setIsResizing] = useState(false)
  const [resizeDirection, setResizeDirection] = useState<ResizeDirection | null>(null)

  const element = elements.find(el => el.id === elementId)
  if (!element || !isVisible) return null

  const handleResizeStart = useCallback((direction: ResizeDirection, event: React.MouseEvent) => {
    event.stopPropagation()
    setIsResizing(true)
    setResizeDirection(direction)

    const startX = event.clientX
    const startY = event.clientY
    const startWidth = element.width || 120
    const startHeight = element.height || 60
    const startPosition = { ...element.position }

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - startX
      const deltaY = e.clientY - startY

      let newWidth = startWidth
      let newHeight = startHeight
      let newX = startPosition.x
      let newY = startPosition.y

      // Calculate new dimensions based on resize direction
      if (direction.includes('e')) {
        newWidth = Math.max(60, startWidth + deltaX)
      }
      if (direction.includes('w')) {
        newWidth = Math.max(60, startWidth - deltaX)
        newX = startPosition.x + (startWidth - newWidth)
      }
      if (direction.includes('s')) {
        newHeight = Math.max(40, startHeight + deltaY)
      }
      if (direction.includes('n')) {
        newHeight = Math.max(40, startHeight - deltaY)
        newY = startPosition.y + (startHeight - newHeight)
      }

      // For timeline elements, update date when resizing horizontally
      if (direction.includes('e') || direction.includes('w')) {
        const viewport = { zoom, panPosition, timelineRange }
        
        // Update end date for phases when resizing
        if (element.type === 'phase') {
          const rightEdge = newX + newWidth
          const rightTimelinePos = screenToTimeline({ x: rightEdge, y: newY }, viewport)
          
          updateElement(elementId, {
            width: newWidth,
            height: newHeight,
            position: { x: newX, y: newY },
            endDate: rightTimelinePos.date
          })
        } else {
          updateElement(elementId, {
            width: newWidth,
            height: newHeight,
            position: { x: newX, y: newY }
          })
        }
      } else {
        updateElement(elementId, {
          width: newWidth,
          height: newHeight,
          position: { x: newX, y: newY }
        })
      }
    }

    const handleMouseUp = () => {
      setIsResizing(false)
      setResizeDirection(null)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [element, elementId, updateElement, zoom, panPosition, timelineRange])

  const getHandleStyle = (direction: ResizeDirection) => {
    const baseStyle = "absolute w-2 h-2 bg-primary border border-background rounded-sm hover:scale-125 transition-transform"
    
    switch (direction) {
      case 'n': return `${baseStyle} top-0 left-1/2 -translate-x-1/2 -translate-y-1 cursor-n-resize`
      case 'ne': return `${baseStyle} top-0 right-0 translate-x-1 -translate-y-1 cursor-ne-resize`
      case 'e': return `${baseStyle} top-1/2 right-0 translate-x-1 -translate-y-1/2 cursor-e-resize`
      case 'se': return `${baseStyle} bottom-0 right-0 translate-x-1 translate-y-1 cursor-se-resize`
      case 's': return `${baseStyle} bottom-0 left-1/2 -translate-x-1/2 translate-y-1 cursor-s-resize`
      case 'sw': return `${baseStyle} bottom-0 left-0 -translate-x-1 translate-y-1 cursor-sw-resize`
      case 'w': return `${baseStyle} top-1/2 left-0 -translate-x-1 -translate-y-1/2 cursor-w-resize`
      case 'nw': return `${baseStyle} top-0 left-0 -translate-x-1 -translate-y-1 cursor-nw-resize`
      default: return baseStyle
    }
  }

  // Only show relevant handles based on element type
  const getVisibleHandles = (): ResizeDirection[] => {
    switch (element.type) {
      case 'phase':
        return ['e', 'w'] // Only horizontal resizing for phases
      case 'event':
      case 'milestone':
        return ['ne', 'se', 'sw', 'nw'] // Corner handles for square elements
      default:
        return ['n', 'ne', 'e', 'se', 's', 'sw', 'w', 'nw'] // All handles
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="absolute inset-0 pointer-events-none"
    >
      {getVisibleHandles().map(direction => (
        <div
          key={direction}
          className={getHandleStyle(direction)}
          style={{ pointerEvents: 'auto' }}
          onMouseDown={(e) => handleResizeStart(direction, e)}
        />
      ))}
    </motion.div>
  )
}
