import { motion } from 'framer-motion'
import { X, Calendar, Type, Palette, Tag } from 'lucide-react'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Textarea } from '~/components/ui/textarea'
import { Separator } from '~/components/ui/separator'
import { useTimelineStore } from '~/stores/timeline-store'

export function TimelinePropertyPanel() {
  const { 
    selectedElements, 
    elements, 
    setPropertyPanelOpen,
    updateElement 
  } = useTimelineStore()

  const selectedElement = selectedElements.length === 1 
    ? elements.find(el => el.id === selectedElements[0])
    : null

  return (
    <motion.div
      initial={{ x: 400, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      exit={{ x: 400, opacity: 0 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className="absolute top-0 right-0 bottom-0 w-80 bg-background border-l shadow-lg z-50"
    >
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h3 className="font-semibold">Properties</h3>
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => setPropertyPanelOpen(false)}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {selectedElement ? (
            <ElementProperties 
              element={selectedElement}
              onUpdate={(updates) => updateElement(selectedElement.id, updates)}
            />
          ) : selectedElements.length > 1 ? (
            <MultipleElementsProperties />
          ) : (
            <NoSelectionProperties />
          )}
        </div>
      </div>
    </motion.div>
  )
}

function ElementProperties({ 
  element, 
  onUpdate 
}: { 
  element: any
  onUpdate: (updates: any) => void 
}) {
  return (
    <div className="space-y-6">
      {/* Basic Properties */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm font-medium">
          <Type className="h-4 w-4" />
          Basic Properties
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="title">Title</Label>
          <Input
            id="title"
            value={element.title}
            onChange={(e) => onUpdate({ title: e.target.value })}
            placeholder="Enter title..."
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={element.description || ''}
            onChange={(e) => onUpdate({ description: e.target.value })}
            placeholder="Enter description..."
            rows={3}
          />
        </div>
      </div>

      <Separator />

      {/* Date Properties */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm font-medium">
          <Calendar className="h-4 w-4" />
          Date & Time
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="date">Start Date</Label>
          <Input
            id="date"
            type="date"
            value={element.date.toISOString().split('T')[0]}
            onChange={(e) => onUpdate({ date: new Date(e.target.value) })}
          />
        </div>
        
        {element.type === 'phase' && (
          <div className="space-y-2">
            <Label htmlFor="endDate">End Date</Label>
            <Input
              id="endDate"
              type="date"
              value={element.endDate?.toISOString().split('T')[0] || ''}
              onChange={(e) => onUpdate({ endDate: new Date(e.target.value) })}
            />
          </div>
        )}
      </div>

      <Separator />

      {/* Style Properties */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm font-medium">
          <Palette className="h-4 w-4" />
          Appearance
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="color">Color</Label>
          <div className="flex gap-2">
            <Input
              id="color"
              type="color"
              value={element.color}
              onChange={(e) => onUpdate({ color: e.target.value })}
              className="w-16 h-8 p-1"
            />
            <Input
              value={element.color}
              onChange={(e) => onUpdate({ color: e.target.value })}
              placeholder="#000000"
              className="flex-1"
            />
          </div>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Input
            id="category"
            value={element.category || ''}
            onChange={(e) => onUpdate({ category: e.target.value })}
            placeholder="Enter category..."
          />
        </div>
      </div>
    </div>
  )
}

function MultipleElementsProperties() {
  return (
    <div className="text-center text-muted-foreground py-8">
      <Tag className="h-8 w-8 mx-auto mb-2" />
      <p>Multiple elements selected</p>
      <p className="text-sm">Select a single element to edit properties</p>
    </div>
  )
}

function NoSelectionProperties() {
  return (
    <div className="text-center text-muted-foreground py-8">
      <Type className="h-8 w-8 mx-auto mb-2" />
      <p>No element selected</p>
      <p className="text-sm">Select an element to edit its properties</p>
    </div>
  )
}
