import { useState, useCallback, useRef, useEffect } from 'react'
import { motion, useSpring, useMotionValue, animate } from 'framer-motion'
import { ZoomIn, ZoomOut, Maximize, Home, Navigation, Target, Move } from 'lucide-react'
import { Button } from '~/components/ui/button'
import { Slider } from '~/components/ui/slider'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '~/components/ui/tooltip'
import { useTimelineStore } from '~/stores/timeline-store'

export function EnhancedNavigationControls() {
  const {
    zoom,
    setZoom,
    panPosition,
    setPanPosition,
    elements,
    minZoom,
    maxZoom,
    fitToContent,
    resetView
  } = useTimelineStore()

  const [isAnimating, setIsAnimating] = useState(false)

  // Smooth zoom with spring animation
  const smoothZoom = useCallback(async (targetZoom: number, duration = 0.3) => {
    setIsAnimating(true)
    
    await animate(zoom, targetZoom, {
      duration,
      ease: [0.23, 1, 0.32, 1], // Apple's easing curve
      onUpdate: (value) => setZoom(value)
    })
    
    setIsAnimating(false)
  }, [zoom, setZoom])

  // Smooth pan with spring animation
  const smoothPan = useCallback(async (targetPosition: { x: number; y: number }, duration = 0.4) => {
    setIsAnimating(true)
    
    const startX = panPosition.x
    const startY = panPosition.y
    
    await animate(0, 1, {
      duration,
      ease: [0.23, 1, 0.32, 1],
      onUpdate: (progress) => {
        const x = startX + (targetPosition.x - startX) * progress
        const y = startY + (targetPosition.y - startY) * progress
        setPanPosition({ x, y })
      }
    })
    
    setIsAnimating(false)
  }, [panPosition, setPanPosition])

  // Zoom to specific level
  const zoomTo = useCallback((level: number) => {
    const clampedZoom = Math.max(minZoom, Math.min(maxZoom, level))
    smoothZoom(clampedZoom)
  }, [smoothZoom, minZoom, maxZoom])

  // Zoom in/out with smooth animation
  const zoomIn = useCallback(() => {
    zoomTo(zoom * 1.4)
  }, [zoom, zoomTo])

  const zoomOut = useCallback(() => {
    zoomTo(zoom / 1.4)
  }, [zoom, zoomTo])

  // Fit to content with animation
  const animatedFitToContent = useCallback(async () => {
    if (elements.length === 0) return

    // Calculate bounds
    const bounds = elements.reduce((acc, element) => {
      const left = element.position.x
      const right = element.position.x + (element.width || 120)
      const top = element.position.y
      const bottom = element.position.y + (element.height || 60)

      return {
        left: Math.min(acc.left, left),
        right: Math.max(acc.right, right),
        top: Math.min(acc.top, top),
        bottom: Math.max(acc.bottom, bottom)
      }
    }, {
      left: Infinity,
      right: -Infinity,
      top: Infinity,
      bottom: -Infinity
    })

    const contentWidth = bounds.right - bounds.left
    const contentHeight = bounds.bottom - bounds.top
    const centerX = (bounds.left + bounds.right) / 2
    const centerY = (bounds.top + bounds.bottom) / 2

    // Calculate optimal zoom and position
    const viewportWidth = window.innerWidth * 0.8
    const viewportHeight = window.innerHeight * 0.8
    const optimalZoom = Math.min(
      viewportWidth / contentWidth,
      viewportHeight / contentHeight,
      maxZoom
    ) * 0.8 // Add some padding

    const targetPan = {
      x: viewportWidth / 2 - centerX * optimalZoom,
      y: viewportHeight / 2 - centerY * optimalZoom
    }

    // Animate zoom and pan simultaneously
    await Promise.all([
      smoothZoom(optimalZoom, 0.6),
      smoothPan(targetPan, 0.6)
    ])
  }, [elements, smoothZoom, smoothPan, maxZoom])

  // Reset view with animation
  const animatedResetView = useCallback(async () => {
    await Promise.all([
      smoothZoom(1, 0.5),
      smoothPan({ x: 0, y: 0 }, 0.5)
    ])
  }, [smoothZoom, smoothPan])

  // Focus on specific element
  const focusOnElement = useCallback(async (elementId: string) => {
    const element = elements.find(el => el.id === elementId)
    if (!element) return

    const targetZoom = 1.5
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    const targetPan = {
      x: viewportWidth / 2 - element.position.x * targetZoom - (element.width || 120) * targetZoom / 2,
      y: viewportHeight / 2 - element.position.y * targetZoom - (element.height || 60) * targetZoom / 2
    }

    await Promise.all([
      smoothZoom(targetZoom, 0.4),
      smoothPan(targetPan, 0.4)
    ])
  }, [elements, smoothZoom, smoothPan])

  return (
    <TooltipProvider>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="absolute bottom-6 right-6 z-40"
      >
        <div className="bg-background/95 backdrop-blur-md border border-border/50 rounded-2xl p-4 shadow-lg">
          <div className="space-y-4">
            {/* Zoom Controls */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>Zoom</span>
                <span>{Math.round(zoom * 100)}%</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={zoomOut}
                      disabled={zoom <= minZoom || isAnimating}
                      className="h-8 w-8 p-0"
                    >
                      <ZoomOut className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom Out</TooltipContent>
                </Tooltip>

                <Slider
                  value={[zoom]}
                  onValueChange={([value]) => setZoom(value)}
                  min={minZoom}
                  max={maxZoom}
                  step={0.1}
                  className="w-24"
                />

                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={zoomIn}
                      disabled={zoom >= maxZoom || isAnimating}
                      className="h-8 w-8 p-0"
                    >
                      <ZoomIn className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Zoom In</TooltipContent>
                </Tooltip>
              </div>
            </div>

            {/* Quick Zoom Presets */}
            <div className="flex gap-1">
              {[0.5, 1, 1.5, 2].map((level) => (
                <Button
                  key={level}
                  variant={Math.abs(zoom - level) < 0.1 ? "default" : "ghost"}
                  size="sm"
                  onClick={() => zoomTo(level)}
                  disabled={isAnimating}
                  className="h-7 px-2 text-xs"
                >
                  {level * 100}%
                </Button>
              ))}
            </div>

            {/* Navigation Actions */}
            <div className="grid grid-cols-2 gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={animatedFitToContent}
                    disabled={elements.length === 0 || isAnimating}
                    className="h-8"
                  >
                    <Maximize className="h-4 w-4 mr-1" />
                    Fit
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Fit to Content</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={animatedResetView}
                    disabled={isAnimating}
                    className="h-8"
                  >
                    <Home className="h-4 w-4 mr-1" />
                    Reset
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Reset View</TooltipContent>
              </Tooltip>
            </div>

            {/* Position Indicator */}
            <div className="text-xs text-muted-foreground text-center">
              <div>X: {Math.round(panPosition.x)}</div>
              <div>Y: {Math.round(panPosition.y)}</div>
            </div>
          </div>
        </div>
      </motion.div>
    </TooltipProvider>
  )
}

// Enhanced Mini Map with Navigation
export function EnhancedMiniMap() {
  const { elements, zoom, panPosition, setPanPosition } = useTimelineStore()
  const [isDragging, setIsDragging] = useState(false)
  const miniMapRef = useRef<HTMLDivElement>(null)

  const miniMapSize = { width: 200, height: 120 }
  const scale = 0.1

  // Calculate content bounds
  const contentBounds = elements.length > 0 ? elements.reduce((acc, element) => {
    const left = element.position.x
    const right = element.position.x + (element.width || 120)
    const top = element.position.y
    const bottom = element.position.y + (element.height || 60)

    return {
      left: Math.min(acc.left, left),
      right: Math.max(acc.right, right),
      top: Math.min(acc.top, top),
      bottom: Math.max(acc.bottom, bottom)
    }
  }, {
    left: Infinity,
    right: -Infinity,
    top: Infinity,
    bottom: -Infinity
  }) : { left: 0, right: 1000, top: 0, bottom: 600 }

  // Calculate viewport rectangle
  const viewportRect = {
    x: (-panPosition.x / zoom) * scale,
    y: (-panPosition.y / zoom) * scale,
    width: (window.innerWidth / zoom) * scale,
    height: (window.innerHeight / zoom) * scale
  }

  const handleMiniMapClick = useCallback((e: React.MouseEvent) => {
    if (!miniMapRef.current) return

    const rect = miniMapRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    // Convert mini map coordinates to canvas coordinates
    const canvasX = (x / scale) * zoom - window.innerWidth / 2
    const canvasY = (y / scale) * zoom - window.innerHeight / 2

    setPanPosition({ x: -canvasX, y: -canvasY })
  }, [scale, zoom, setPanPosition])

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className="absolute top-6 right-6 z-40"
    >
      <div className="bg-background/95 backdrop-blur-md border border-border/50 rounded-lg p-3 shadow-lg">
        <div className="text-xs text-muted-foreground mb-2">Mini Map</div>
        
        <div
          ref={miniMapRef}
          className="relative bg-muted/30 rounded cursor-pointer overflow-hidden"
          style={{ width: miniMapSize.width, height: miniMapSize.height }}
          onClick={handleMiniMapClick}
        >
          {/* Content elements */}
          {elements.map((element) => (
            <div
              key={element.id}
              className="absolute rounded-sm opacity-60"
              style={{
                left: element.position.x * scale,
                top: element.position.y * scale,
                width: (element.width || 120) * scale,
                height: (element.height || 60) * scale,
                backgroundColor: element.color
              }}
            />
          ))}

          {/* Viewport indicator */}
          <motion.div
            className="absolute border-2 border-primary bg-primary/10"
            style={{
              left: Math.max(0, viewportRect.x),
              top: Math.max(0, viewportRect.y),
              width: Math.min(miniMapSize.width, viewportRect.width),
              height: Math.min(miniMapSize.height, viewportRect.height)
            }}
            animate={{
              left: Math.max(0, viewportRect.x),
              top: Math.max(0, viewportRect.y)
            }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          />
        </div>
      </div>
    </motion.div>
  )
}
