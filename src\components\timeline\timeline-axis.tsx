import { useMemo } from 'react'
import { useTimelineStore } from '~/stores/timeline-store'
import { calculatePixelsPerDay } from '~/lib/timeline-utils'

export function TimelineAxis() {
  const { timelineRange, zoom } = useTimelineStore()

  const timelineData = useMemo(() => {
    const { start, end } = timelineRange
    const totalDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))

    // Calculate pixels per day using Premiere Pro-style zoom
    const pixelsPerDay = calculatePixelsPerDay(timelineRange, zoom)
    const totalWidth = totalDays * pixelsPerDay

    // Generate tick marks with improved zoom-responsive intervals
    const ticks = []
    let tickInterval: number
    let majorTickMultiplier: number

    // Improved zoom thresholds for better year visibility
    if (zoom >= 3) {
      tickInterval = 1 // Daily at very high zoom
      majorTickMultiplier = 7 // Weekly major ticks
    } else if (zoom >= 2) {
      tickInterval = 3 // Every 3 days at high zoom
      majorTickMultiplier = 10 // ~Monthly major ticks
    } else if (zoom >= 1.2) {
      tickInterval = 7 // Weekly at normal zoom
      majorTickMultiplier = 4 // Monthly major ticks
    } else if (zoom >= 0.6) {
      tickInterval = 30 // Monthly at medium zoom
      majorTickMultiplier = 3 // Quarterly major ticks
    } else if (zoom >= 0.3) {
      tickInterval = 90 // Quarterly at low zoom
      majorTickMultiplier = 4 // Yearly major ticks
    } else {
      tickInterval = 180 // Semi-annually at very low zoom
      majorTickMultiplier = 2 // Yearly major ticks
    }

    // Generate ticks with smart major tick detection
    for (let i = 0; i <= totalDays; i += tickInterval) {
      const date = new Date(start.getTime() + i * 24 * 60 * 60 * 1000)
      const x = i * pixelsPerDay

      // Determine if this is a major tick based on date significance
      let isMajor = false
      if (zoom >= 1.2) {
        // For higher zooms, use interval-based major ticks
        isMajor = i % (tickInterval * majorTickMultiplier) === 0
      } else {
        // For lower zooms, prioritize year boundaries and quarters
        const isYearStart = date.getMonth() === 0 && date.getDate() === 1
        const isQuarterStart = [0, 3, 6, 9].includes(date.getMonth()) && date.getDate() === 1

        if (zoom >= 0.3) {
          isMajor = isQuarterStart
        } else {
          isMajor = isYearStart
        }
      }

      ticks.push({
        date,
        x,
        isMajor,
        label: formatDateLabel(date, zoom, isMajor)
      })
    }

    return { totalWidth, ticks, pixelsPerDay }
  }, [timelineRange, zoom])

  return (
    <div className="absolute bottom-0 left-0 right-0 h-20 bg-background/98 backdrop-blur-md border-t border-border/50 z-20 shadow-sm">
      <div className="relative h-full overflow-hidden">
        {/* Timeline line */}
        <div
          className="absolute top-2 h-1 bg-gradient-to-r from-border via-primary/40 to-border shadow-sm"
          style={{
            width: `${Math.max(timelineData.totalWidth, 2000)}px`,
            minWidth: '200vw'
          }}
        />

        {/* Tick marks and labels */}
        {timelineData.ticks.map((tick, index) => (
          <div
            key={index}
            className="absolute top-3"
            style={{ left: `${tick.x}px` }}
          >
            {/* Tick mark */}
            <div
              className={`w-0.5 transition-all duration-200 ${
                tick.isMajor
                  ? 'h-10 bg-foreground/70 shadow-sm'
                  : 'h-6 bg-border/60'
              }`}
            />

            {/* Label */}
            {tick.isMajor && (
              <div className="absolute bottom-16 -translate-x-1/2 text-sm text-foreground font-semibold whitespace-nowrap bg-background/90 backdrop-blur-sm px-2 py-1 rounded-lg border border-border/30 shadow-sm">
                {tick.label}
              </div>
            )}
          </div>
        ))}

        {/* Current date indicator */}
        <CurrentDateIndicator
          timelineRange={timelineRange}
          zoom={zoom}
        />
      </div>
    </div>
  )
}

function CurrentDateIndicator({
  timelineRange,
  zoom
}: {
  timelineRange: { start: Date; end: Date }
  zoom: number
}) {
  const today = new Date()

  // Check if today is within the timeline range
  if (today < timelineRange.start || today > timelineRange.end) {
    return null
  }

  const daysSinceStart = Math.floor(
    (today.getTime() - timelineRange.start.getTime()) / (1000 * 60 * 60 * 24)
  )
  const pixelsPerDay = calculatePixelsPerDay(timelineRange, zoom)
  const x = daysSinceStart * pixelsPerDay

  return (
    <div
      className="absolute top-0 bottom-0 w-1 bg-primary shadow-lg z-15"
      style={{ left: `${x}px` }}
    >
      <div className="absolute top-1 left-1/2 -translate-x-1/2 w-3 h-3 bg-primary rounded-full shadow-md border-2 border-background" />
      <div className="absolute bottom-16 left-1/2 -translate-x-1/2 text-sm text-primary font-bold whitespace-nowrap bg-primary/15 backdrop-blur-sm px-3 py-1.5 rounded-lg border border-primary/30 shadow-md">
        Today
      </div>
    </div>
  )
}

function formatDateLabel(date: Date, zoom: number, isMajor: boolean): string {
  if (zoom >= 3) {
    // Daily view - show month and day
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  } else if (zoom >= 2) {
    // High zoom - show month and day for major ticks, just day for minor
    if (isMajor) {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      })
    } else {
      return date.getDate().toString()
    }
  } else if (zoom >= 1.2) {
    // Weekly view - show month and day
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  } else if (zoom >= 0.6) {
    // Monthly view - show month and year
    return date.toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric'
    })
  } else if (zoom >= 0.3) {
    // Quarterly view - show quarter and year
    const quarter = Math.floor(date.getMonth() / 3) + 1
    return `Q${quarter} ${date.getFullYear()}`
  } else {
    // Yearly view - show just the year with better spacing
    return date.getFullYear().toString()
  }
}
