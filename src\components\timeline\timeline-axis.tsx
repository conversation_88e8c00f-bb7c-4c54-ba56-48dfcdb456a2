import { useMemo, useState } from 'react'
import { useTimelineStore } from '~/stores/timeline-store'
import { calculatePixelsPerDay } from '~/lib/timeline-utils'

export function TimelineAxis() {
  const { timelineRange, zoom } = useTimelineStore()

  const timelineData = useMemo(() => {
    const { start, end } = timelineRange
    const totalDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))

    // Calculate pixels per day using Premiere Pro-style zoom
    const pixelsPerDay = calculatePixelsPerDay(timelineRange, zoom)
    const totalWidth = totalDays * pixelsPerDay

    // Generate tick marks with better intervals
    const ticks = []
    let tickInterval: number

    if (zoom >= 2.5) {
      tickInterval = 1 // Daily at very high zoom
    } else if (zoom >= 1.5) {
      tickInterval = 3 // Every 3 days at high zoom
    } else if (zoom >= 1) {
      tickInterval = 7 // Weekly at normal zoom
    } else if (zoom >= 0.5) {
      tickInterval = 30 // Monthly at medium zoom
    } else {
      tickInterval = 90 // Quarterly at low zoom
    }

    for (let i = 0; i <= totalDays; i += tickInterval) {
      const date = new Date(start.getTime() + i * 24 * 60 * 60 * 1000)
      const x = i * pixelsPerDay

      ticks.push({
        date,
        x,
        isMajor: i % (tickInterval * 4) === 0,
        label: formatDateLabel(date, zoom)
      })
    }

    return { totalWidth, ticks, pixelsPerDay }
  }, [timelineRange, zoom])

  return (
    <div className="absolute bottom-0 left-0 right-0 h-20 bg-background/98 backdrop-blur-md border-t border-border/50 z-20 shadow-sm">
      <div className="relative h-full overflow-hidden">
        {/* Timeline line */}
        <div
          className="absolute top-2 h-1 bg-gradient-to-r from-border via-primary/40 to-border shadow-sm"
          style={{
            width: `${Math.max(timelineData.totalWidth, window.innerWidth * 2)}px`,
            minWidth: '200vw'
          }}
        />

        {/* Tick marks and labels */}
        {timelineData.ticks.map((tick, index) => (
          <div
            key={index}
            className="absolute top-3"
            style={{ left: `${tick.x}px` }}
          >
            {/* Tick mark */}
            <div
              className={`w-0.5 transition-all duration-200 ${
                tick.isMajor
                  ? 'h-10 bg-foreground/70 shadow-sm'
                  : 'h-6 bg-border/60'
              }`}
            />

            {/* Label */}
            {tick.isMajor && (
              <div className="absolute bottom-16 -translate-x-1/2 text-sm text-foreground font-semibold whitespace-nowrap bg-background/90 backdrop-blur-sm px-2 py-1 rounded-lg border border-border/30 shadow-sm">
                {tick.label}
              </div>
            )}
          </div>
        ))}

        {/* Current date indicator */}
        <CurrentDateIndicator
          timelineRange={timelineRange}
          zoom={zoom}
        />
      </div>
    </div>
  )
}

function CurrentDateIndicator({
  timelineRange,
  zoom
}: {
  timelineRange: { start: Date; end: Date }
  zoom: number
}) {
  const today = new Date()

  // Check if today is within the timeline range
  if (today < timelineRange.start || today > timelineRange.end) {
    return null
  }

  const daysSinceStart = Math.floor(
    (today.getTime() - timelineRange.start.getTime()) / (1000 * 60 * 60 * 24)
  )
  const pixelsPerDay = calculatePixelsPerDay(timelineRange, zoom)
  const x = daysSinceStart * pixelsPerDay

  return (
    <div
      className="absolute top-0 bottom-0 w-1 bg-primary shadow-lg z-15"
      style={{ left: `${x}px` }}
    >
      <div className="absolute top-1 left-1/2 -translate-x-1/2 w-3 h-3 bg-primary rounded-full shadow-md border-2 border-background" />
      <div className="absolute bottom-16 left-1/2 -translate-x-1/2 text-sm text-primary font-bold whitespace-nowrap bg-primary/15 backdrop-blur-sm px-3 py-1.5 rounded-lg border border-primary/30 shadow-md">
        Today
      </div>
    </div>
  )
}

function formatDateLabel(date: Date, zoom: number): string {
  if (zoom > 2) {
    // Daily view
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  } else if (zoom > 1) {
    // Weekly view
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  } else if (zoom > 0.5) {
    // Monthly view
    return date.toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric'
    })
  } else {
    // Quarterly view
    return date.toLocaleDateString('en-US', {
      year: 'numeric'
    })
  }
}
