import { useMemo } from 'react'
import { useTimelineStore } from '~/stores/timeline-store'

export function TimelineAxis() {
  const { timelineRange, zoom } = useTimelineStore()

  const timelineData = useMemo(() => {
    const { start, end } = timelineRange
    const totalDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
    
    // Adjust scale based on zoom level
    const pixelsPerDay = Math.max(1, zoom * 2)
    const totalWidth = totalDays * pixelsPerDay
    
    // Generate tick marks
    const ticks = []
    const tickInterval = zoom > 2 ? 1 : zoom > 1 ? 7 : zoom > 0.5 ? 30 : 90 // days
    
    for (let i = 0; i <= totalDays; i += tickInterval) {
      const date = new Date(start.getTime() + i * 24 * 60 * 60 * 1000)
      const x = i * pixelsPerDay
      
      ticks.push({
        date,
        x,
        isMajor: i % (tickInterval * 4) === 0,
        label: formatDateLabel(date, zoom)
      })
    }
    
    return { totalWidth, ticks, pixelsPerDay }
  }, [timelineRange, zoom])

  return (
    <div className="absolute top-20 left-0 right-0 h-16 bg-background/80 backdrop-blur-sm border-b z-10">
      <div className="relative h-full">
        {/* Timeline line */}
        <div 
          className="absolute top-12 h-0.5 bg-border"
          style={{ width: `${timelineData.totalWidth}px` }}
        />
        
        {/* Tick marks and labels */}
        {timelineData.ticks.map((tick, index) => (
          <div
            key={index}
            className="absolute top-8"
            style={{ left: `${tick.x}px` }}
          >
            {/* Tick mark */}
            <div 
              className={`w-0.5 bg-border ${
                tick.isMajor ? 'h-8' : 'h-4'
              }`}
            />
            
            {/* Label */}
            {tick.isMajor && (
              <div className="absolute top-0 -translate-x-1/2 text-xs text-muted-foreground whitespace-nowrap">
                {tick.label}
              </div>
            )}
          </div>
        ))}
        
        {/* Current date indicator */}
        <CurrentDateIndicator 
          timelineRange={timelineRange}
          pixelsPerDay={timelineData.pixelsPerDay}
        />
      </div>
    </div>
  )
}

function CurrentDateIndicator({ 
  timelineRange, 
  pixelsPerDay 
}: { 
  timelineRange: { start: Date; end: Date }
  pixelsPerDay: number 
}) {
  const today = new Date()
  
  // Check if today is within the timeline range
  if (today < timelineRange.start || today > timelineRange.end) {
    return null
  }
  
  const daysSinceStart = Math.floor(
    (today.getTime() - timelineRange.start.getTime()) / (1000 * 60 * 60 * 24)
  )
  const x = daysSinceStart * pixelsPerDay
  
  return (
    <div
      className="absolute top-0 bottom-0 w-0.5 bg-primary z-10"
      style={{ left: `${x}px` }}
    >
      <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-2 h-2 bg-primary rounded-full" />
      <div className="absolute -top-6 left-1/2 -translate-x-1/2 text-xs text-primary font-medium whitespace-nowrap">
        Today
      </div>
    </div>
  )
}

function formatDateLabel(date: Date, zoom: number): string {
  if (zoom > 2) {
    // Daily view
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    })
  } else if (zoom > 1) {
    // Weekly view
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    })
  } else if (zoom > 0.5) {
    // Monthly view
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      year: 'numeric' 
    })
  } else {
    // Quarterly view
    return date.toLocaleDateString('en-US', { 
      year: 'numeric' 
    })
  }
}
