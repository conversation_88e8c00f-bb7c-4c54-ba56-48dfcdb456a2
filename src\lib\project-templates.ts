import type { TimelineElement } from '~/stores/timeline-store'

export interface ProjectTemplate {
  id: string
  name: string
  description: string
  category: 'business' | 'personal' | 'academic' | 'historical'
  thumbnail: string
  timelineRange: {
    start: Date
    end: Date
  }
  elements: Omit<TimelineElement, 'id'>[]
  settings: {
    zoom: number
    panPosition: { x: number; y: number }
  }
}

export const PROJECT_TEMPLATES: ProjectTemplate[] = [
  {
    id: 'project-planning',
    name: 'Project Planning',
    description: 'Complete project lifecycle with phases, milestones, and dependencies',
    category: 'business',
    thumbnail: '/templates/project-planning.png',
    timelineRange: {
      start: new Date(),
      end: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
    },
    elements: [
      {
        type: 'phase',
        title: 'Planning Phase',
        description: 'Initial project planning and requirements gathering',
        date: new Date(),
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        position: { x: 100, y: 100 },
        color: '#3b82f6',
        category: 'Planning',
        width: 200,
        height: 60
      },
      {
        type: 'milestone',
        title: 'Project Kickoff',
        description: 'Official project start',
        date: new Date(),
        position: { x: 50, y: 200 },
        color: '#8b5cf6',
        category: 'Milestone'
      },
      {
        type: 'phase',
        title: 'Development Phase',
        description: 'Core development and implementation',
        date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
        position: { x: 350, y: 100 },
        color: '#10b981',
        category: 'Development',
        width: 300,
        height: 60
      },
      {
        type: 'milestone',
        title: 'MVP Release',
        description: 'Minimum viable product release',
        date: new Date(Date.now() + 120 * 24 * 60 * 60 * 1000),
        position: { x: 500, y: 200 },
        color: '#f59e0b',
        category: 'Milestone'
      },
      {
        type: 'phase',
        title: 'Testing & QA',
        description: 'Quality assurance and testing phase',
        date: new Date(Date.now() + 150 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 210 * 24 * 60 * 60 * 1000),
        position: { x: 700, y: 100 },
        color: '#ef4444',
        category: 'Testing',
        width: 150,
        height: 60
      },
      {
        type: 'milestone',
        title: 'Go Live',
        description: 'Production deployment',
        date: new Date(Date.now() + 210 * 24 * 60 * 60 * 1000),
        position: { x: 850, y: 200 },
        color: '#06d6a0',
        category: 'Milestone'
      }
    ],
    settings: {
      zoom: 1,
      panPosition: { x: 0, y: 0 }
    }
  },
  {
    id: 'product-roadmap',
    name: 'Product Roadmap',
    description: 'Product development roadmap with features and releases',
    category: 'business',
    thumbnail: '/templates/product-roadmap.png',
    timelineRange: {
      start: new Date(),
      end: new Date(Date.now() + 730 * 24 * 60 * 60 * 1000) // 2 years
    },
    elements: [
      {
        type: 'phase',
        title: 'Q1 2024 - Foundation',
        description: 'Core platform development',
        date: new Date(),
        endDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
        position: { x: 100, y: 100 },
        color: '#3b82f6',
        category: 'Development',
        width: 180,
        height: 60
      },
      {
        type: 'event',
        title: 'User Authentication',
        description: 'Implement secure user login system',
        date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        position: { x: 150, y: 200 },
        color: '#8b5cf6',
        category: 'Feature'
      },
      {
        type: 'phase',
        title: 'Q2 2024 - Features',
        description: 'Advanced feature development',
        date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
        endDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
        position: { x: 300, y: 100 },
        color: '#10b981',
        category: 'Development',
        width: 180,
        height: 60
      },
      {
        type: 'milestone',
        title: 'Beta Release',
        description: 'Public beta launch',
        date: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
        position: { x: 500, y: 200 },
        color: '#f59e0b',
        category: 'Release'
      }
    ],
    settings: {
      zoom: 1.2,
      panPosition: { x: 0, y: 0 }
    }
  },
  {
    id: 'historical-timeline',
    name: 'Historical Timeline',
    description: 'Template for historical events and periods',
    category: 'historical',
    thumbnail: '/templates/historical.png',
    timelineRange: {
      start: new Date('1900-01-01'),
      end: new Date('2000-12-31')
    },
    elements: [
      {
        type: 'event',
        title: 'World War I',
        description: 'The Great War (1914-1918)',
        date: new Date('1914-07-28'),
        position: { x: 200, y: 100 },
        color: '#ef4444',
        category: 'War'
      },
      {
        type: 'event',
        title: 'Spanish Flu Pandemic',
        description: 'Global influenza pandemic',
        date: new Date('1918-01-01'),
        position: { x: 250, y: 200 },
        color: '#f59e0b',
        category: 'Health'
      },
      {
        type: 'event',
        title: 'Stock Market Crash',
        description: 'Black Tuesday - Start of Great Depression',
        date: new Date('1929-10-29'),
        position: { x: 350, y: 150 },
        color: '#8b5cf6',
        category: 'Economics'
      },
      {
        type: 'phase',
        title: 'World War II',
        description: 'Global conflict (1939-1945)',
        date: new Date('1939-09-01'),
        endDate: new Date('1945-09-02'),
        position: { x: 500, y: 100 },
        color: '#dc2626',
        category: 'War',
        width: 120,
        height: 60
      }
    ],
    settings: {
      zoom: 0.5,
      panPosition: { x: 0, y: 0 }
    }
  },
  {
    id: 'personal-goals',
    name: 'Personal Goals',
    description: 'Track personal goals and achievements',
    category: 'personal',
    thumbnail: '/templates/personal-goals.png',
    timelineRange: {
      start: new Date(),
      end: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
    },
    elements: [
      {
        type: 'milestone',
        title: 'Start Fitness Journey',
        description: 'Begin regular exercise routine',
        date: new Date(),
        position: { x: 100, y: 100 },
        color: '#10b981',
        category: 'Health'
      },
      {
        type: 'event',
        title: 'Complete Marathon Training',
        description: '16-week marathon training program',
        date: new Date(Date.now() + 112 * 24 * 60 * 60 * 1000),
        position: { x: 400, y: 150 },
        color: '#3b82f6',
        category: 'Health'
      },
      {
        type: 'milestone',
        title: 'Run First Marathon',
        description: 'Complete 26.2 mile race',
        date: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000),
        position: { x: 600, y: 100 },
        color: '#f59e0b',
        category: 'Achievement'
      }
    ],
    settings: {
      zoom: 1.5,
      panPosition: { x: 0, y: 0 }
    }
  },
  {
    id: 'academic-semester',
    name: 'Academic Semester',
    description: 'University semester with courses and deadlines',
    category: 'academic',
    thumbnail: '/templates/academic.png',
    timelineRange: {
      start: new Date(),
      end: new Date(Date.now() + 120 * 24 * 60 * 60 * 1000) // 4 months
    },
    elements: [
      {
        type: 'phase',
        title: 'Fall Semester 2024',
        description: 'Academic semester',
        date: new Date(),
        endDate: new Date(Date.now() + 120 * 24 * 60 * 60 * 1000),
        position: { x: 100, y: 50 },
        color: '#8b5cf6',
        category: 'Academic',
        width: 400,
        height: 40
      },
      {
        type: 'event',
        title: 'Midterm Exams',
        description: 'Mid-semester examinations',
        date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
        position: { x: 300, y: 150 },
        color: '#ef4444',
        category: 'Exam'
      },
      {
        type: 'event',
        title: 'Research Paper Due',
        description: 'Final research paper submission',
        date: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
        position: { x: 400, y: 200 },
        color: '#f59e0b',
        category: 'Assignment'
      },
      {
        type: 'milestone',
        title: 'Graduation',
        description: 'Commencement ceremony',
        date: new Date(Date.now() + 120 * 24 * 60 * 60 * 1000),
        position: { x: 500, y: 150 },
        color: '#10b981',
        category: 'Achievement'
      }
    ],
    settings: {
      zoom: 1.8,
      panPosition: { x: 0, y: 0 }
    }
  }
]

export function getTemplatesByCategory(category: ProjectTemplate['category']) {
  return PROJECT_TEMPLATES.filter(template => template.category === category)
}

export function getTemplateById(id: string) {
  return PROJECT_TEMPLATES.find(template => template.id === id)
}

export function createProjectFromTemplate(template: ProjectTemplate, customizations?: {
  title?: string
  description?: string
  timelineRange?: { start: Date; end: Date }
}) {
  return {
    title: customizations?.title || template.name,
    description: customizations?.description || template.description,
    timelineRange: customizations?.timelineRange || template.timelineRange,
    elements: template.elements.map(element => ({
      ...element,
      id: `${element.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    })),
    settings: template.settings
  }
}
