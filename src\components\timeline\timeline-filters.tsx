import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Filter, Search, Calendar, Tag, Users, X, ChevronDown } from 'lucide-react'
import { Button } from '~/components/ui/button'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { Badge } from '~/components/ui/badge'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import { Checkbox } from '~/components/ui/checkbox'
import { DatePickerWithRange } from '~/components/ui/date-range-picker'
import { useTimelineStore } from '~/stores/timeline-store'

interface TimelineFiltersProps {
  isVisible: boolean
  onToggle: () => void
}

interface FilterState {
  search: string
  dateRange: { from: Date | null; to: Date | null }
  types: string[]
  categories: string[]
  assignees: string[]
}

export function TimelineFilters({ isVisible, onToggle }: TimelineFiltersProps) {
  const { elements } = useTimelineStore()
  const [filters, setFilters] = useState<FilterState>({
    search: '',
    dateRange: { from: null, to: null },
    types: [],
    categories: [],
    assignees: []
  })

  // Extract unique values for filter options
  const uniqueTypes = [...new Set(elements.map(el => el.type))]
  const uniqueCategories = [...new Set(elements.map(el => el.category).filter(Boolean))]
  const uniqueAssignees = [...new Set(elements.flatMap(el => el.metadata?.assignees || []))]

  const activeFiltersCount = [
    filters.search,
    filters.dateRange.from || filters.dateRange.to,
    filters.types.length > 0,
    filters.categories.length > 0,
    filters.assignees.length > 0
  ].filter(Boolean).length

  const clearAllFilters = () => {
    setFilters({
      search: '',
      dateRange: { from: null, to: null },
      types: [],
      categories: [],
      assignees: []
    })
  }

  const updateFilter = (key: keyof FilterState, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const toggleArrayFilter = (key: 'types' | 'categories' | 'assignees', value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: prev[key].includes(value)
        ? prev[key].filter(item => item !== value)
        : [...prev[key], value]
    }))
  }

  return (
    <div className="relative">
      {/* Filter Toggle Button */}
      <Button
        variant={isVisible ? "default" : "outline"}
        size="sm"
        onClick={onToggle}
        className="relative"
      >
        <Filter className="h-4 w-4 mr-2" />
        Filters
        {activeFiltersCount > 0 && (
          <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
            {activeFiltersCount}
          </Badge>
        )}
      </Button>

      {/* Filter Panel */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 mt-2 w-80 bg-background border rounded-lg shadow-lg z-50 p-4"
          >
            <div className="space-y-4">
              {/* Header */}
              <div className="flex items-center justify-between">
                <h3 className="font-semibold">Filter Timeline</h3>
                <div className="flex items-center gap-2">
                  {activeFiltersCount > 0 && (
                    <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                      Clear All
                    </Button>
                  )}
                  <Button variant="ghost" size="sm" onClick={onToggle}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Search Filter */}
              <div className="space-y-2">
                <Label>Search</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search elements..."
                    value={filters.search}
                    onChange={(e) => updateFilter('search', e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Date Range Filter */}
              <div className="space-y-2">
                <Label>Date Range</Label>
                <DatePickerWithRange
                  value={filters.dateRange}
                  onChange={(range) => updateFilter('dateRange', range)}
                />
              </div>

              {/* Element Types Filter */}
              <div className="space-y-2">
                <Label>Element Types</Label>
                <div className="space-y-2">
                  {uniqueTypes.map(type => (
                    <div key={type} className="flex items-center space-x-2">
                      <Checkbox
                        id={`type-${type}`}
                        checked={filters.types.includes(type)}
                        onCheckedChange={() => toggleArrayFilter('types', type)}
                      />
                      <Label htmlFor={`type-${type}`} className="capitalize">
                        {type}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Categories Filter */}
              {uniqueCategories.length > 0 && (
                <div className="space-y-2">
                  <Label>Categories</Label>
                  <div className="space-y-2">
                    {uniqueCategories.map(category => (
                      <div key={category} className="flex items-center space-x-2">
                        <Checkbox
                          id={`category-${category}`}
                          checked={filters.categories.includes(category)}
                          onCheckedChange={() => toggleArrayFilter('categories', category)}
                        />
                        <Label htmlFor={`category-${category}`}>
                          {category}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Assignees Filter */}
              {uniqueAssignees.length > 0 && (
                <div className="space-y-2">
                  <Label>Assignees</Label>
                  <div className="space-y-2">
                    {uniqueAssignees.map(assignee => (
                      <div key={assignee} className="flex items-center space-x-2">
                        <Checkbox
                          id={`assignee-${assignee}`}
                          checked={filters.assignees.includes(assignee)}
                          onCheckedChange={() => toggleArrayFilter('assignees', assignee)}
                        />
                        <Label htmlFor={`assignee-${assignee}`}>
                          {assignee}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Active Filters */}
              {activeFiltersCount > 0 && (
                <div className="space-y-2">
                  <Label>Active Filters</Label>
                  <div className="flex flex-wrap gap-1">
                    {filters.search && (
                      <Badge variant="secondary" className="text-xs">
                        Search: {filters.search}
                        <X 
                          className="h-3 w-3 ml-1 cursor-pointer" 
                          onClick={() => updateFilter('search', '')}
                        />
                      </Badge>
                    )}
                    {filters.types.map(type => (
                      <Badge key={type} variant="secondary" className="text-xs">
                        {type}
                        <X 
                          className="h-3 w-3 ml-1 cursor-pointer" 
                          onClick={() => toggleArrayFilter('types', type)}
                        />
                      </Badge>
                    ))}
                    {filters.categories.map(category => (
                      <Badge key={category} variant="secondary" className="text-xs">
                        {category}
                        <X 
                          className="h-3 w-3 ml-1 cursor-pointer" 
                          onClick={() => toggleArrayFilter('categories', category)}
                        />
                      </Badge>
                    ))}
                    {filters.assignees.map(assignee => (
                      <Badge key={assignee} variant="secondary" className="text-xs">
                        {assignee}
                        <X 
                          className="h-3 w-3 ml-1 cursor-pointer" 
                          onClick={() => toggleArrayFilter('assignees', assignee)}
                        />
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Hook to use filtered elements
export function useFilteredElements(filters: FilterState) {
  const { elements } = useTimelineStore()

  return elements.filter(element => {
    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      const matchesSearch = 
        element.title.toLowerCase().includes(searchLower) ||
        element.description?.toLowerCase().includes(searchLower) ||
        element.category?.toLowerCase().includes(searchLower)
      
      if (!matchesSearch) return false
    }

    // Date range filter
    if (filters.dateRange.from || filters.dateRange.to) {
      const elementDate = element.date
      if (filters.dateRange.from && elementDate < filters.dateRange.from) return false
      if (filters.dateRange.to && elementDate > filters.dateRange.to) return false
    }

    // Type filter
    if (filters.types.length > 0 && !filters.types.includes(element.type)) {
      return false
    }

    // Category filter
    if (filters.categories.length > 0) {
      if (!element.category || !filters.categories.includes(element.category)) {
        return false
      }
    }

    // Assignees filter
    if (filters.assignees.length > 0) {
      const elementAssignees = element.metadata?.assignees || []
      const hasMatchingAssignee = filters.assignees.some(assignee => 
        elementAssignees.includes(assignee)
      )
      if (!hasMatchingAssignee) return false
    }

    return true
  })
}
