import {
  clientOnly,
  createIsomorphicFn,
  createMiddleware,
  createServerFn,
  globalMiddleware,
  hydrate,
  json,
  mergeHeaders,
  registerGlobalMiddleware,
  serverOnly,
  startSerializer
} from "./chunk-DE7T3MBN.js";
import {
  Await,
  HeadContent,
  Router<PERSON><PERSON>ider,
  <PERSON><PERSON><PERSON>,
  useRouter
} from "./chunk-66HE4JXE.js";
import "./chunk-K2ZHHHIO.js";
import {
  invariant,
  isRedirect
} from "./chunk-OSGES7B7.js";
import "./chunk-IVQXI74J.js";
import {
  require_jsx_runtime
} from "./chunk-UBTSCJSJ.js";
import {
  require_react
} from "./chunk-QKMUVXYX.js";
import {
  __toESM
} from "./chunk-5WRI5ZAA.js";

// node_modules/@tanstack/react-start-client/dist/esm/Meta.js
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var Meta = () => {
  if (true) {
    console.warn(
      "The Meta component is deprecated. Use `HeadContent` from `@tanstack/react-router` instead."
    );
  }
  return (0, import_jsx_runtime.jsx)(HeadContent, {});
};

// node_modules/@tanstack/react-start-client/dist/esm/Scripts.js
var import_jsx_runtime2 = __toESM(require_jsx_runtime(), 1);
var Scripts2 = () => {
  if (true) {
    console.warn("The Scripts component was moved to `@tanstack/react-router`");
  }
  return (0, import_jsx_runtime2.jsx)(Scripts, {});
};

// node_modules/@tanstack/react-start-client/dist/esm/StartClient.js
var import_jsx_runtime3 = __toESM(require_jsx_runtime(), 1);
var hydrationPromise;
function StartClient(props) {
  if (!hydrationPromise) {
    if (!props.router.state.matches.length) {
      hydrationPromise = hydrate(props.router);
    } else {
      hydrationPromise = Promise.resolve();
    }
  }
  return (0, import_jsx_runtime3.jsx)(
    Await,
    {
      promise: hydrationPromise,
      children: () => (0, import_jsx_runtime3.jsx)(RouterProvider, { router: props.router })
    }
  );
}

// node_modules/@tanstack/react-start-client/dist/esm/renderRSC.js
var import_react = __toESM(require_react(), 1);
function renderRsc(input) {
  if ((0, import_react.isValidElement)(input)) {
    return input;
  }
  if (typeof input === "object" && !input.state) {
    input.state = {
      status: "pending",
      promise: Promise.resolve().then(() => {
        invariant(false, "renderRSC() is coming soon!");
      }).then((element) => {
        input.state.value = element;
        input.state.status = "success";
      }).catch((err) => {
        input.state.status = "error";
        input.state.error = err;
      })
    };
  }
  if (input.state.status === "pending") {
    throw input.state.promise;
  }
  return input.state.value;
}

// node_modules/@tanstack/react-start-client/dist/esm/useServerFn.js
function useServerFn(serverFn) {
  const router = useRouter();
  return async (...args) => {
    try {
      const res = await serverFn(...args);
      if (isRedirect(res)) {
        throw res;
      }
      return res;
    } catch (err) {
      if (isRedirect(err)) {
        const resolvedRedirect = router.resolveRedirect({
          ...err,
          _fromLocation: router.state.location
        });
        return router.navigate(resolvedRedirect);
      }
      throw err;
    }
  };
}
export {
  Meta,
  Scripts2 as Scripts,
  StartClient,
  clientOnly,
  createIsomorphicFn,
  createMiddleware,
  createServerFn,
  globalMiddleware,
  json,
  mergeHeaders,
  registerGlobalMiddleware,
  renderRsc,
  serverOnly,
  startSerializer,
  useServerFn
};
//# sourceMappingURL=@tanstack_react-start.js.map
