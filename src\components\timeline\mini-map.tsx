import { motion } from 'framer-motion'
import { Map, Maximize2 } from 'lucide-react'
import { useTimelineStore } from '~/stores/timeline-store'
import { Button } from '~/components/ui/button'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '~/components/ui/tooltip'

export function MiniMap() {
  const { zoom, panPosition, elements } = useTimelineStore()

  return (
    <TooltipProvider>
      <motion.div
        initial={{ x: -100, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ duration: 0.4, ease: [0.23, 1, 0.32, 1] }}
        className="absolute bottom-6 left-6 z-50"
      >
        <div className="w-52 bg-background/98 backdrop-blur-md border border-border/50 rounded-2xl shadow-2xl shadow-black/10 p-4">
          {/* Header */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <Map className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium text-foreground">Overview</span>
            </div>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 rounded-md hover:scale-105 transition-all duration-200"
                >
                  <Maximize2 className="h-3 w-3" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top" className="bg-background/95 backdrop-blur-sm">
                <p className="text-xs">Expand Mini Map</p>
              </TooltipContent>
            </Tooltip>
          </div>

          {/* Mini canvas representation */}
          <div className="relative w-full h-24 bg-gradient-to-br from-muted/30 to-muted/60 rounded-xl border border-border/30 overflow-hidden">
            {/* Grid pattern */}
            <div
              className="absolute inset-0 opacity-20"
              style={{
                backgroundImage: `
                  linear-gradient(to right, hsl(var(--border)) 0.5px, transparent 0.5px),
                  linear-gradient(to bottom, hsl(var(--border)) 0.5px, transparent 0.5px)
                `,
                backgroundSize: '8px 8px'
              }}
            />

            {/* Viewport indicator */}
            <div
              className="absolute border-2 border-primary bg-primary/20 rounded-sm transition-all duration-200"
              style={{
                width: `${Math.min(100, 100 / zoom)}%`,
                height: `${Math.min(100, 100 / zoom)}%`,
                left: `${Math.max(0, Math.min(100 - 100/zoom, -panPosition.x / 10))}%`,
                top: `${Math.max(0, Math.min(100 - 100/zoom, -panPosition.y / 10))}%`,
              }}
            />

            {/* Timeline elements representation */}
            {elements.map((element) => (
              <div
                key={element.id}
                className="absolute w-1.5 h-1.5 rounded-full shadow-sm transition-all duration-200"
                style={{
                  backgroundColor: element.color,
                  left: `${(element.position.x + 1000) / 20}%`,
                  top: `${(element.position.y + 500) / 10}%`,
                }}
              />
            ))}

            {/* Center indicator */}
            <div className="absolute top-1/2 left-1/2 w-1 h-1 bg-foreground/40 rounded-full -translate-x-1/2 -translate-y-1/2" />
          </div>

          {/* Stats */}
          <div className="flex items-center justify-between mt-3 text-xs text-muted-foreground">
            <span>Zoom: {Math.round(zoom * 100)}%</span>
            <span>{elements.length} elements</span>
          </div>
        </div>
      </motion.div>
    </TooltipProvider>
  )
}
