import { motion } from 'framer-motion'
import { useTimelineStore } from '~/stores/timeline-store'

export function MiniMap() {
  const { zoom, panPosition, elements } = useTimelineStore()

  return (
    <motion.div
      initial={{ x: -100, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className="absolute bottom-4 left-4 z-50"
    >
      <div className="w-48 h-32 bg-background/95 backdrop-blur-sm border rounded-lg shadow-lg p-2">
        <div className="text-xs text-muted-foreground mb-2">Mini Map</div>
        
        {/* Mini canvas representation */}
        <div className="relative w-full h-20 bg-muted/50 rounded border overflow-hidden">
          {/* Viewport indicator */}
          <div 
            className="absolute border-2 border-primary bg-primary/10"
            style={{
              width: `${Math.min(100, 100 / zoom)}%`,
              height: `${Math.min(100, 100 / zoom)}%`,
              left: `${Math.max(0, Math.min(100 - 100/zoom, -panPosition.x / 10))}%`,
              top: `${Math.max(0, Math.min(100 - 100/zoom, -panPosition.y / 10))}%`,
            }}
          />
          
          {/* Timeline elements representation */}
          {elements.map((element) => (
            <div
              key={element.id}
              className="absolute w-1 h-1 bg-primary rounded-full"
              style={{
                left: `${(element.position.x + 1000) / 20}%`,
                top: `${(element.position.y + 500) / 10}%`,
              }}
            />
          ))}
        </div>
        
        <div className="text-xs text-muted-foreground mt-1">
          Zoom: {Math.round(zoom * 100)}%
        </div>
      </div>
    </motion.div>
  )
}
