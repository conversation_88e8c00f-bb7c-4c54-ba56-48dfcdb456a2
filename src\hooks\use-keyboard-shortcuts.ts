import { useEffect } from 'react'
import { useTimelineStore } from '~/stores/timeline-store'

export function useKeyboardShortcuts() {
  const {
    selectedElements,
    setSelectedElements,
    deleteElement,
    elements,
    updateElement,
    autoPositionElements,
    fitToContent,
    resetView,
    setSelectedTool,
    zoom,
    setZoom,
    minZoom,
    maxZoom
  } = useTimelineStore()

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in inputs
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return
      }

      const { key, ctrlKey, metaKey, shiftKey, altKey } = event
      const isModifierPressed = ctrlKey || metaKey

      switch (key.toLowerCase()) {
        // Selection shortcuts
        case 'a':
          if (isModifierPressed) {
            event.preventDefault()
            setSelectedElements(elements.map(el => el.id))
          }
          break

        case 'escape':
          event.preventDefault()
          setSelectedElements([])
          setSelectedTool('select')
          break

        // Delete selected elements
        case 'delete':
        case 'backspace':
          event.preventDefault()
          selectedElements.forEach(id => deleteElement(id))
          break

        // Tool shortcuts
        case 'v':
          if (!isModifierPressed) {
            event.preventDefault()
            setSelectedTool('select')
          }
          break

        case 'h':
          if (!isModifierPressed) {
            event.preventDefault()
            setSelectedTool('pan')
          }
          break

        case 'e':
          if (!isModifierPressed) {
            event.preventDefault()
            setSelectedTool('event')
          }
          break

        case 'm':
          if (!isModifierPressed) {
            event.preventDefault()
            setSelectedTool('milestone')
          }
          break

        case 'p':
          if (!isModifierPressed) {
            event.preventDefault()
            setSelectedTool('phase')
          }
          break

        case 'd':
          if (!isModifierPressed) {
            event.preventDefault()
            setSelectedTool('dependency')
          }
          break

        // Movement shortcuts
        case 'arrowup':
        case 'arrowdown':
        case 'arrowleft':
        case 'arrowright':
          if (selectedElements.length > 0) {
            event.preventDefault()
            const moveDistance = shiftKey ? 10 : 1
            const deltaX = key === 'ArrowLeft' ? -moveDistance : key === 'ArrowRight' ? moveDistance : 0
            const deltaY = key === 'ArrowUp' ? -moveDistance : key === 'ArrowDown' ? moveDistance : 0

            selectedElements.forEach(id => {
              const element = elements.find(el => el.id === id)
              if (element) {
                updateElement(id, {
                  position: {
                    x: element.position.x + deltaX,
                    y: element.position.y + deltaY
                  }
                })
              }
            })
          }
          break

        // Zoom shortcuts
        case '=':
        case '+':
          if (isModifierPressed) {
            event.preventDefault()
            setZoom(Math.min(maxZoom, zoom * 1.2))
          }
          break

        case '-':
          if (isModifierPressed) {
            event.preventDefault()
            setZoom(Math.max(minZoom, zoom / 1.2))
          }
          break

        case '0':
          if (isModifierPressed) {
            event.preventDefault()
            resetView()
          }
          break

        // Auto-organize shortcuts
        case 'l':
          if (isModifierPressed) {
            event.preventDefault()
            autoPositionElements()
          }
          break

        case 'f':
          if (isModifierPressed) {
            event.preventDefault()
            fitToContent()
          }
          break

        // Duplicate selected elements
        case 'j':
          if (isModifierPressed && selectedElements.length > 0) {
            event.preventDefault()
            selectedElements.forEach(id => {
              const element = elements.find(el => el.id === id)
              if (element) {
                const newElement = {
                  ...element,
                  id: `${element.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                  title: `${element.title} Copy`,
                  position: {
                    x: element.position.x + 20,
                    y: element.position.y + 20
                  }
                }
                // Note: We'd need to add a createElement method that accepts full element data
                // For now, this is a placeholder for the duplication logic
              }
            })
          }
          break

        // Group/Ungroup (placeholder for future feature)
        case 'g':
          if (isModifierPressed && selectedElements.length > 1) {
            event.preventDefault()
            // TODO: Implement grouping functionality
            console.log('Group selected elements')
          }
          break

        case 'u':
          if (isModifierPressed && shiftKey) {
            event.preventDefault()
            // TODO: Implement ungrouping functionality
            console.log('Ungroup selected elements')
          }
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [
    selectedElements,
    setSelectedElements,
    deleteElement,
    elements,
    updateElement,
    autoPositionElements,
    fitToContent,
    resetView,
    setSelectedTool,
    zoom,
    setZoom,
    minZoom,
    maxZoom
  ])
}
