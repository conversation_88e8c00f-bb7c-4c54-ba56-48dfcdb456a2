import { useState, useCallback, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useTimelineStore } from '~/stores/timeline-store'

interface SelectionBoxProps {
  isActive: boolean
  onSelectionComplete: (selectedIds: string[]) => void
}

export function SelectionBox({ isActive, onSelectionComplete }: SelectionBoxProps) {
  const [selectionBox, setSelectionBox] = useState<{
    startX: number
    startY: number
    currentX: number
    currentY: number
    isSelecting: boolean
  } | null>(null)
  
  const { elements, canvasMode, setCanvasMode } = useTimelineStore()
  const canvasRef = useRef<HTMLDivElement>(null)

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    if (!isActive || canvasMode !== 'idle') return

    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    const startX = e.clientX - rect.left
    const startY = e.clientY - rect.top

    setSelectionBox({
      startX,
      startY,
      currentX: startX,
      currentY: startY,
      isSelecting: true
    })

    setCanvasMode('selecting')
  }, [isActive, canvasMode, setCanvasMode])

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!selectionBox?.isSelecting) return

    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    const currentX = e.clientX - rect.left
    const currentY = e.clientY - rect.top

    setSelectionBox(prev => prev ? {
      ...prev,
      currentX,
      currentY
    } : null)
  }, [selectionBox?.isSelecting])

  const handleMouseUp = useCallback(() => {
    if (!selectionBox?.isSelecting) return

    // Calculate selection bounds
    const left = Math.min(selectionBox.startX, selectionBox.currentX)
    const right = Math.max(selectionBox.startX, selectionBox.currentX)
    const top = Math.min(selectionBox.startY, selectionBox.currentY)
    const bottom = Math.max(selectionBox.startY, selectionBox.currentY)

    // Find elements within selection bounds
    const selectedIds = elements
      .filter(element => {
        const elementLeft = element.position.x
        const elementRight = element.position.x + (element.width || 120)
        const elementTop = element.position.y
        const elementBottom = element.position.y + (element.height || 60)

        return (
          elementLeft < right &&
          elementRight > left &&
          elementTop < bottom &&
          elementBottom > top
        )
      })
      .map(element => element.id)

    onSelectionComplete(selectedIds)
    setSelectionBox(null)
    setCanvasMode('idle')
  }, [selectionBox, elements, onSelectionComplete, setCanvasMode])

  // Add event listeners
  React.useEffect(() => {
    if (selectionBox?.isSelecting) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)

      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }
    }
  }, [selectionBox?.isSelecting, handleMouseMove, handleMouseUp])

  if (!isActive) return null

  const selectionStyle = selectionBox ? {
    left: Math.min(selectionBox.startX, selectionBox.currentX),
    top: Math.min(selectionBox.startY, selectionBox.currentY),
    width: Math.abs(selectionBox.currentX - selectionBox.startX),
    height: Math.abs(selectionBox.currentY - selectionBox.startY)
  } : undefined

  return (
    <div
      ref={canvasRef}
      className="absolute inset-0 z-40"
      onMouseDown={handleMouseDown}
      style={{ pointerEvents: isActive ? 'auto' : 'none' }}
    >
      <AnimatePresence>
        {selectionBox?.isSelecting && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute border-2 border-primary bg-primary/10 backdrop-blur-sm"
            style={selectionStyle}
          >
            {/* Selection corners for visual feedback */}
            <div className="absolute -top-1 -left-1 w-2 h-2 bg-primary rounded-full" />
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-primary rounded-full" />
            <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-primary rounded-full" />
            <div className="absolute -bottom-1 -right-1 w-2 h-2 bg-primary rounded-full" />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

// Enhanced Multi-Selection Bounding Box
export function MultiSelectionBounds({ selectedElementIds }: { selectedElementIds: string[] }) {
  const { elements } = useTimelineStore()

  if (selectedElementIds.length <= 1) return null

  const selectedElements = elements.filter(el => selectedElementIds.includes(el.id))
  
  if (selectedElements.length === 0) return null

  // Calculate bounding box
  const bounds = selectedElements.reduce((acc, element) => {
    const left = element.position.x
    const right = element.position.x + (element.width || 120)
    const top = element.position.y
    const bottom = element.position.y + (element.height || 60)

    return {
      left: Math.min(acc.left, left),
      right: Math.max(acc.right, right),
      top: Math.min(acc.top, top),
      bottom: Math.max(acc.bottom, bottom)
    }
  }, {
    left: Infinity,
    right: -Infinity,
    top: Infinity,
    bottom: -Infinity
  })

  const width = bounds.right - bounds.left
  const height = bounds.bottom - bounds.top

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className="absolute border-2 border-primary border-dashed bg-primary/5 pointer-events-none"
      style={{
        left: bounds.left - 8,
        top: bounds.top - 8,
        width: width + 16,
        height: height + 16
      }}
    >
      {/* Corner handles */}
      <div className="absolute -top-2 -left-2 w-4 h-4 bg-primary rounded-full border-2 border-background" />
      <div className="absolute -top-2 -right-2 w-4 h-4 bg-primary rounded-full border-2 border-background" />
      <div className="absolute -bottom-2 -left-2 w-4 h-4 bg-primary rounded-full border-2 border-background" />
      <div className="absolute -bottom-2 -right-2 w-4 h-4 bg-primary rounded-full border-2 border-background" />

      {/* Selection count badge */}
      <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full">
        {selectedElementIds.length} selected
      </div>
    </motion.div>
  )
}

// Magnetic Snapping Indicators
export function MagneticSnapIndicators({ 
  draggedElementId, 
  snapTargets 
}: { 
  draggedElementId: string | null
  snapTargets: Array<{
    id: string
    type: 'edge' | 'center' | 'grid'
    position: { x: number; y: number }
    direction: 'horizontal' | 'vertical'
  }>
}) {
  if (!draggedElementId || snapTargets.length === 0) return null

  return (
    <div className="absolute inset-0 pointer-events-none z-30">
      <AnimatePresence>
        {snapTargets.map((target, index) => (
          <motion.div
            key={`${target.id}-${index}`}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.15 }}
            className={`absolute ${
              target.direction === 'horizontal' 
                ? 'h-0.5 w-full bg-primary/60' 
                : 'w-0.5 h-full bg-primary/60'
            }`}
            style={{
              [target.direction === 'horizontal' ? 'top' : 'left']: `${
                target.direction === 'horizontal' ? target.position.y : target.position.x
              }px`,
              [target.direction === 'horizontal' ? 'left' : 'top']: 0
            }}
          >
            {/* Snap indicator dot */}
            <div 
              className="absolute w-2 h-2 bg-primary rounded-full border border-background"
              style={{
                [target.direction === 'horizontal' ? 'left' : 'top']: `${
                  target.direction === 'horizontal' ? target.position.x : target.position.y
                }px`,
                [target.direction === 'horizontal' ? 'top' : 'left']: '-4px'
              }}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  )
}

// Collision Detection Warnings
export function CollisionWarnings({ 
  collidingElements 
}: { 
  collidingElements: Array<{
    id1: string
    id2: string
    severity: 'warning' | 'error'
  }>
}) {
  const { elements } = useTimelineStore()

  if (collidingElements.length === 0) return null

  return (
    <div className="absolute inset-0 pointer-events-none z-25">
      <AnimatePresence>
        {collidingElements.map((collision, index) => {
          const element1 = elements.find(el => el.id === collision.id1)
          const element2 = elements.find(el => el.id === collision.id2)
          
          if (!element1 || !element2) return null

          const centerX = (element1.position.x + element2.position.x) / 2
          const centerY = (element1.position.y + element2.position.y) / 2

          return (
            <motion.div
              key={`collision-${index}`}
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              className={`absolute w-6 h-6 rounded-full border-2 ${
                collision.severity === 'error' 
                  ? 'bg-red-500/20 border-red-500' 
                  : 'bg-yellow-500/20 border-yellow-500'
              }`}
              style={{
                left: centerX - 12,
                top: centerY - 12
              }}
            >
              <div className="absolute inset-0 rounded-full animate-ping bg-current opacity-20" />
            </motion.div>
          )
        })}
      </AnimatePresence>
    </div>
  )
}
