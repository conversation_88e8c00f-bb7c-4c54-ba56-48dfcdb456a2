import { createFile<PERSON><PERSON><PERSON>, <PERSON> } from '@tanstack/react-router'
import { motion } from 'framer-motion'
import { But<PERSON> } from '~/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '~/components/ui/card'
import { Badge } from '~/components/ui/badge'
import { Separator } from '~/components/ui/separator'
import { Plus, Calendar, Clock, Users, Zap, ArrowRight, Star, Sparkles } from 'lucide-react'
import { InteractiveTimelineDemo } from '~/components/interactive-timeline-demo'

export const Route = createFileRoute('/')({
  component: Home,
})

function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      {/* Hero Section */}
      <section className="container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Badge variant="secondary" className="mb-4 px-3 py-1">
              <Sparkles className="w-3 h-3 mr-1" />
              Now with AI-powered suggestions
            </Badge>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent"
          >
            Create Beautiful Timelines
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="text-xl text-muted-foreground mb-8 leading-relaxed"
          >
            Transform your ideas into stunning visual timelines. Perfect for project planning,
            storytelling, and organizing information chronologically with Miro-inspired design.
          </motion.p>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Button asChild size="lg" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 group">
              <Link to="/signup">
                <Plus className="w-5 h-5 mr-2" />
                Create Your First Timeline
                <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" className="group">
              <Calendar className="w-5 h-5 mr-2" />
              View Examples
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
          </motion.div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="container mx-auto px-4 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h3 className="text-3xl font-bold mb-4">Why Choose Timeline Creator?</h3>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Built with modern technologies and Apple's design principles for the best user experience.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[
            {
              icon: Zap,
              title: "Lightning Fast",
              description: "Built with TanStack Start and optimized for performance with sub-100ms interactions.",
              color: "text-yellow-500"
            },
            {
              icon: Users,
              title: "Real-time Collaboration",
              description: "Work together with your team in real-time with Supabase-powered live updates.",
              color: "text-blue-500"
            },
            {
              icon: Calendar,
              title: "Intuitive Design",
              description: "Miro-inspired interface with Apple's animation principles for delightful interactions.",
              color: "text-green-500"
            }
          ].map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
            >
              <Card className="h-full hover:shadow-lg transition-all duration-300 group">
                <CardHeader>
                  <motion.div
                    className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform"
                    whileHover={{ rotate: 5 }}
                  >
                    <feature.icon className={`w-6 h-6 ${feature.color}`} />
                  </motion.div>
                  <CardTitle className="group-hover:text-primary transition-colors">
                    {feature.title}
                  </CardTitle>
                  <CardDescription>
                    {feature.description}
                  </CardDescription>
                </CardHeader>
              </Card>
            </motion.div>
          ))}
        </div>
      </section>

      {/* Interactive Timeline Demo */}
      <section className="container mx-auto px-4 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h3 className="text-3xl font-bold mb-4">See It In Action</h3>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Experience the power of visual storytelling with our interactive timeline creator.
            Try adding, removing, and clicking on events below!
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <InteractiveTimelineDemo />
        </motion.div>
      </section>

      {/* Footer */}
      <footer className="border-t bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container mx-auto px-4 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="flex flex-col md:flex-row justify-between items-center"
          >
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <motion.div
                className="w-6 h-6 bg-gradient-to-r from-blue-600 to-purple-600 rounded"
                whileHover={{ rotate: 180 }}
                transition={{ duration: 0.3 }}
              />
              <span className="font-semibold">Timeline Creator</span>
            </div>
            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
              <span>Built with TanStack Start</span>
              <Separator orientation="vertical" className="h-4" />
              <span>Powered by Supabase</span>
              <Separator orientation="vertical" className="h-4" />
              <span>Styled with shadcn/ui</span>
            </div>
          </motion.div>
        </div>
      </footer>
    </div>
  )
}
