/**
 * Timeline coordinate system utilities
 */

export interface TimelineCoordinates {
  x: number // Screen x coordinate
  y: number // Screen y coordinate
}

export interface TimelinePosition {
  date: Date // Timeline date
  lane: number // Timeline lane (0-based)
}

export interface TimelineViewport {
  zoom: number
  panPosition: { x: number; y: number }
  timelineRange: { start: Date; end: Date }
}

/**
 * Convert screen coordinates to timeline position
 */
export function screenToTimeline(
  screenCoords: TimelineCoordinates,
  viewport: TimelineViewport
): TimelinePosition {
  const { zoom, panPosition, timelineRange } = viewport
  
  // Adjust for pan position
  const adjustedX = screenCoords.x - panPosition.x
  const adjustedY = screenCoords.y - panPosition.y
  
  // Calculate pixels per day
  const totalDays = Math.ceil(
    (timelineRange.end.getTime() - timelineRange.start.getTime()) / (1000 * 60 * 60 * 24)
  )
  const pixelsPerDay = Math.max(2, zoom * 50)
  
  // Convert x to date
  const dayOffset = adjustedX / pixelsPerDay
  const date = new Date(timelineRange.start.getTime() + dayOffset * 24 * 60 * 60 * 1000)
  
  // Convert y to lane (assuming 80px per lane)
  const laneHeight = 80
  const lane = Math.max(0, Math.floor(adjustedY / laneHeight))
  
  return { date, lane }
}

/**
 * Convert timeline position to screen coordinates
 */
export function timelineToScreen(
  timelinePos: TimelinePosition,
  viewport: TimelineViewport
): TimelineCoordinates {
  const { zoom, panPosition, timelineRange } = viewport
  
  // Calculate pixels per day
  const pixelsPerDay = Math.max(2, zoom * 50)
  
  // Convert date to x
  const dayOffset = Math.ceil(
    (timelinePos.date.getTime() - timelineRange.start.getTime()) / (1000 * 60 * 60 * 1000 * 24)
  )
  const x = dayOffset * pixelsPerDay + panPosition.x
  
  // Convert lane to y
  const laneHeight = 80
  const y = timelinePos.lane * laneHeight + panPosition.y
  
  return { x, y }
}

/**
 * Snap coordinates to timeline grid
 */
export function snapToGrid(
  coords: TimelineCoordinates,
  viewport: TimelineViewport,
  snapToDate = true,
  snapToLane = true
): TimelineCoordinates {
  if (!snapToDate && !snapToLane) return coords
  
  const timelinePos = screenToTimeline(coords, viewport)
  
  if (snapToDate) {
    // Snap to nearest day
    timelinePos.date.setHours(0, 0, 0, 0)
  }
  
  if (snapToLane) {
    // Lane is already snapped by the conversion
  }
  
  return timelineToScreen(timelinePos, viewport)
}

/**
 * Get timeline bounds in screen coordinates
 */
export function getTimelineBounds(viewport: TimelineViewport): {
  left: number
  right: number
  top: number
  bottom: number
} {
  const { zoom, panPosition, timelineRange } = viewport
  
  const totalDays = Math.ceil(
    (timelineRange.end.getTime() - timelineRange.start.getTime()) / (1000 * 60 * 60 * 24)
  )
  const pixelsPerDay = Math.max(2, zoom * 50)
  const totalWidth = totalDays * pixelsPerDay
  
  return {
    left: panPosition.x,
    right: panPosition.x + totalWidth,
    top: panPosition.y,
    bottom: panPosition.y + 1000 // Arbitrary large height
  }
}

/**
 * Calculate optimal zoom level to fit content
 */
export function calculateFitZoom(
  elements: Array<{ date: Date; endDate?: Date }>,
  viewportWidth: number,
  viewportHeight: number,
  padding = 100
): number {
  if (elements.length === 0) return 1
  
  // Find date range of all elements
  const dates = elements.flatMap(el => [el.date, el.endDate].filter(Boolean) as Date[])
  const minDate = new Date(Math.min(...dates.map(d => d.getTime())))
  const maxDate = new Date(Math.max(...dates.map(d => d.getTime())))
  
  // Add some padding to the date range
  const paddingDays = 30
  minDate.setDate(minDate.getDate() - paddingDays)
  maxDate.setDate(maxDate.getDate() + paddingDays)
  
  const totalDays = Math.ceil((maxDate.getTime() - minDate.getTime()) / (1000 * 60 * 60 * 24))
  const availableWidth = viewportWidth - padding * 2
  
  // Calculate zoom to fit width
  const pixelsPerDay = availableWidth / totalDays
  const zoom = Math.max(0.1, Math.min(3, pixelsPerDay / 50))
  
  return zoom
}

/**
 * Get visible timeline range based on viewport
 */
export function getVisibleTimelineRange(
  viewport: TimelineViewport,
  viewportWidth: number,
  viewportHeight: number
): { start: Date; end: Date } {
  const { zoom, panPosition, timelineRange } = viewport
  
  const pixelsPerDay = Math.max(2, zoom * 50)
  
  // Calculate visible start date
  const visibleStartDays = Math.max(0, -panPosition.x / pixelsPerDay)
  const visibleStart = new Date(
    timelineRange.start.getTime() + visibleStartDays * 24 * 60 * 60 * 1000
  )
  
  // Calculate visible end date
  const visibleEndDays = visibleStartDays + viewportWidth / pixelsPerDay
  const visibleEnd = new Date(
    timelineRange.start.getTime() + visibleEndDays * 24 * 60 * 60 * 1000
  )
  
  return {
    start: new Date(Math.max(visibleStart.getTime(), timelineRange.start.getTime())),
    end: new Date(Math.min(visibleEnd.getTime(), timelineRange.end.getTime()))
  }
}
