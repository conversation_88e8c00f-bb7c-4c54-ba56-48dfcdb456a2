import { useState, useCallback } from 'react'
import { AnimatePresence } from 'framer-motion'
import { TimelineElement, PhaseElement, MilestoneElement } from './timeline-element'
import { useTimelineStore } from '~/stores/timeline-store'
import { screenToTimeline, snapToGrid } from '~/lib/timeline-utils'

export function TimelineElements() {
  const {
    elements,
    selectedElements,
    setSelectedElements,
    updateElement,
    canvasMode,
    setCanvasMode,
    zoom,
    panPosition,
    timelineRange
  } = useTimelineStore()

  const [dragState, setDragState] = useState<{
    isDragging: boolean
    elementIds: string[]
    startPosition: { x: number; y: number }
    elementStartPositions: Map<string, { x: number; y: number; date: Date }>
    snapEnabled: boolean
    constrainToTimeline: boolean
  }>({
    isDragging: false,
    elementIds: [],
    startPosition: { x: 0, y: 0 },
    elementStartPositions: new Map(),
    snapEnabled: true,
    constrainToTimeline: true
  })

  const handleElementSelect = useCallback((elementId: string, event?: React.MouseEvent) => {
    if (event?.ctrlKey || event?.metaKey) {
      // Multi-select with Ctrl/Cmd
      const isSelected = selectedElements.includes(elementId)
      if (isSelected) {
        setSelectedElements(selectedElements.filter(id => id !== elementId))
      } else {
        setSelectedElements([...selectedElements, elementId])
      }
    } else {
      // Single select
      setSelectedElements([elementId])
    }
  }, [selectedElements, setSelectedElements])

  const handleDragStart = useCallback((elementId: string, event: React.MouseEvent) => {
    const element = elements.find(el => el.id === elementId)
    if (!element) return

    // Determine which elements to drag
    const elementsToDrag = selectedElements.includes(elementId)
      ? selectedElements
      : [elementId]

    // If element wasn't selected, select it
    if (!selectedElements.includes(elementId)) {
      setSelectedElements([elementId])
    }

    // Store initial positions and dates for all elements being dragged
    const elementStartPositions = new Map()
    elementsToDrag.forEach(id => {
      const el = elements.find(e => e.id === id)
      if (el) {
        elementStartPositions.set(id, {
          x: el.position.x,
          y: el.position.y,
          date: el.date
        })
      }
    })

    setCanvasMode('dragging')
    setDragState({
      isDragging: true,
      elementIds: elementsToDrag,
      startPosition: { x: event.clientX, y: event.clientY },
      elementStartPositions,
      snapEnabled: !event.altKey, // Alt key disables snapping
      constrainToTimeline: !event.shiftKey // Shift key allows moving outside timeline
    })

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - dragState.startPosition.x
      const deltaY = e.clientY - dragState.startPosition.y

      // Update all selected elements
      dragState.elementIds.forEach(id => {
        const startPos = dragState.elementStartPositions.get(id)
        if (!startPos) return

        let newX = startPos.x + deltaX
        let newY = startPos.y + deltaY

        // Apply constraints if enabled
        if (dragState.constrainToTimeline) {
          // Keep elements above timeline axis (bottom 80px)
          newY = Math.max(0, Math.min(newY, window.innerHeight - 140))
          // Keep elements within reasonable horizontal bounds
          newX = Math.max(-100, Math.min(newX, window.innerWidth + 100))
        }

        // Apply snapping if enabled
        if (dragState.snapEnabled) {
          const viewport = { zoom, panPosition, timelineRange }
          const snappedCoords = snapToGrid({ x: newX, y: newY }, viewport, true, true)
          newX = snappedCoords.x
          newY = snappedCoords.y
        }

        // Convert position back to date for timeline synchronization
        const viewport = { zoom, panPosition, timelineRange }
        const timelinePos = screenToTimeline({ x: newX, y: newY }, viewport)

        updateElement(id, {
          position: { x: newX, y: newY },
          date: timelinePos.date
        })
      })
    }

    const handleMouseUp = () => {
      setCanvasMode('idle')
      setDragState({
        isDragging: false,
        elementIds: [],
        startPosition: { x: 0, y: 0 },
        elementStartPositions: new Map(),
        snapEnabled: true,
        constrainToTimeline: true
      })
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [elements, updateElement, setCanvasMode, dragState])

  const renderElement = (element: any) => {
    const isSelected = selectedElements.includes(element.id)

    switch (element.type) {
      case 'phase':
        return (
          <PhaseElement
            key={element.id}
            element={element}
            isSelected={isSelected}
            onSelect={handleElementSelect}
            onDragStart={handleDragStart}
          />
        )
      case 'milestone':
        return (
          <MilestoneElement
            key={element.id}
            element={element}
            isSelected={isSelected}
            onSelect={handleElementSelect}
            onDragStart={handleDragStart}
          />
        )
      default:
        return (
          <TimelineElement
            key={element.id}
            element={element}
            isSelected={isSelected}
            onSelect={handleElementSelect}
            onDragStart={handleDragStart}
          />
        )
    }
  }

  return (
    <div className="absolute inset-0 pointer-events-none">
      <div className="relative w-full h-full pointer-events-auto">
        <AnimatePresence>
          {elements.map(renderElement)}
        </AnimatePresence>
      </div>
    </div>
  )
}
