import { useState, useCallback } from 'react'
import { AnimatePresence } from 'framer-motion'
import { TimelineElement, PhaseElement, MilestoneElement } from './timeline-element'
import { useTimelineStore } from '~/stores/timeline-store'

export function TimelineElements() {
  const {
    elements,
    selectedElements,
    setSelectedElements,
    updateElement,
    canvasMode,
    setCanvasMode
  } = useTimelineStore()

  const [dragState, setDragState] = useState<{
    isDragging: boolean
    elementId: string | null
    startPosition: { x: number; y: number }
    elementStartPosition: { x: number; y: number }
  }>({
    isDragging: false,
    elementId: null,
    startPosition: { x: 0, y: 0 },
    elementStartPosition: { x: 0, y: 0 }
  })

  const handleElementSelect = useCallback((elementId: string) => {
    setSelectedElements([elementId])
  }, [setSelectedElements])

  const handleDragStart = useCallback((elementId: string, event: React.MouseEvent) => {
    const element = elements.find(el => el.id === elementId)
    if (!element) return

    setCanvasMode('dragging')
    setDragState({
      isDragging: true,
      elementId,
      startPosition: { x: event.clientX, y: event.clientY },
      elementStartPosition: { x: element.position.x, y: element.position.y }
    })

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX = e.clientX - dragState.startPosition.x
      const deltaY = e.clientY - dragState.startPosition.y

      updateElement(elementId, {
        position: {
          x: dragState.elementStartPosition.x + deltaX,
          y: dragState.elementStartPosition.y + deltaY
        }
      })
    }

    const handleMouseUp = () => {
      setCanvasMode('idle')
      setDragState({
        isDragging: false,
        elementId: null,
        startPosition: { x: 0, y: 0 },
        elementStartPosition: { x: 0, y: 0 }
      })
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }

    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)
  }, [elements, updateElement, setCanvasMode, dragState])

  const renderElement = (element: any) => {
    const isSelected = selectedElements.includes(element.id)

    switch (element.type) {
      case 'phase':
        return (
          <PhaseElement
            key={element.id}
            element={element}
            isSelected={isSelected}
            onSelect={handleElementSelect}
            onDragStart={handleDragStart}
          />
        )
      case 'milestone':
        return (
          <MilestoneElement
            key={element.id}
            element={element}
            isSelected={isSelected}
            onSelect={handleElementSelect}
            onDragStart={handleDragStart}
          />
        )
      default:
        return (
          <TimelineElement
            key={element.id}
            element={element}
            isSelected={isSelected}
            onSelect={handleElementSelect}
            onDragStart={handleDragStart}
          />
        )
    }
  }

  return (
    <div className="absolute inset-0 pointer-events-none">
      <div className="relative w-full h-full pointer-events-auto">
        <AnimatePresence>
          {elements.map(renderElement)}
        </AnimatePresence>
      </div>
    </div>
  )
}
