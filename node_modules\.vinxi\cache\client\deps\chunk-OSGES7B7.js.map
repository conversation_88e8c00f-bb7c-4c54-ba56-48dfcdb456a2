{"version": 3, "sources": ["../../../../@tanstack/history/src/index.ts", "../../../../tiny-invariant/dist/esm/tiny-invariant.js", "../../../../@tanstack/router-core/src/utils.ts", "../../../../@tanstack/router-core/src/path.ts", "../../../../@tanstack/router-core/src/not-found.ts", "../../../../@tanstack/router-core/src/qss.ts", "../../../../@tanstack/router-core/src/searchParams.ts", "../../../../@tanstack/router-core/src/root.ts", "../../../../@tanstack/router-core/src/redirect.ts", "../../../../@tanstack/store/src/scheduler.ts", "../../../../@tanstack/store/src/store.ts", "../../../../@tanstack/store/src/derived.ts", "../../../../@tanstack/router-core/src/scroll-restoration.ts", "../../../../@tanstack/router-core/src/router.ts", "../../../../@tanstack/router-core/src/defer.ts", "../../../../@tanstack/router-core/src/Matches.ts", "../../../../@tanstack/router-core/src/searchMiddleware.ts", "../../../../@tanstack/router-core/src/link.ts", "../../../../@tanstack/router-core/src/route.ts"], "sourcesContent": ["// While the public API was clearly inspired by the \"history\" npm package,\n// This implementation attempts to be more lightweight by\n// making assumptions about the way TanStack Router works\n\nexport interface NavigateOptions {\n  ignoreBlocker?: boolean\n}\n\ntype SubscriberHistoryAction =\n  | {\n      type: Exclude<HistoryAction, 'GO'>\n    }\n  | {\n      type: 'GO'\n      index: number\n    }\n\ntype SubscriberArgs = {\n  location: HistoryLocation\n  action: SubscriberHistoryAction\n}\n\nexport interface RouterHistory {\n  location: HistoryLocation\n  length: number\n  subscribers: Set<(opts: SubscriberArgs) => void>\n  subscribe: (cb: (opts: SubscriberArgs) => void) => () => void\n  push: (path: string, state?: any, navigateOpts?: NavigateOptions) => void\n  replace: (path: string, state?: any, navigateOpts?: NavigateOptions) => void\n  go: (index: number, navigateOpts?: NavigateOptions) => void\n  back: (navigateOpts?: NavigateOptions) => void\n  forward: (navigateOpts?: NavigateOptions) => void\n  canGoBack: () => boolean\n  createHref: (href: string) => string\n  block: (blocker: NavigationBlocker) => () => void\n  flush: () => void\n  destroy: () => void\n  notify: (action: SubscriberHistoryAction) => void\n  _ignoreSubscribers?: boolean\n}\n\nexport interface HistoryLocation extends ParsedPath {\n  state: ParsedHistoryState\n}\n\nexport interface ParsedPath {\n  href: string\n  pathname: string\n  search: string\n  hash: string\n}\n\nexport interface HistoryState {}\n\nexport type ParsedHistoryState = HistoryState & {\n  key?: string\n  __TSR_index: number\n}\n\ntype ShouldAllowNavigation = any\n\nexport type HistoryAction = 'PUSH' | 'REPLACE' | 'FORWARD' | 'BACK' | 'GO'\n\nexport type BlockerFnArgs = {\n  currentLocation: HistoryLocation\n  nextLocation: HistoryLocation\n  action: HistoryAction\n}\n\nexport type BlockerFn = (\n  args: BlockerFnArgs,\n) => Promise<ShouldAllowNavigation> | ShouldAllowNavigation\n\nexport type NavigationBlocker = {\n  blockerFn: BlockerFn\n  enableBeforeUnload?: (() => boolean) | boolean\n}\n\ntype TryNavigateArgs = {\n  task: () => void\n  type: 'PUSH' | 'REPLACE' | 'BACK' | 'FORWARD' | 'GO'\n  navigateOpts?: NavigateOptions\n} & (\n  | {\n      type: 'PUSH' | 'REPLACE'\n      path: string\n      state: any\n    }\n  | {\n      type: 'BACK' | 'FORWARD' | 'GO'\n    }\n)\n\nconst stateIndexKey = '__TSR_index'\nconst popStateEvent = 'popstate'\nconst beforeUnloadEvent = 'beforeunload'\n\nexport function createHistory(opts: {\n  getLocation: () => HistoryLocation\n  getLength: () => number\n  pushState: (path: string, state: any) => void\n  replaceState: (path: string, state: any) => void\n  go: (n: number) => void\n  back: (ignoreBlocker: boolean) => void\n  forward: (ignoreBlocker: boolean) => void\n  createHref: (path: string) => string\n  flush?: () => void\n  destroy?: () => void\n  onBlocked?: () => void\n  getBlockers?: () => Array<NavigationBlocker>\n  setBlockers?: (blockers: Array<NavigationBlocker>) => void\n  // Avoid notifying on forward/back/go, used for browser history as we already get notified by the popstate event\n  notifyOnIndexChange?: boolean\n}): RouterHistory {\n  let location = opts.getLocation()\n  const subscribers = new Set<(opts: SubscriberArgs) => void>()\n\n  const notify = (action: SubscriberHistoryAction) => {\n    location = opts.getLocation()\n    subscribers.forEach((subscriber) => subscriber({ location, action }))\n  }\n\n  const handleIndexChange = (action: SubscriberHistoryAction) => {\n    if (opts.notifyOnIndexChange ?? true) notify(action)\n    else location = opts.getLocation()\n  }\n\n  const tryNavigation = async ({\n    task,\n    navigateOpts,\n    ...actionInfo\n  }: TryNavigateArgs) => {\n    const ignoreBlocker = navigateOpts?.ignoreBlocker ?? false\n    if (ignoreBlocker) {\n      task()\n      return\n    }\n\n    const blockers = opts.getBlockers?.() ?? []\n    const isPushOrReplace =\n      actionInfo.type === 'PUSH' || actionInfo.type === 'REPLACE'\n    if (typeof document !== 'undefined' && blockers.length && isPushOrReplace) {\n      for (const blocker of blockers) {\n        const nextLocation = parseHref(actionInfo.path, actionInfo.state)\n        const isBlocked = await blocker.blockerFn({\n          currentLocation: location,\n          nextLocation,\n          action: actionInfo.type,\n        })\n        if (isBlocked) {\n          opts.onBlocked?.()\n          return\n        }\n      }\n    }\n\n    task()\n  }\n\n  return {\n    get location() {\n      return location\n    },\n    get length() {\n      return opts.getLength()\n    },\n    subscribers,\n    subscribe: (cb: (opts: SubscriberArgs) => void) => {\n      subscribers.add(cb)\n\n      return () => {\n        subscribers.delete(cb)\n      }\n    },\n    push: (path, state, navigateOpts) => {\n      const currentIndex = location.state[stateIndexKey]\n      state = assignKeyAndIndex(currentIndex + 1, state)\n      tryNavigation({\n        task: () => {\n          opts.pushState(path, state)\n          notify({ type: 'PUSH' })\n        },\n        navigateOpts,\n        type: 'PUSH',\n        path,\n        state,\n      })\n    },\n    replace: (path, state, navigateOpts) => {\n      const currentIndex = location.state[stateIndexKey]\n      state = assignKeyAndIndex(currentIndex, state)\n      tryNavigation({\n        task: () => {\n          opts.replaceState(path, state)\n          notify({ type: 'REPLACE' })\n        },\n        navigateOpts,\n        type: 'REPLACE',\n        path,\n        state,\n      })\n    },\n    go: (index, navigateOpts) => {\n      tryNavigation({\n        task: () => {\n          opts.go(index)\n          handleIndexChange({ type: 'GO', index })\n        },\n        navigateOpts,\n        type: 'GO',\n      })\n    },\n    back: (navigateOpts) => {\n      tryNavigation({\n        task: () => {\n          opts.back(navigateOpts?.ignoreBlocker ?? false)\n          handleIndexChange({ type: 'BACK' })\n        },\n        navigateOpts,\n        type: 'BACK',\n      })\n    },\n    forward: (navigateOpts) => {\n      tryNavigation({\n        task: () => {\n          opts.forward(navigateOpts?.ignoreBlocker ?? false)\n          handleIndexChange({ type: 'FORWARD' })\n        },\n        navigateOpts,\n        type: 'FORWARD',\n      })\n    },\n    canGoBack: () => location.state[stateIndexKey] !== 0,\n    createHref: (str) => opts.createHref(str),\n    block: (blocker) => {\n      if (!opts.setBlockers) return () => {}\n      const blockers = opts.getBlockers?.() ?? []\n      opts.setBlockers([...blockers, blocker])\n\n      return () => {\n        const blockers = opts.getBlockers?.() ?? []\n        opts.setBlockers?.(blockers.filter((b) => b !== blocker))\n      }\n    },\n    flush: () => opts.flush?.(),\n    destroy: () => opts.destroy?.(),\n    notify,\n  }\n}\n\nfunction assignKeyAndIndex(index: number, state: HistoryState | undefined) {\n  if (!state) {\n    state = {} as HistoryState\n  }\n  return {\n    ...state,\n    key: createRandomKey(),\n    [stateIndexKey]: index,\n  } as ParsedHistoryState\n}\n\n/**\n * Creates a history object that can be used to interact with the browser's\n * navigation. This is a lightweight API wrapping the browser's native methods.\n * It is designed to work with TanStack Router, but could be used as a standalone API as well.\n * IMPORTANT: This API implements history throttling via a microtask to prevent\n * excessive calls to the history API. In some browsers, calling history.pushState or\n * history.replaceState in quick succession can cause the browser to ignore subsequent\n * calls. This API smooths out those differences and ensures that your application\n * state will *eventually* match the browser state. In most cases, this is not a problem,\n * but if you need to ensure that the browser state is up to date, you can use the\n * `history.flush` method to immediately flush all pending state changes to the browser URL.\n * @param opts\n * @param opts.getHref A function that returns the current href (path + search + hash)\n * @param opts.createHref A function that takes a path and returns a href (path + search + hash)\n * @returns A history instance\n */\nexport function createBrowserHistory(opts?: {\n  parseLocation?: () => HistoryLocation\n  createHref?: (path: string) => string\n  window?: any\n}): RouterHistory {\n  const win =\n    opts?.window ??\n    (typeof document !== 'undefined' ? window : (undefined as any))\n\n  const originalPushState = win.history.pushState\n  const originalReplaceState = win.history.replaceState\n\n  let blockers: Array<NavigationBlocker> = []\n  const _getBlockers = () => blockers\n  const _setBlockers = (newBlockers: Array<NavigationBlocker>) =>\n    (blockers = newBlockers)\n\n  const createHref = opts?.createHref ?? ((path) => path)\n  const parseLocation =\n    opts?.parseLocation ??\n    (() =>\n      parseHref(\n        `${win.location.pathname}${win.location.search}${win.location.hash}`,\n        win.history.state,\n      ))\n\n  // Ensure there is always a key to start\n  if (!win.history.state?.key) {\n    win.history.replaceState(\n      {\n        [stateIndexKey]: 0,\n        key: createRandomKey(),\n      },\n      '',\n    )\n  }\n\n  let currentLocation = parseLocation()\n  let rollbackLocation: HistoryLocation | undefined\n\n  let nextPopIsGo = false\n  let ignoreNextPop = false\n  let skipBlockerNextPop = false\n  let ignoreNextBeforeUnload = false\n\n  const getLocation = () => currentLocation\n\n  let next:\n    | undefined\n    | {\n        // This is the latest location that we were attempting to push/replace\n        href: string\n        // This is the latest state that we were attempting to push/replace\n        state: any\n        // This is the latest type that we were attempting to push/replace\n        isPush: boolean\n      }\n\n  // We need to track the current scheduled update to prevent\n  // multiple updates from being scheduled at the same time.\n  let scheduled: Promise<void> | undefined\n\n  // This function flushes the next update to the browser history\n  const flush = () => {\n    if (!next) {\n      return\n    }\n\n    // We need to ignore any updates to the subscribers while we update the browser history\n    history._ignoreSubscribers = true\n\n    // Update the browser history\n    ;(next.isPush ? win.history.pushState : win.history.replaceState)(\n      next.state,\n      '',\n      next.href,\n    )\n\n    // Stop ignoring subscriber updates\n    history._ignoreSubscribers = false\n\n    // Reset the nextIsPush flag and clear the scheduled update\n    next = undefined\n    scheduled = undefined\n    rollbackLocation = undefined\n  }\n\n  // This function queues up a call to update the browser history\n  const queueHistoryAction = (\n    type: 'push' | 'replace',\n    destHref: string,\n    state: any,\n  ) => {\n    const href = createHref(destHref)\n\n    if (!scheduled) {\n      rollbackLocation = currentLocation\n    }\n\n    // Update the location in memory\n    currentLocation = parseHref(destHref, state)\n\n    // Keep track of the next location we need to flush to the URL\n    next = {\n      href,\n      state,\n      isPush: next?.isPush || type === 'push',\n    }\n\n    if (!scheduled) {\n      // Schedule an update to the browser history\n      scheduled = Promise.resolve().then(() => flush())\n    }\n  }\n\n  // NOTE: this function can probably be removed\n  const onPushPop = (type: 'PUSH' | 'REPLACE') => {\n    currentLocation = parseLocation()\n    history.notify({ type })\n  }\n\n  const onPushPopEvent = async () => {\n    if (ignoreNextPop) {\n      ignoreNextPop = false\n      return\n    }\n\n    const nextLocation = parseLocation()\n    const delta =\n      nextLocation.state[stateIndexKey] - currentLocation.state[stateIndexKey]\n    const isForward = delta === 1\n    const isBack = delta === -1\n    const isGo = (!isForward && !isBack) || nextPopIsGo\n    nextPopIsGo = false\n\n    const action = isGo ? 'GO' : isBack ? 'BACK' : 'FORWARD'\n    const notify: SubscriberHistoryAction = isGo\n      ? {\n          type: 'GO',\n          index: delta,\n        }\n      : {\n          type: isBack ? 'BACK' : 'FORWARD',\n        }\n\n    if (skipBlockerNextPop) {\n      skipBlockerNextPop = false\n    } else {\n      const blockers = _getBlockers()\n      if (typeof document !== 'undefined' && blockers.length) {\n        for (const blocker of blockers) {\n          const isBlocked = await blocker.blockerFn({\n            currentLocation,\n            nextLocation,\n            action,\n          })\n          if (isBlocked) {\n            ignoreNextPop = true\n            win.history.go(1)\n            history.notify(notify)\n            return\n          }\n        }\n      }\n    }\n\n    currentLocation = parseLocation()\n    history.notify(notify)\n  }\n\n  const onBeforeUnload = (e: BeforeUnloadEvent) => {\n    if (ignoreNextBeforeUnload) {\n      ignoreNextBeforeUnload = false\n      return\n    }\n\n    let shouldBlock = false\n\n    // If one blocker has a non-disabled beforeUnload, we should block\n    const blockers = _getBlockers()\n    if (typeof document !== 'undefined' && blockers.length) {\n      for (const blocker of blockers) {\n        const shouldHaveBeforeUnload = blocker.enableBeforeUnload ?? true\n        if (shouldHaveBeforeUnload === true) {\n          shouldBlock = true\n          break\n        }\n\n        if (\n          typeof shouldHaveBeforeUnload === 'function' &&\n          shouldHaveBeforeUnload() === true\n        ) {\n          shouldBlock = true\n          break\n        }\n      }\n    }\n\n    if (shouldBlock) {\n      e.preventDefault()\n      return (e.returnValue = '')\n    }\n    return\n  }\n\n  const history = createHistory({\n    getLocation,\n    getLength: () => win.history.length,\n    pushState: (href, state) => queueHistoryAction('push', href, state),\n    replaceState: (href, state) => queueHistoryAction('replace', href, state),\n    back: (ignoreBlocker) => {\n      if (ignoreBlocker) skipBlockerNextPop = true\n      ignoreNextBeforeUnload = true\n      return win.history.back()\n    },\n    forward: (ignoreBlocker) => {\n      if (ignoreBlocker) skipBlockerNextPop = true\n      ignoreNextBeforeUnload = true\n      win.history.forward()\n    },\n    go: (n) => {\n      nextPopIsGo = true\n      win.history.go(n)\n    },\n    createHref: (href) => createHref(href),\n    flush,\n    destroy: () => {\n      win.history.pushState = originalPushState\n      win.history.replaceState = originalReplaceState\n      win.removeEventListener(beforeUnloadEvent, onBeforeUnload, {\n        capture: true,\n      })\n      win.removeEventListener(popStateEvent, onPushPopEvent)\n    },\n    onBlocked: () => {\n      // If a navigation is blocked, we need to rollback the location\n      // that we optimistically updated in memory.\n      if (rollbackLocation && currentLocation !== rollbackLocation) {\n        currentLocation = rollbackLocation\n      }\n    },\n    getBlockers: _getBlockers,\n    setBlockers: _setBlockers,\n    notifyOnIndexChange: false,\n  })\n\n  win.addEventListener(beforeUnloadEvent, onBeforeUnload, { capture: true })\n  win.addEventListener(popStateEvent, onPushPopEvent)\n\n  win.history.pushState = function (...args: Array<any>) {\n    const res = originalPushState.apply(win.history, args as any)\n    if (!history._ignoreSubscribers) onPushPop('PUSH')\n    return res\n  }\n\n  win.history.replaceState = function (...args: Array<any>) {\n    const res = originalReplaceState.apply(win.history, args as any)\n    if (!history._ignoreSubscribers) onPushPop('REPLACE')\n    return res\n  }\n\n  return history\n}\n\nexport function createHashHistory(opts?: { window?: any }): RouterHistory {\n  const win =\n    opts?.window ??\n    (typeof document !== 'undefined' ? window : (undefined as any))\n  return createBrowserHistory({\n    window: win,\n    parseLocation: () => {\n      const hashSplit = win.location.hash.split('#').slice(1)\n      const pathPart = hashSplit[0] ?? '/'\n      const searchPart = win.location.search\n      const hashEntries = hashSplit.slice(1)\n      const hashPart =\n        hashEntries.length === 0 ? '' : `#${hashEntries.join('#')}`\n      const hashHref = `${pathPart}${searchPart}${hashPart}`\n      return parseHref(hashHref, win.history.state)\n    },\n    createHref: (href) =>\n      `${win.location.pathname}${win.location.search}#${href}`,\n  })\n}\n\nexport function createMemoryHistory(\n  opts: {\n    initialEntries: Array<string>\n    initialIndex?: number\n  } = {\n    initialEntries: ['/'],\n  },\n): RouterHistory {\n  const entries = opts.initialEntries\n  let index = opts.initialIndex\n    ? Math.min(Math.max(opts.initialIndex, 0), entries.length - 1)\n    : entries.length - 1\n  const states = entries.map((_entry, index) =>\n    assignKeyAndIndex(index, undefined),\n  )\n\n  const getLocation = () => parseHref(entries[index]!, states[index])\n\n  return createHistory({\n    getLocation,\n    getLength: () => entries.length,\n    pushState: (path, state) => {\n      // Removes all subsequent entries after the current index to start a new branch\n      if (index < entries.length - 1) {\n        entries.splice(index + 1)\n        states.splice(index + 1)\n      }\n      states.push(state)\n      entries.push(path)\n      index = Math.max(entries.length - 1, 0)\n    },\n    replaceState: (path, state) => {\n      states[index] = state\n      entries[index] = path\n    },\n    back: () => {\n      index = Math.max(index - 1, 0)\n    },\n    forward: () => {\n      index = Math.min(index + 1, entries.length - 1)\n    },\n    go: (n) => {\n      index = Math.min(Math.max(index + n, 0), entries.length - 1)\n    },\n    createHref: (path) => path,\n  })\n}\n\nexport function parseHref(\n  href: string,\n  state: ParsedHistoryState | undefined,\n): HistoryLocation {\n  const hashIndex = href.indexOf('#')\n  const searchIndex = href.indexOf('?')\n\n  return {\n    href,\n    pathname: href.substring(\n      0,\n      hashIndex > 0\n        ? searchIndex > 0\n          ? Math.min(hashIndex, searchIndex)\n          : hashIndex\n        : searchIndex > 0\n          ? searchIndex\n          : href.length,\n    ),\n    hash: hashIndex > -1 ? href.substring(hashIndex) : '',\n    search:\n      searchIndex > -1\n        ? href.slice(searchIndex, hashIndex === -1 ? undefined : hashIndex)\n        : '',\n    state: state || { [stateIndexKey]: 0, key: createRandomKey() },\n  }\n}\n\n// Thanks co-pilot!\nfunction createRandomKey() {\n  return (Math.random() + 1).toString(36).substring(7)\n}\n", "var isProduction = process.env.NODE_ENV === 'production';\nvar prefix = 'Invariant failed';\nfunction invariant(condition, message) {\n    if (condition) {\n        return;\n    }\n    if (isProduction) {\n        throw new Error(prefix);\n    }\n    var provided = typeof message === 'function' ? message() : message;\n    var value = provided ? \"\".concat(prefix, \": \").concat(provided) : prefix;\n    throw new Error(value);\n}\n\nexport { invariant as default };\n", "import type { RouteIds } from './routeInfo'\nimport type { AnyRouter } from './router'\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\nexport type IsAny<TValue, TYesResult, TNoResult = TValue> = 1 extends 0 & TValue\n  ? TYesResult\n  : TNoResult\n\nexport type PickAsRequired<TValue, <PERSON><PERSON><PERSON> extends keyof TValue> = Omit<\n  TValue,\n  TKey\n> &\n  Required<Pick<TValue, TKey>>\n\nexport type PickRequired<T> = {\n  [K in keyof T as undefined extends T[K] ? never : K]: T[K]\n}\n\nexport type PickOptional<T> = {\n  [K in keyof T as undefined extends T[K] ? K : never]: T[K]\n}\n\n// from https://stackoverflow.com/a/76458160\nexport type WithoutEmpty<T> = T extends any ? ({} extends T ? never : T) : never\n\nexport type Expand<T> = T extends object\n  ? T extends infer O\n    ? O extends Function\n      ? O\n      : { [K in keyof O]: O[K] }\n    : never\n  : T\n\nexport type DeepPartial<T> = T extends object\n  ? {\n      [P in keyof T]?: DeepPartial<T[P]>\n    }\n  : T\n\nexport type MakeDifferenceOptional<TLeft, TRight> = keyof TLeft &\n  keyof TRight extends never\n  ? TRight\n  : Omit<TRight, keyof TLeft & keyof TRight> & {\n      [K in keyof TLeft & keyof TRight]?: TRight[K]\n    }\n\n// from https://stackoverflow.com/a/53955431\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport type IsUnion<T, U extends T = T> = (\n  T extends any ? (U extends T ? false : true) : never\n) extends false\n  ? false\n  : true\n\nexport type IsNonEmptyObject<T> = T extends object\n  ? keyof T extends never\n    ? false\n    : true\n  : false\n\nexport type Assign<TLeft, TRight> = TLeft extends any\n  ? TRight extends any\n    ? IsNonEmptyObject<TLeft> extends false\n      ? TRight\n      : IsNonEmptyObject<TRight> extends false\n        ? TLeft\n        : keyof TLeft & keyof TRight extends never\n          ? TLeft & TRight\n          : Omit<TLeft, keyof TRight> & TRight\n    : never\n  : never\n\nexport type IntersectAssign<TLeft, TRight> = TLeft extends any\n  ? TRight extends any\n    ? IsNonEmptyObject<TLeft> extends false\n      ? TRight\n      : IsNonEmptyObject<TRight> extends false\n        ? TLeft\n        : TRight & TLeft\n    : never\n  : never\n\nexport type Timeout = ReturnType<typeof setTimeout>\n\nexport type Updater<TPrevious, TResult = TPrevious> =\n  | TResult\n  | ((prev?: TPrevious) => TResult)\n\nexport type NonNullableUpdater<TPrevious, TResult = TPrevious> =\n  | TResult\n  | ((prev: TPrevious) => TResult)\n\nexport type ExtractObjects<TUnion> = TUnion extends MergeAllPrimitive\n  ? never\n  : TUnion\n\nexport type PartialMergeAllObject<TUnion> =\n  ExtractObjects<TUnion> extends infer TObj\n    ? [TObj] extends [never]\n      ? never\n      : {\n          [TKey in TObj extends any ? keyof TObj : never]?: TObj extends any\n            ? TKey extends keyof TObj\n              ? TObj[TKey]\n              : never\n            : never\n        }\n    : never\n\nexport type MergeAllPrimitive =\n  | ReadonlyArray<any>\n  | number\n  | string\n  | bigint\n  | boolean\n  | symbol\n  | undefined\n  | null\n\nexport type ExtractPrimitives<TUnion> = TUnion extends MergeAllPrimitive\n  ? TUnion\n  : TUnion extends object\n    ? never\n    : TUnion\n\nexport type PartialMergeAll<TUnion> =\n  | ExtractPrimitives<TUnion>\n  | PartialMergeAllObject<TUnion>\n\nexport type Constrain<T, TConstraint, TDefault = TConstraint> =\n  | (T extends TConstraint ? T : never)\n  | TDefault\n\nexport type ConstrainLiteral<T, TConstraint, TDefault = TConstraint> =\n  | (T & TConstraint)\n  | TDefault\n\n/**\n * To be added to router types\n */\nexport type UnionToIntersection<T> = (\n  T extends any ? (arg: T) => any : never\n) extends (arg: infer T) => any\n  ? T\n  : never\n\n/**\n * Merges everything in a union into one object.\n * This mapped type is homomorphic which means it preserves stuff! :)\n */\nexport type MergeAllObjects<\n  TUnion,\n  TIntersected = UnionToIntersection<ExtractObjects<TUnion>>,\n> = [keyof TIntersected] extends [never]\n  ? never\n  : {\n      [TKey in keyof TIntersected]: TUnion extends any\n        ? TUnion[TKey & keyof TUnion]\n        : never\n    }\n\nexport type MergeAll<TUnion> =\n  | MergeAllObjects<TUnion>\n  | ExtractPrimitives<TUnion>\n\nexport type ValidateJSON<T> = ((...args: Array<any>) => any) extends T\n  ? unknown extends T\n    ? never\n    : 'Function is not serializable'\n  : { [K in keyof T]: ValidateJSON<T[K]> }\n\nexport function last<T>(arr: Array<T>) {\n  return arr[arr.length - 1]\n}\n\nfunction isFunction(d: any): d is Function {\n  return typeof d === 'function'\n}\n\nexport function functionalUpdate<TPrevious, TResult = TPrevious>(\n  updater: Updater<TPrevious, TResult> | NonNullableUpdater<TPrevious, TResult>,\n  previous: TPrevious,\n): TResult {\n  if (isFunction(updater)) {\n    return updater(previous)\n  }\n\n  return updater\n}\n\nexport function pick<TValue, TKey extends keyof TValue>(\n  parent: TValue,\n  keys: Array<TKey>,\n): Pick<TValue, TKey> {\n  return keys.reduce((obj: any, key: TKey) => {\n    obj[key] = parent[key]\n    return obj\n  }, {} as any)\n}\n\n/**\n * This function returns `prev` if `_next` is deeply equal.\n * If not, it will replace any deeply equal children of `b` with those of `a`.\n * This can be used for structural sharing between immutable JSON values for example.\n * Do not use this with signals\n */\nexport function replaceEqualDeep<T>(prev: any, _next: T): T {\n  if (prev === _next) {\n    return prev\n  }\n\n  const next = _next as any\n\n  const array = isPlainArray(prev) && isPlainArray(next)\n\n  if (array || (isSimplePlainObject(prev) && isSimplePlainObject(next))) {\n    const prevItems = array\n      ? prev\n      : (Object.keys(prev) as Array<unknown>).concat(\n          Object.getOwnPropertySymbols(prev),\n        )\n    const prevSize = prevItems.length\n    const nextItems = array\n      ? next\n      : (Object.keys(next) as Array<unknown>).concat(\n          Object.getOwnPropertySymbols(next),\n        )\n    const nextSize = nextItems.length\n    const copy: any = array ? [] : {}\n\n    let equalItems = 0\n\n    for (let i = 0; i < nextSize; i++) {\n      const key = array ? i : (nextItems[i] as any)\n      if (\n        ((!array && prevItems.includes(key)) || array) &&\n        prev[key] === undefined &&\n        next[key] === undefined\n      ) {\n        copy[key] = undefined\n        equalItems++\n      } else {\n        copy[key] = replaceEqualDeep(prev[key], next[key])\n        if (copy[key] === prev[key] && prev[key] !== undefined) {\n          equalItems++\n        }\n      }\n    }\n\n    return prevSize === nextSize && equalItems === prevSize ? prev : copy\n  }\n\n  return next\n}\n\n/**\n * A wrapper around `isPlainObject` with additional checks to ensure that it is not\n * only a plain object, but also one that is \"clone-friendly\" (doesn't have any\n * non-enumerable properties).\n */\nfunction isSimplePlainObject(o: any) {\n  return (\n    // all the checks from isPlainObject are more likely to hit so we perform them first\n    isPlainObject(o) &&\n    Object.getOwnPropertyNames(o).length === Object.keys(o).length\n  )\n}\n\n// Copied from: https://github.com/jonschlinkert/is-plain-object\nexport function isPlainObject(o: any) {\n  if (!hasObjectPrototype(o)) {\n    return false\n  }\n\n  // If has modified constructor\n  const ctor = o.constructor\n  if (typeof ctor === 'undefined') {\n    return true\n  }\n\n  // If has modified prototype\n  const prot = ctor.prototype\n  if (!hasObjectPrototype(prot)) {\n    return false\n  }\n\n  // If constructor does not have an Object-specific method\n  if (!prot.hasOwnProperty('isPrototypeOf')) {\n    return false\n  }\n\n  // Most likely a plain Object\n  return true\n}\n\nfunction hasObjectPrototype(o: any) {\n  return Object.prototype.toString.call(o) === '[object Object]'\n}\n\nexport function isPlainArray(value: unknown): value is Array<unknown> {\n  return Array.isArray(value) && value.length === Object.keys(value).length\n}\n\nfunction getObjectKeys(obj: any, ignoreUndefined: boolean) {\n  let keys = Object.keys(obj)\n  if (ignoreUndefined) {\n    keys = keys.filter((key) => obj[key] !== undefined)\n  }\n  return keys\n}\n\nexport function deepEqual(\n  a: any,\n  b: any,\n  opts?: { partial?: boolean; ignoreUndefined?: boolean },\n): boolean {\n  if (a === b) {\n    return true\n  }\n\n  if (typeof a !== typeof b) {\n    return false\n  }\n\n  if (isPlainObject(a) && isPlainObject(b)) {\n    const ignoreUndefined = opts?.ignoreUndefined ?? true\n    const aKeys = getObjectKeys(a, ignoreUndefined)\n    const bKeys = getObjectKeys(b, ignoreUndefined)\n\n    if (!opts?.partial && aKeys.length !== bKeys.length) {\n      return false\n    }\n\n    return bKeys.every((key) => deepEqual(a[key], b[key], opts))\n  }\n\n  if (Array.isArray(a) && Array.isArray(b)) {\n    if (a.length !== b.length) {\n      return false\n    }\n    return !a.some((item, index) => !deepEqual(item, b[index], opts))\n  }\n\n  return false\n}\n\nexport type StringLiteral<T> = T extends string\n  ? string extends T\n    ? string\n    : T\n  : never\n\nexport type ThrowOrOptional<T, TThrow extends boolean> = TThrow extends true\n  ? T\n  : T | undefined\n\nexport type StrictOrFrom<\n  TRouter extends AnyRouter,\n  TFrom,\n  TStrict extends boolean = true,\n> = TStrict extends false\n  ? {\n      from?: never\n      strict: TStrict\n    }\n  : {\n      from: ConstrainLiteral<TFrom, RouteIds<TRouter['routeTree']>>\n      strict?: TStrict\n    }\n\nexport type ThrowConstraint<\n  TStrict extends boolean,\n  TThrow extends boolean,\n> = TStrict extends false ? (TThrow extends true ? never : TThrow) : TThrow\n\nexport type ControlledPromise<T> = Promise<T> & {\n  resolve: (value: T) => void\n  reject: (value: any) => void\n  status: 'pending' | 'resolved' | 'rejected'\n  value?: T\n}\n\nexport function createControlledPromise<T>(onResolve?: (value: T) => void) {\n  let resolveLoadPromise!: (value: T) => void\n  let rejectLoadPromise!: (value: any) => void\n\n  const controlledPromise = new Promise<T>((resolve, reject) => {\n    resolveLoadPromise = resolve\n    rejectLoadPromise = reject\n  }) as ControlledPromise<T>\n\n  controlledPromise.status = 'pending'\n\n  controlledPromise.resolve = (value: T) => {\n    controlledPromise.status = 'resolved'\n    controlledPromise.value = value\n    resolveLoadPromise(value)\n    onResolve?.(value)\n  }\n\n  controlledPromise.reject = (e) => {\n    controlledPromise.status = 'rejected'\n    rejectLoadPromise(e)\n  }\n\n  return controlledPromise\n}\n\n/**\n *\n * @deprecated use `jsesc` instead\n */\nexport function escapeJSON(jsonString: string) {\n  return jsonString\n    .replace(/\\\\/g, '\\\\\\\\') // Escape backslashes\n    .replace(/'/g, \"\\\\'\") // Escape single quotes\n    .replace(/\"/g, '\\\\\"') // Escape double quotes\n}\n\nexport function shallow<T>(objA: T, objB: T) {\n  if (Object.is(objA, objB)) {\n    return true\n  }\n\n  if (\n    typeof objA !== 'object' ||\n    objA === null ||\n    typeof objB !== 'object' ||\n    objB === null\n  ) {\n    return false\n  }\n\n  const keysA = Object.keys(objA)\n  if (keysA.length !== Object.keys(objB).length) {\n    return false\n  }\n\n  for (const item of keysA) {\n    if (\n      !Object.prototype.hasOwnProperty.call(objB, item) ||\n      !Object.is(objA[item as keyof T], objB[item as keyof T])\n    ) {\n      return false\n    }\n  }\n  return true\n}\n\n/**\n * Checks if a string contains URI-encoded special characters (e.g., %3F, %20).\n *\n * @param {string} inputString The string to check.\n * @returns {boolean} True if the string contains URI-encoded characters, false otherwise.\n * @example\n * ```typescript\n * const str1 = \"foo%3Fbar\";\n * const hasEncodedChars = hasUriEncodedChars(str1); // returns true\n * ```\n */\nexport function hasUriEncodedChars(inputString: string): boolean {\n  // This regex looks for a percent sign followed by two hexadecimal digits\n  const pattern = /%[0-9A-Fa-f]{2}/\n  return pattern.test(inputString)\n}\n", "import { last } from './utils'\nimport type { MatchLocation } from './RouterProvider'\nimport type { AnyPathParams } from './route'\n\nexport interface Segment {\n  type: 'pathname' | 'param' | 'wildcard'\n  value: string\n}\n\nexport function joinPaths(paths: Array<string | undefined>) {\n  return cleanPath(\n    paths\n      .filter((val) => {\n        return val !== undefined\n      })\n      .join('/'),\n  )\n}\n\nexport function cleanPath(path: string) {\n  // remove double slashes\n  return path.replace(/\\/{2,}/g, '/')\n}\n\nexport function trimPathLeft(path: string) {\n  return path === '/' ? path : path.replace(/^\\/{1,}/, '')\n}\n\nexport function trimPathRight(path: string) {\n  return path === '/' ? path : path.replace(/\\/{1,}$/, '')\n}\n\nexport function trimPath(path: string) {\n  return trimPathRight(trimPathLeft(path))\n}\n\nexport function removeTrailingSlash(value: string, basepath: string): string {\n  if (value?.endsWith('/') && value !== '/' && value !== `${basepath}/`) {\n    return value.slice(0, -1)\n  }\n  return value\n}\n\n// intended to only compare path name\n// see the usage in the isActive under useLinkProps\n// /sample/path1 = /sample/path1/\n// /sample/path1/some <> /sample/path1\nexport function exactPathTest(\n  pathName1: string,\n  pathName2: string,\n  basepath: string,\n): boolean {\n  return (\n    removeTrailingSlash(pathName1, basepath) ===\n    removeTrailingSlash(pathName2, basepath)\n  )\n}\n\n// When resolving relative paths, we treat all paths as if they are trailing slash\n// documents. All trailing slashes are removed after the path is resolved.\n// Here are a few examples:\n//\n// /a/b/c + ./d = /a/b/c/d\n// /a/b/c + ../d = /a/b/d\n// /a/b/c + ./d/ = /a/b/c/d\n// /a/b/c + ../d/ = /a/b/d\n// /a/b/c + ./ = /a/b/c\n//\n// Absolute paths that start with `/` short circuit the resolution process to the root\n// path.\n//\n// Here are some examples:\n//\n// /a/b/c + /d = /d\n// /a/b/c + /d/ = /d\n// /a/b/c + / = /\n//\n// Non-.-prefixed paths are still treated as relative paths, resolved like `./`\n//\n// Here are some examples:\n//\n// /a/b/c + d = /a/b/c/d\n// /a/b/c + d/ = /a/b/c/d\n// /a/b/c + d/e = /a/b/c/d/e\ninterface ResolvePathOptions {\n  basepath: string\n  base: string\n  to: string\n  trailingSlash?: 'always' | 'never' | 'preserve'\n  caseSensitive?: boolean\n}\n\nexport function resolvePath({\n  basepath,\n  base,\n  to,\n  trailingSlash = 'never',\n  caseSensitive,\n}: ResolvePathOptions) {\n  base = removeBasepath(basepath, base, caseSensitive)\n  to = removeBasepath(basepath, to, caseSensitive)\n\n  let baseSegments = parsePathname(base)\n  const toSegments = parsePathname(to)\n\n  if (baseSegments.length > 1 && last(baseSegments)?.value === '/') {\n    baseSegments.pop()\n  }\n\n  toSegments.forEach((toSegment, index) => {\n    if (toSegment.value === '/') {\n      if (!index) {\n        // Leading slash\n        baseSegments = [toSegment]\n      } else if (index === toSegments.length - 1) {\n        // Trailing Slash\n        baseSegments.push(toSegment)\n      } else {\n        // ignore inter-slashes\n      }\n    } else if (toSegment.value === '..') {\n      baseSegments.pop()\n    } else if (toSegment.value === '.') {\n      // ignore\n    } else {\n      baseSegments.push(toSegment)\n    }\n  })\n\n  if (baseSegments.length > 1) {\n    if (last(baseSegments)?.value === '/') {\n      if (trailingSlash === 'never') {\n        baseSegments.pop()\n      }\n    } else if (trailingSlash === 'always') {\n      baseSegments.push({ type: 'pathname', value: '/' })\n    }\n  }\n\n  const joined = joinPaths([basepath, ...baseSegments.map((d) => d.value)])\n  return cleanPath(joined)\n}\n\nexport function parsePathname(pathname?: string): Array<Segment> {\n  if (!pathname) {\n    return []\n  }\n\n  pathname = cleanPath(pathname)\n\n  const segments: Array<Segment> = []\n\n  if (pathname.slice(0, 1) === '/') {\n    pathname = pathname.substring(1)\n    segments.push({\n      type: 'pathname',\n      value: '/',\n    })\n  }\n\n  if (!pathname) {\n    return segments\n  }\n\n  // Remove empty segments and '.' segments\n  const split = pathname.split('/').filter(Boolean)\n\n  segments.push(\n    ...split.map((part): Segment => {\n      if (part === '$' || part === '*') {\n        return {\n          type: 'wildcard',\n          value: part,\n        }\n      }\n\n      if (part.charAt(0) === '$') {\n        return {\n          type: 'param',\n          value: part,\n        }\n      }\n\n      return {\n        type: 'pathname',\n        value: part.includes('%25')\n          ? part\n              .split('%25')\n              .map((segment) => decodeURI(segment))\n              .join('%25')\n          : decodeURI(part),\n      }\n    }),\n  )\n\n  if (pathname.slice(-1) === '/') {\n    pathname = pathname.substring(1)\n    segments.push({\n      type: 'pathname',\n      value: '/',\n    })\n  }\n\n  return segments\n}\n\ninterface InterpolatePathOptions {\n  path?: string\n  params: Record<string, unknown>\n  leaveWildcards?: boolean\n  leaveParams?: boolean\n  // Map of encoded chars to decoded chars (e.g. '%40' -> '@') that should remain decoded in path params\n  decodeCharMap?: Map<string, string>\n}\n\ntype InterPolatePathResult = {\n  interpolatedPath: string\n  usedParams: Record<string, unknown>\n  isMissingParams: boolean // true if any params were not available when being looked up in the params object\n}\nexport function interpolatePath({\n  path,\n  params,\n  leaveWildcards,\n  leaveParams,\n  decodeCharMap,\n}: InterpolatePathOptions): InterPolatePathResult {\n  const interpolatedPathSegments = parsePathname(path)\n\n  function encodeParam(key: string): any {\n    const value = params[key]\n    const isValueString = typeof value === 'string'\n\n    if (['*', '_splat'].includes(key)) {\n      // the splat/catch-all routes shouldn't have the '/' encoded out\n      return isValueString ? encodeURI(value) : value\n    } else {\n      return isValueString ? encodePathParam(value, decodeCharMap) : value\n    }\n  }\n\n  // Tracking if any params are missing in the `params` object\n  // when interpolating the path\n  let isMissingParams = false\n\n  const usedParams: Record<string, unknown> = {}\n  const interpolatedPath = joinPaths(\n    interpolatedPathSegments.map((segment) => {\n      if (segment.type === 'wildcard') {\n        usedParams._splat = params._splat\n        const value = encodeParam('_splat')\n        if (leaveWildcards) return `${segment.value}${value ?? ''}`\n        return value\n      }\n\n      if (segment.type === 'param') {\n        const key = segment.value.substring(1)\n        if (!isMissingParams && !(key in params)) {\n          isMissingParams = true\n        }\n        usedParams[key] = params[key]\n        if (leaveParams) {\n          const value = encodeParam(segment.value)\n          return `${segment.value}${value ?? ''}`\n        }\n        return encodeParam(key) ?? 'undefined'\n      }\n\n      return segment.value\n    }),\n  )\n  return { usedParams, interpolatedPath, isMissingParams }\n}\n\nfunction encodePathParam(value: string, decodeCharMap?: Map<string, string>) {\n  let encoded = encodeURIComponent(value)\n  if (decodeCharMap) {\n    for (const [encodedChar, char] of decodeCharMap) {\n      encoded = encoded.replaceAll(encodedChar, char)\n    }\n  }\n  return encoded\n}\n\nexport function matchPathname(\n  basepath: string,\n  currentPathname: string,\n  matchLocation: Pick<MatchLocation, 'to' | 'fuzzy' | 'caseSensitive'>,\n): AnyPathParams | undefined {\n  const pathParams = matchByPath(basepath, currentPathname, matchLocation)\n  // const searchMatched = matchBySearch(location.search, matchLocation)\n\n  if (matchLocation.to && !pathParams) {\n    return\n  }\n\n  return pathParams ?? {}\n}\n\nexport function removeBasepath(\n  basepath: string,\n  pathname: string,\n  caseSensitive: boolean = false,\n) {\n  // normalize basepath and pathname for case-insensitive comparison if needed\n  const normalizedBasepath = caseSensitive ? basepath : basepath.toLowerCase()\n  const normalizedPathname = caseSensitive ? pathname : pathname.toLowerCase()\n\n  switch (true) {\n    // default behaviour is to serve app from the root - pathname\n    // left untouched\n    case normalizedBasepath === '/':\n      return pathname\n\n    // shortcut for removing the basepath if it matches the pathname\n    case normalizedPathname === normalizedBasepath:\n      return ''\n\n    // in case pathname is shorter than basepath - there is\n    // nothing to remove\n    case pathname.length < basepath.length:\n      return pathname\n\n    // avoid matching partial segments - strict equality handled\n    // earlier, otherwise, basepath separated from pathname with\n    // separator, therefore lack of separator means partial\n    // segment match (`/app` should not match `/application`)\n    case normalizedPathname[normalizedBasepath.length] !== '/':\n      return pathname\n\n    // remove the basepath from the pathname if it starts with it\n    case normalizedPathname.startsWith(normalizedBasepath):\n      return pathname.slice(basepath.length)\n\n    // otherwise, return the pathname as is\n    default:\n      return pathname\n  }\n}\n\nexport function matchByPath(\n  basepath: string,\n  from: string,\n  matchLocation: Pick<MatchLocation, 'to' | 'caseSensitive' | 'fuzzy'>,\n): Record<string, string> | undefined {\n  // check basepath first\n  if (basepath !== '/' && !from.startsWith(basepath)) {\n    return undefined\n  }\n  // Remove the base path from the pathname\n  from = removeBasepath(basepath, from, matchLocation.caseSensitive)\n  // Default to to $ (wildcard)\n  const to = removeBasepath(\n    basepath,\n    `${matchLocation.to ?? '$'}`,\n    matchLocation.caseSensitive,\n  )\n\n  // Parse the from and to\n  const baseSegments = parsePathname(from)\n  const routeSegments = parsePathname(to)\n\n  if (!from.startsWith('/')) {\n    baseSegments.unshift({\n      type: 'pathname',\n      value: '/',\n    })\n  }\n\n  if (!to.startsWith('/')) {\n    routeSegments.unshift({\n      type: 'pathname',\n      value: '/',\n    })\n  }\n\n  const params: Record<string, string> = {}\n\n  const isMatch = (() => {\n    for (\n      let i = 0;\n      i < Math.max(baseSegments.length, routeSegments.length);\n      i++\n    ) {\n      const baseSegment = baseSegments[i]\n      const routeSegment = routeSegments[i]\n\n      const isLastBaseSegment = i >= baseSegments.length - 1\n      const isLastRouteSegment = i >= routeSegments.length - 1\n\n      if (routeSegment) {\n        if (routeSegment.type === 'wildcard') {\n          const _splat = decodeURI(\n            joinPaths(baseSegments.slice(i).map((d) => d.value)),\n          )\n          // TODO: Deprecate *\n          params['*'] = _splat\n          params['_splat'] = _splat\n          return true\n        }\n\n        if (routeSegment.type === 'pathname') {\n          if (routeSegment.value === '/' && !baseSegment?.value) {\n            return true\n          }\n\n          if (baseSegment) {\n            if (matchLocation.caseSensitive) {\n              if (routeSegment.value !== baseSegment.value) {\n                return false\n              }\n            } else if (\n              routeSegment.value.toLowerCase() !==\n              baseSegment.value.toLowerCase()\n            ) {\n              return false\n            }\n          }\n        }\n\n        if (!baseSegment) {\n          return false\n        }\n\n        if (routeSegment.type === 'param') {\n          if (baseSegment.value === '/') {\n            return false\n          }\n          if (baseSegment.value.charAt(0) !== '$') {\n            params[routeSegment.value.substring(1)] = decodeURIComponent(\n              baseSegment.value,\n            )\n          }\n        }\n      }\n\n      if (!isLastBaseSegment && isLastRouteSegment) {\n        params['**'] = joinPaths(baseSegments.slice(i + 1).map((d) => d.value))\n        return !!matchLocation.fuzzy && routeSegment?.value !== '/'\n      }\n    }\n\n    return true\n  })()\n\n  return isMatch ? params : undefined\n}\n", "import type { RouteIds } from './routeInfo'\nimport type { RegisteredRouter } from './router'\n\nexport type NotFoundError = {\n  /**\n    @deprecated\n    Use `routeId: rootRouteId` instead\n  */\n  global?: boolean\n  /**\n    @private\n    Do not use this. It's used internally to indicate a path matching error\n  */\n  _global?: boolean\n  data?: any\n  throw?: boolean\n  routeId?: RouteIds<RegisteredRouter['routeTree']>\n  headers?: HeadersInit\n}\n\nexport function notFound(options: NotFoundError = {}) {\n  ;(options as any).isNotFound = true\n  if (options.throw) throw options\n  return options\n}\n\nexport function isNotFound(obj: any): obj is NotFoundError {\n  return !!obj?.isNotFound\n}\n", "/**\n * Program is a reimplementation of the `qss` package:\n * Copyright (c) <PERSON> <EMAIL>, MIT License\n * https://github.com/lukeed/qss/blob/master/license.md\n *\n * This reimplementation uses modern browser APIs\n * (namely URLSearchParams) and TypeScript while still\n * maintaining the original functionality and interface.\n */\nimport { hasUriEncodedChars } from './utils'\n\n/**\n * Encodes an object into a query string.\n * @param obj - The object to encode into a query string.\n * @param [pfx] - An optional prefix to add before the query string.\n * @returns The encoded query string.\n * @example\n * ```\n * // Example input: encode({ token: 'foo', key: 'value' })\n * // Expected output: \"token=foo&key=value\"\n * ```\n */\nexport function encode(obj: any, pfx?: string) {\n  const normalizedObject = Object.entries(obj).flatMap(([key, value]) => {\n    if (Array.isArray(value)) {\n      return value.map((v) => [key, String(v)])\n    } else {\n      return [[key, String(value)]]\n    }\n  })\n\n  const searchParams = new URLSearchParams(normalizedObject)\n\n  return (pfx || '') + searchParams.toString()\n}\n\n/**\n * Converts a string value to its appropriate type (string, number, boolean).\n * @param mix - The string value to convert.\n * @returns The converted value.\n * @example\n * // Example input: toValue(\"123\")\n * // Expected output: 123\n */\nfunction toValue(mix: any) {\n  if (!mix) return ''\n  const str = hasUriEncodedChars(mix)\n    ? decodeURIComponent(mix)\n    : decodeURIComponent(encodeURIComponent(mix))\n\n  if (str === 'false') return false\n  if (str === 'true') return true\n  return +str * 0 === 0 && +str + '' === str ? +str : str\n}\n\n/**\n * Decodes a query string into an object.\n * @param str - The query string to decode.\n * @param [pfx] - An optional prefix to filter out from the query string.\n * @returns The decoded key-value pairs in an object format.\n * @example\n * // Example input: decode(\"token=foo&key=value\")\n * // Expected output: { \"token\": \"foo\", \"key\": \"value\" }\n */\nexport function decode(str: any, pfx?: string): any {\n  const searchParamsPart = pfx ? str.slice(pfx.length) : str\n  const searchParams = new URLSearchParams(searchParamsPart)\n\n  const entries = [...searchParams.entries()]\n\n  return entries.reduce<Record<string, unknown>>((acc, [key, value]) => {\n    const previousValue = acc[key]\n    if (previousValue == null) {\n      acc[key] = toValue(value)\n    } else {\n      acc[key] = Array.isArray(previousValue)\n        ? [...previousValue, toValue(value)]\n        : [previousValue, toValue(value)]\n    }\n\n    return acc\n  }, {})\n}\n", "import { decode, encode } from './qss'\nimport type { AnySchema } from './validators'\n\nexport const defaultParseSearch = parseSearchWith(JSON.parse)\nexport const defaultStringifySearch = stringifySearchWith(\n  JSON.stringify,\n  JSON.parse,\n)\n\nexport function parseSearchWith(parser: (str: string) => any) {\n  return (searchStr: string): AnySchema => {\n    if (searchStr.substring(0, 1) === '?') {\n      searchStr = searchStr.substring(1)\n    }\n\n    const query: Record<string, unknown> = decode(searchStr)\n\n    // Try to parse any query params that might be json\n    for (const key in query) {\n      const value = query[key]\n      if (typeof value === 'string') {\n        try {\n          query[key] = parser(value)\n        } catch (err) {\n          //\n        }\n      }\n    }\n\n    return query\n  }\n}\n\nexport function stringifySearchWith(\n  stringify: (search: any) => string,\n  parser?: (str: string) => any,\n) {\n  function stringifyValue(val: any) {\n    if (typeof val === 'object' && val !== null) {\n      try {\n        return stringify(val)\n      } catch (err) {\n        // silent\n      }\n    } else if (typeof val === 'string' && typeof parser === 'function') {\n      try {\n        // Check if it's a valid parseable string.\n        // If it is, then stringify it again.\n        parser(val)\n        return stringify(val)\n      } catch (err) {\n        // silent\n      }\n    }\n    return val\n  }\n\n  return (search: Record<string, any>) => {\n    search = { ...search }\n\n    Object.keys(search).forEach((key) => {\n      const val = search[key]\n      if (typeof val === 'undefined' || val === undefined) {\n        delete search[key]\n      } else {\n        search[key] = stringifyValue(val)\n      }\n    })\n\n    const searchStr = encode(search as Record<string, string>).toString()\n\n    return searchStr ? `?${searchStr}` : ''\n  }\n}\n\nexport type SearchSerializer = (searchObj: Record<string, any>) => string\nexport type SearchParser = (searchStr: string) => Record<string, any>\n", "export const rootRouteId = '__root__'\nexport type RootRouteId = typeof rootRouteId\n", "import type { NavigateOptions } from './link'\nimport type { <PERSON><PERSON>out<PERSON>, RegisteredRouter } from './router'\nimport type { PickAsRequired } from './utils'\n\nexport type AnyRedirect = Redirect<any, any, any, any, any>\n\n/**\n * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RedirectType)\n */\nexport type Redirect<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = undefined,\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = {\n  href?: string\n  /**\n   * @deprecated Use `statusCode` instead\n   **/\n  code?: number\n  /**\n   * The HTTP status code to use when redirecting.\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RedirectType#statuscode-property)\n   */\n  statusCode?: number\n  /**\n   * If provided, will throw the redirect object instead of returning it. This can be useful in places where `throwing` in a function might cause it to have a return type of `never`. In that case, you can use `redirect({ throw: true })` to throw the redirect object instead of returning it.\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RedirectType#throw-property)\n   */\n  throw?: any\n  /**\n   * The HTTP headers to use when redirecting.\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RedirectType#headers-property)\n   */\n  headers?: HeadersInit\n} & NavigateOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>\n\nexport type ResolvedRedirect<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string = '',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '',\n> = PickAsRequired<\n  Redirect<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>,\n  'code' | 'statusCode' | 'headers'\n> & {\n  href: string\n}\n\nexport function redirect<\n  TRouter extends AnyRouter = RegisteredRouter,\n  const TTo extends string | undefined = '.',\n  const TFrom extends string = string,\n  const TMaskFrom extends string = TFrom,\n  const TMaskTo extends string = '',\n>(\n  opts: Redirect<TRouter, TFrom, TTo, TMaskFrom, TMaskTo>,\n): Redirect<TRouter, TFrom, TTo, TMaskFrom, TMaskTo> {\n  ;(opts as any).isRedirect = true\n  opts.statusCode = opts.statusCode || opts.code || 307\n  opts.headers = opts.headers || {}\n  if (!opts.reloadDocument) {\n    opts.reloadDocument = false\n    try {\n      new URL(`${opts.href}`)\n      opts.reloadDocument = true\n    } catch {}\n  }\n\n  if (opts.throw) {\n    throw opts\n  }\n\n  return opts\n}\n\nexport function isRedirect(obj: any): obj is AnyRedirect {\n  return !!obj?.isRedirect\n}\n\nexport function isResolvedRedirect(obj: any): obj is ResolvedRedirect {\n  return !!obj?.isRedirect && obj.href\n}\n", "import { Derived } from './derived'\nimport type { Store } from './store'\n\n/**\n * This is here to solve the pyramid dependency problem where:\n *       A\n *      / \\\n *     B   C\n *      \\ /\n *       D\n *\n * Where we deeply traverse this tree, how do we avoid D being recomputed twice; once when B is updated, once when C is.\n *\n * To solve this, we create linkedDeps that allows us to sync avoid writes to the state until all of the deps have been\n * resolved.\n *\n * This is a record of stores, because derived stores are not able to write values to, but stores are\n */\nexport const __storeToDerived = new WeakMap<\n  Store<unknown>,\n  Set<Derived<unknown>>\n>()\nexport const __derivedToStore = new WeakMap<\n  Derived<unknown>,\n  Set<Store<unknown>>\n>()\n\nexport const __depsThatHaveWrittenThisTick = {\n  current: [] as Array<Derived<unknown> | Store<unknown>>,\n}\n\nlet __isFlushing = false\nlet __batchDepth = 0\nconst __pendingUpdates = new Set<Store<unknown>>()\n// Add a map to store initial values before batch\nconst __initialBatchValues = new Map<Store<unknown>, unknown>()\n\nfunction __flush_internals(relatedVals: Set<Derived<unknown>>) {\n  // First sort deriveds by dependency order\n  const sorted = Array.from(relatedVals).sort((a, b) => {\n    // If a depends on b, b should go first\n    if (a instanceof Derived && a.options.deps.includes(b)) return 1\n    // If b depends on a, a should go first\n    if (b instanceof Derived && b.options.deps.includes(a)) return -1\n    return 0\n  })\n\n  for (const derived of sorted) {\n    if (__depsThatHaveWrittenThisTick.current.includes(derived)) {\n      continue\n    }\n\n    __depsThatHaveWrittenThisTick.current.push(derived)\n    derived.recompute()\n\n    const stores = __derivedToStore.get(derived)\n    if (stores) {\n      for (const store of stores) {\n        const relatedLinkedDerivedVals = __storeToDerived.get(store)\n        if (!relatedLinkedDerivedVals) continue\n        __flush_internals(relatedLinkedDerivedVals)\n      }\n    }\n  }\n}\n\nfunction __notifyListeners(store: Store<unknown>) {\n  store.listeners.forEach((listener) =>\n    listener({\n      prevVal: store.prevState as never,\n      currentVal: store.state as never,\n    }),\n  )\n}\n\nfunction __notifyDerivedListeners(derived: Derived<unknown>) {\n  derived.listeners.forEach((listener) =>\n    listener({\n      prevVal: derived.prevState as never,\n      currentVal: derived.state as never,\n    }),\n  )\n}\n\n/**\n * @private only to be called from `Store` on write\n */\nexport function __flush(store: Store<unknown>) {\n  // If we're starting a batch, store the initial values\n  if (__batchDepth > 0 && !__initialBatchValues.has(store)) {\n    __initialBatchValues.set(store, store.prevState)\n  }\n\n  __pendingUpdates.add(store)\n\n  if (__batchDepth > 0) return\n  if (__isFlushing) return\n\n  try {\n    __isFlushing = true\n\n    while (__pendingUpdates.size > 0) {\n      const stores = Array.from(__pendingUpdates)\n      __pendingUpdates.clear()\n\n      // First notify listeners with updated values\n      for (const store of stores) {\n        // Use initial batch values for prevState if we have them\n        const prevState = __initialBatchValues.get(store) ?? store.prevState\n        store.prevState = prevState\n        __notifyListeners(store)\n      }\n\n      // Then update all derived values\n      for (const store of stores) {\n        const derivedVals = __storeToDerived.get(store)\n        if (!derivedVals) continue\n\n        __depsThatHaveWrittenThisTick.current.push(store)\n        __flush_internals(derivedVals)\n      }\n\n      // Notify derived listeners after recomputing\n      for (const store of stores) {\n        const derivedVals = __storeToDerived.get(store)\n        if (!derivedVals) continue\n\n        for (const derived of derivedVals) {\n          __notifyDerivedListeners(derived)\n        }\n      }\n    }\n  } finally {\n    __isFlushing = false\n    __depsThatHaveWrittenThisTick.current = []\n    __initialBatchValues.clear()\n  }\n}\n\nexport function batch(fn: () => void) {\n  __batchDepth++\n  try {\n    fn()\n  } finally {\n    __batchDepth--\n    if (__batchDepth === 0) {\n      const pendingUpdateToFlush = Array.from(__pendingUpdates)[0] as\n        | Store<unknown>\n        | undefined\n      if (pendingUpdateToFlush) {\n        __flush(pendingUpdateToFlush) // Trigger flush of all pending updates\n      }\n    }\n  }\n}\n", "import { __flush } from './scheduler'\nimport type { AnyUpdater, Listener } from './types'\n\nexport interface StoreOptions<\n  TState,\n  TUpdater extends AnyUpdater = (cb: TState) => TState,\n> {\n  /**\n   * Replace the default update function with a custom one.\n   */\n  updateFn?: (previous: TState) => (updater: TUpdater) => TState\n  /**\n   * Called when a listener subscribes to the store.\n   *\n   * @return a function to unsubscribe the listener\n   */\n  onSubscribe?: (\n    listener: Listener<TState>,\n    store: Store<TState, TUpdater>,\n  ) => () => void\n  /**\n   * Called after the state has been updated, used to derive other state.\n   */\n  onUpdate?: () => void\n}\n\nexport class Store<\n  TState,\n  TUpdater extends AnyUpdater = (cb: TState) => TState,\n> {\n  listeners = new Set<Listener<TState>>()\n  state: TState\n  prevState: TState\n  options?: StoreOptions<TState, TUpdater>\n\n  constructor(initialState: TState, options?: StoreOptions<TState, TUpdater>) {\n    this.prevState = initialState\n    this.state = initialState\n    this.options = options\n  }\n\n  subscribe = (listener: Listener<TState>) => {\n    this.listeners.add(listener)\n    const unsub = this.options?.onSubscribe?.(listener, this)\n    return () => {\n      this.listeners.delete(listener)\n      unsub?.()\n    }\n  }\n\n  setState = (updater: TUpdater) => {\n    this.prevState = this.state\n    this.state = this.options?.updateFn\n      ? this.options.updateFn(this.prevState)(updater)\n      : (updater as any)(this.prevState)\n\n    // Always run onUpdate, regardless of batching\n    this.options?.onUpdate?.()\n\n    // Attempt to flush\n    __flush(this as never)\n  }\n}\n", "import { Store } from './store'\nimport { __derivedToStore, __storeToDerived } from './scheduler'\nimport type { Listener } from './types'\n\nexport type UnwrapDerivedOrStore<T> =\n  T extends Derived<infer InnerD>\n    ? InnerD\n    : T extends Store<infer InnerS>\n      ? InnerS\n      : never\n\ntype UnwrapReadonlyDerivedOrStoreArray<\n  TArr extends ReadonlyArray<Derived<any> | Store<any>>,\n> = TArr extends readonly [infer Head, ...infer Tail]\n  ? Head extends Derived<any> | Store<any>\n    ? Tail extends ReadonlyArray<Derived<any> | Store<any>>\n      ? [UnwrapDerivedOrStore<Head>, ...UnwrapReadonlyDerivedOrStoreArray<Tail>]\n      : []\n    : []\n  : []\n\n// Can't have currVal, as it's being evaluated from the current derived fn\nexport interface DerivedFnProps<\n  TArr extends ReadonlyArray<Derived<any> | Store<any>> = ReadonlyArray<any>,\n  TUnwrappedArr extends\n    UnwrapReadonlyDerivedOrStoreArray<TArr> = UnwrapReadonlyDerivedOrStoreArray<TArr>,\n> {\n  // `undefined` if it's the first run\n  /**\n   * `undefined` if it's the first run\n   * @privateRemarks this also cannot be typed as TState, as it breaks the inferencing of the function's return type when an argument is used - even with `NoInfer` usage\n   */\n  prevVal: unknown | undefined\n  prevDepVals: TUnwrappedArr | undefined\n  currDepVals: TUnwrappedArr\n}\n\nexport interface DerivedOptions<\n  TState,\n  TArr extends ReadonlyArray<Derived<any> | Store<any>> = ReadonlyArray<any>,\n> {\n  onSubscribe?: (\n    listener: Listener<TState>,\n    derived: Derived<TState>,\n  ) => () => void\n  onUpdate?: () => void\n  deps: TArr\n  /**\n   * Values of the `deps` from before and after the current invocation of `fn`\n   */\n  fn: (props: DerivedFnProps<TArr>) => TState\n}\n\nexport class Derived<\n  TState,\n  const TArr extends ReadonlyArray<\n    Derived<any> | Store<any>\n  > = ReadonlyArray<any>,\n> {\n  listeners = new Set<Listener<TState>>()\n  state: TState\n  prevState: TState | undefined\n  options: DerivedOptions<TState, TArr>\n\n  /**\n   * Functions representing the subscriptions. Call a function to cleanup\n   * @private\n   */\n  _subscriptions: Array<() => void> = []\n\n  lastSeenDepValues: Array<unknown> = []\n  getDepVals = () => {\n    const prevDepVals = [] as Array<unknown>\n    const currDepVals = [] as Array<unknown>\n    for (const dep of this.options.deps) {\n      prevDepVals.push(dep.prevState)\n      currDepVals.push(dep.state)\n    }\n    this.lastSeenDepValues = currDepVals\n    return {\n      prevDepVals,\n      currDepVals,\n      prevVal: this.prevState ?? undefined,\n    }\n  }\n\n  constructor(options: DerivedOptions<TState, TArr>) {\n    this.options = options\n    this.state = options.fn({\n      prevDepVals: undefined,\n      prevVal: undefined,\n      currDepVals: this.getDepVals().currDepVals as never,\n    })\n  }\n\n  registerOnGraph(\n    deps: ReadonlyArray<Derived<any> | Store<any>> = this.options.deps,\n  ) {\n    for (const dep of deps) {\n      if (dep instanceof Derived) {\n        // First register the intermediate derived value if it's not already registered\n        dep.registerOnGraph()\n        // Then register this derived with the dep's underlying stores\n        this.registerOnGraph(dep.options.deps)\n      } else if (dep instanceof Store) {\n        // Register the derived as related derived to the store\n        let relatedLinkedDerivedVals = __storeToDerived.get(dep)\n        if (!relatedLinkedDerivedVals) {\n          relatedLinkedDerivedVals = new Set()\n          __storeToDerived.set(dep, relatedLinkedDerivedVals)\n        }\n        relatedLinkedDerivedVals.add(this as never)\n\n        // Register the store as a related store to this derived\n        let relatedStores = __derivedToStore.get(this as never)\n        if (!relatedStores) {\n          relatedStores = new Set()\n          __derivedToStore.set(this as never, relatedStores)\n        }\n        relatedStores.add(dep)\n      }\n    }\n  }\n\n  unregisterFromGraph(\n    deps: ReadonlyArray<Derived<any> | Store<any>> = this.options.deps,\n  ) {\n    for (const dep of deps) {\n      if (dep instanceof Derived) {\n        this.unregisterFromGraph(dep.options.deps)\n      } else if (dep instanceof Store) {\n        const relatedLinkedDerivedVals = __storeToDerived.get(dep)\n        if (relatedLinkedDerivedVals) {\n          relatedLinkedDerivedVals.delete(this as never)\n        }\n\n        const relatedStores = __derivedToStore.get(this as never)\n        if (relatedStores) {\n          relatedStores.delete(dep)\n        }\n      }\n    }\n  }\n\n  recompute = () => {\n    this.prevState = this.state\n    const { prevDepVals, currDepVals, prevVal } = this.getDepVals()\n    this.state = this.options.fn({\n      prevDepVals: prevDepVals as never,\n      currDepVals: currDepVals as never,\n      prevVal,\n    })\n\n    this.options.onUpdate?.()\n  }\n\n  checkIfRecalculationNeededDeeply = () => {\n    for (const dep of this.options.deps) {\n      if (dep instanceof Derived) {\n        dep.checkIfRecalculationNeededDeeply()\n      }\n    }\n    let shouldRecompute = false\n    const lastSeenDepValues = this.lastSeenDepValues\n    const { currDepVals } = this.getDepVals()\n    for (let i = 0; i < currDepVals.length; i++) {\n      if (currDepVals[i] !== lastSeenDepValues[i]) {\n        shouldRecompute = true\n        break\n      }\n    }\n\n    if (shouldRecompute) {\n      this.recompute()\n    }\n  }\n\n  mount = () => {\n    this.registerOnGraph()\n    this.checkIfRecalculationNeededDeeply()\n\n    return () => {\n      this.unregisterFromGraph()\n      for (const cleanup of this._subscriptions) {\n        cleanup()\n      }\n    }\n  }\n\n  subscribe = (listener: Listener<TState>) => {\n    this.listeners.add(listener)\n    const unsub = this.options.onSubscribe?.(listener, this)\n    return () => {\n      this.listeners.delete(listener)\n      unsub?.()\n    }\n  }\n}\n", "import { functionalUpdate } from './utils'\nimport type { AnyRouter } from './router'\nimport type { ParsedLocation } from './location'\nimport type { NonNullableUpdater } from './utils'\n\nexport type ScrollRestorationEntry = { scrollX: number; scrollY: number }\n\nexport type ScrollRestorationByElement = Record<string, ScrollRestorationEntry>\n\nexport type ScrollRestorationByKey = Record<string, ScrollRestorationByElement>\n\nexport type ScrollRestorationCache = {\n  state: ScrollRestorationByKey\n  set: (updater: NonNullableUpdater<ScrollRestorationByKey>) => void\n}\nexport type ScrollRestorationOptions = {\n  getKey?: (location: ParsedLocation) => string\n  scrollBehavior?: ScrollToOptions['behavior']\n}\n\nfunction getSafeSessionStorage() {\n  try {\n    if (\n      typeof window !== 'undefined' &&\n      typeof window.sessionStorage === 'object'\n    ) {\n      return window.sessionStorage\n    }\n  } catch {\n    return undefined\n  }\n  return undefined\n}\n\nexport const storageKey = 'tsr-scroll-restoration-v1_3'\n\nconst throttle = (fn: (...args: Array<any>) => void, wait: number) => {\n  let timeout: any\n  return (...args: Array<any>) => {\n    if (!timeout) {\n      timeout = setTimeout(() => {\n        fn(...args)\n        timeout = null\n      }, wait)\n    }\n  }\n}\n\nfunction createScrollRestorationCache(): ScrollRestorationCache | undefined {\n  const safeSessionStorage = getSafeSessionStorage()\n  if (!safeSessionStorage) {\n    return undefined\n  }\n\n  const persistedState = safeSessionStorage.getItem(storageKey)\n  let state: ScrollRestorationByKey = persistedState\n    ? JSON.parse(persistedState)\n    : {}\n\n  return {\n    state,\n    // This setter is simply to make sure that we set the sessionStorage right\n    // after the state is updated. It doesn't necessarily need to be a functional\n    // update.\n    set: (updater) => (\n      (state = functionalUpdate(updater, state) || state),\n      safeSessionStorage.setItem(storageKey, JSON.stringify(state))\n    ),\n  }\n}\n\nexport const scrollRestorationCache = createScrollRestorationCache()\n\n/**\n * The default `getKey` function for `useScrollRestoration`.\n * It returns the `key` from the location state or the `href` of the location.\n *\n * The `location.href` is used as a fallback to support the use case where the location state is not available like the initial render.\n */\n\nexport const defaultGetScrollRestorationKey = (location: ParsedLocation) => {\n  return location.state.key! || location.href\n}\n\nexport function getCssSelector(el: any): string {\n  const path = []\n  let parent\n  while ((parent = el.parentNode)) {\n    path.unshift(\n      `${el.tagName}:nth-child(${([].indexOf as any).call(parent.children, el) + 1})`,\n    )\n    el = parent\n  }\n  return `${path.join(' > ')}`.toLowerCase()\n}\n\nlet ignoreScroll = false\n\n// NOTE: This function must remain pure and not use any outside variables\n// unless they are passed in as arguments. Why? Because we need to be able to\n// toString() it into a script tag to execute as early as possible in the browser\n// during SSR. Additionally, we also call it from within the router lifecycle\nexport function restoreScroll(\n  storageKey: string,\n  key: string | undefined,\n  behavior: ScrollToOptions['behavior'] | undefined,\n  shouldScrollRestoration: boolean | undefined,\n  scrollToTopSelectors: Array<string> | undefined,\n) {\n  let byKey: ScrollRestorationByKey\n\n  try {\n    byKey = JSON.parse(sessionStorage.getItem(storageKey) || '{}')\n  } catch (error: any) {\n    console.error(error)\n    return\n  }\n\n  const resolvedKey = key || window.history.state?.key\n  const elementEntries = byKey[resolvedKey]\n\n  //\n  ignoreScroll = true\n\n  //\n  ;(() => {\n    // If we have a cached entry for this location state,\n    // we always need to prefer that over the hash scroll.\n    if (shouldScrollRestoration && elementEntries) {\n      for (const elementSelector in elementEntries) {\n        const entry = elementEntries[elementSelector]!\n        if (elementSelector === 'window') {\n          window.scrollTo({\n            top: entry.scrollY,\n            left: entry.scrollX,\n            behavior,\n          })\n        } else if (elementSelector) {\n          const element = document.querySelector(elementSelector)\n          if (element) {\n            element.scrollLeft = entry.scrollX\n            element.scrollTop = entry.scrollY\n          }\n        }\n      }\n\n      return\n    }\n\n    // If we don't have a cached entry for the hash,\n    // Which means we've never seen this location before,\n    // we need to check if there is a hash in the URL.\n    // If there is, we need to scroll it's ID into view.\n    const hash = window.location.hash.split('#')[1]\n\n    if (hash) {\n      const hashScrollIntoViewOptions =\n        (window.history.state || {}).__hashScrollIntoViewOptions ?? true\n\n      if (hashScrollIntoViewOptions) {\n        const el = document.getElementById(hash)\n        if (el) {\n          el.scrollIntoView(hashScrollIntoViewOptions)\n        }\n      }\n\n      return\n    }\n\n    // If there is no cached entry for the hash and there is no hash in the URL,\n    // we need to scroll to the top of the page for every scrollToTop element\n    ;[\n      'window',\n      ...(scrollToTopSelectors?.filter((d) => d !== 'window') ?? []),\n    ].forEach((selector) => {\n      const element =\n        selector === 'window' ? window : document.querySelector(selector)\n      if (element) {\n        element.scrollTo({\n          top: 0,\n          left: 0,\n          behavior,\n        })\n      }\n    })\n  })()\n\n  //\n  ignoreScroll = false\n}\n\nexport function setupScrollRestoration(router: AnyRouter, force?: boolean) {\n  if (scrollRestorationCache === undefined) {\n    return\n  }\n  const shouldScrollRestoration =\n    force ?? router.options.scrollRestoration ?? false\n\n  if (shouldScrollRestoration) {\n    router.isScrollRestoring = true\n  }\n\n  if (typeof document === 'undefined' || router.isScrollRestorationSetup) {\n    return\n  }\n\n  router.isScrollRestorationSetup = true\n\n  //\n  ignoreScroll = false\n\n  const getKey =\n    router.options.getScrollRestorationKey || defaultGetScrollRestorationKey\n\n  window.history.scrollRestoration = 'manual'\n\n  // // Create a MutationObserver to monitor DOM changes\n  // const mutationObserver = new MutationObserver(() => {\n  //   ;ignoreScroll = true\n  //   requestAnimationFrame(() => {\n  //     ;ignoreScroll = false\n\n  //     // Attempt to restore scroll position on each dom\n  //     // mutation until the user scrolls. We do this\n  //     // because dynamic content may come in at different\n  //     // ticks after the initial render and we want to\n  //     // keep up with that content as much as possible.\n  //     // As soon as the user scrolls, we no longer need\n  //     // to attempt router.\n  //     // console.log('mutation observer restoreScroll')\n  //     restoreScroll(\n  //       storageKey,\n  //       getKey(router.state.location),\n  //       router.options.scrollRestorationBehavior,\n  //     )\n  //   })\n  // })\n\n  // const observeDom = () => {\n  //   // Observe changes to the entire document\n  //   mutationObserver.observe(document, {\n  //     childList: true, // Detect added or removed child nodes\n  //     subtree: true, // Monitor all descendants\n  //     characterData: true, // Detect text content changes\n  //   })\n  // }\n\n  // const unobserveDom = () => {\n  //   mutationObserver.disconnect()\n  // }\n\n  // observeDom()\n\n  const onScroll = (event: Event) => {\n    // unobserveDom()\n\n    if (ignoreScroll || !router.isScrollRestoring) {\n      return\n    }\n\n    let elementSelector = ''\n\n    if (event.target === document || event.target === window) {\n      elementSelector = 'window'\n    } else {\n      const attrId = (event.target as Element).getAttribute(\n        'data-scroll-restoration-id',\n      )\n\n      if (attrId) {\n        elementSelector = `[data-scroll-restoration-id=\"${attrId}\"]`\n      } else {\n        elementSelector = getCssSelector(event.target)\n      }\n    }\n\n    const restoreKey = getKey(router.state.location)\n\n    scrollRestorationCache.set((state) => {\n      const keyEntry = (state[restoreKey] =\n        state[restoreKey] || ({} as ScrollRestorationByElement))\n\n      const elementEntry = (keyEntry[elementSelector] =\n        keyEntry[elementSelector] || ({} as ScrollRestorationEntry))\n\n      if (elementSelector === 'window') {\n        elementEntry.scrollX = window.scrollX || 0\n        elementEntry.scrollY = window.scrollY || 0\n      } else if (elementSelector) {\n        const element = document.querySelector(elementSelector)\n        if (element) {\n          elementEntry.scrollX = element.scrollLeft || 0\n          elementEntry.scrollY = element.scrollTop || 0\n        }\n      }\n\n      return state\n    })\n  }\n\n  // Throttle the scroll event to avoid excessive updates\n  if (typeof document !== 'undefined') {\n    document.addEventListener('scroll', throttle(onScroll, 100), true)\n  }\n\n  router.subscribe('onRendered', (event) => {\n    // unobserveDom()\n\n    const cacheKey = getKey(event.toLocation)\n\n    // If the user doesn't want to restore the scroll position,\n    // we don't need to do anything.\n    if (!router.resetNextScroll) {\n      router.resetNextScroll = true\n      return\n    }\n\n    restoreScroll(\n      storageKey,\n      cacheKey,\n      router.options.scrollRestorationBehavior || undefined,\n      router.isScrollRestoring || undefined,\n      router.options.scrollToTopSelectors || undefined,\n    )\n\n    if (router.isScrollRestoring) {\n      // Mark the location as having been seen\n      scrollRestorationCache.set((state) => {\n        state[cacheKey] = state[cacheKey] || ({} as ScrollRestorationByElement)\n\n        return state\n      })\n    }\n  })\n}\n\n/**\n * @internal\n * Handles hash-based scrolling after navigation completes.\n * To be used in framework-specific <Transitioner> components during the onResolved event.\n *\n * Provides hash scrolling for programmatic navigation when default browser handling is prevented.\n * @param router The router instance containing current location and state\n */\nexport function handleHashScroll(router: AnyRouter) {\n  if (typeof document !== 'undefined' && (document as any).querySelector) {\n    const hashScrollIntoViewOptions =\n      router.state.location.state.__hashScrollIntoViewOptions ?? true\n\n    if (hashScrollIntoViewOptions && router.state.location.hash !== '') {\n      const el = document.getElementById(router.state.location.hash)\n      if (el) {\n        el.scrollIntoView(hashScrollIntoViewOptions)\n      }\n    }\n  }\n}\n", "import { Store, batch } from '@tanstack/store'\nimport {\n  createBrowserHistory,\n  createMemoryHistory,\n  parseHref,\n} from '@tanstack/history'\nimport invariant from 'tiny-invariant'\nimport {\n  createControlledPromise,\n  deepEqual,\n  functionalUpdate,\n  last,\n  pick,\n  replaceEqualDeep,\n} from './utils'\nimport {\n  cleanPath,\n  interpolatePath,\n  joinPaths,\n  matchPathname,\n  parsePathname,\n  resolvePath,\n  trimPath,\n  trimPathLeft,\n  trimPathRight,\n} from './path'\nimport { isNotFound } from './not-found'\nimport { setupScrollRestoration } from './scroll-restoration'\nimport { defaultParseSearch, defaultStringifySearch } from './searchParams'\nimport { rootRouteId } from './root'\nimport { isRedirect, isResolvedRedirect } from './redirect'\nimport type { SearchParser, SearchSerializer } from './searchParams'\nimport type { AnyRedirect, ResolvedRedirect } from './redirect'\nimport type {\n  HistoryLocation,\n  HistoryState,\n  ParsedHistoryState,\n  RouterHistory,\n} from '@tanstack/history'\nimport type {\n  ControlledPromise,\n  NoInfer,\n  NonNullableUpdater,\n  PickAsRequired,\n  Updater,\n} from './utils'\nimport type { ParsedLocation } from './location'\nimport type { DeferredPromiseState } from './defer'\nimport type {\n  AnyContext,\n  AnyRoute,\n  AnyRouteWithContext,\n  BeforeLoadContextOptions,\n  LoaderFnContext,\n  MakeRemountDepsOptionsUnion,\n  RouteContextOptions,\n  RouteMask,\n  SearchMiddleware,\n} from './route'\nimport type {\n  FullSearchSchema,\n  RouteById,\n  RoutePaths,\n  RoutesById,\n  RoutesByPath,\n} from './routeInfo'\nimport type {\n  AnyRouteMatch,\n  MakeRouteMatch,\n  MakeRouteMatchUnion,\n  MatchRouteOptions,\n} from './Matches'\nimport type {\n  BuildLocationFn,\n  CommitLocationOptions,\n  NavigateFn,\n} from './RouterProvider'\nimport type { Manifest } from './manifest'\nimport type { StartSerializer } from './serializer'\nimport type { AnySchema, AnyValidator } from './validators'\nimport type { NavigateOptions, ResolveRelativePath, ToOptions } from './link'\nimport type { NotFoundError } from './not-found'\n\ndeclare global {\n  interface Window {\n    __TSR_ROUTER__?: AnyRouter\n  }\n}\n\nexport type ControllablePromise<T = any> = Promise<T> & {\n  resolve: (value: T) => void\n  reject: (value?: any) => void\n}\n\nexport type InjectedHtmlEntry = Promise<string>\n\nexport interface DefaultRegister {\n  router: AnyRouter\n}\n\nexport interface Register extends DefaultRegister {\n  // router: Router\n}\n\nexport type RegisteredRouter = Register['router']\n\nexport type DefaultRemountDepsFn<TRouteTree extends AnyRoute> = (\n  opts: MakeRemountDepsOptionsUnion<TRouteTree>,\n) => any\n\nexport interface DefaultRouterOptionsExtensions {}\n\nexport interface RouterOptionsExtensions\n  extends DefaultRouterOptionsExtensions {}\n\nexport interface RouterOptions<\n  TRouteTree extends AnyRoute,\n  TTrailingSlashOption extends TrailingSlashOption,\n  TDefaultStructuralSharingOption extends boolean = false,\n  TRouterHistory extends RouterHistory = RouterHistory,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n> extends RouterOptionsExtensions {\n  /**\n   * The history object that will be used to manage the browser history.\n   *\n   * If not provided, a new createBrowserHistory instance will be created and used.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#history-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/history-types)\n   */\n  history?: TRouterHistory\n  /**\n   * A function that will be used to stringify search params when generating links.\n   *\n   * @default defaultStringifySearch\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#stringifysearch-method)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/custom-search-param-serialization)\n   */\n  stringifySearch?: SearchSerializer\n  /**\n   * A function that will be used to parse search params when parsing the current location.\n   *\n   * @default defaultParseSearch\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#parsesearch-method)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/custom-search-param-serialization)\n   */\n  parseSearch?: SearchParser\n  /**\n   * If `false`, routes will not be preloaded by default in any way.\n   *\n   * If `'intent'`, routes will be preloaded by default when the user hovers over a link or a `touchstart` event is detected on a `<Link>`.\n   *\n   * If `'viewport'`, routes will be preloaded by default when they are within the viewport.\n   *\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpreload-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/preloading)\n   */\n  defaultPreload?: false | 'intent' | 'viewport' | 'render'\n  /**\n   * The delay in milliseconds that a route must be hovered over or touched before it is preloaded.\n   *\n   * @default 50\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpreloaddelay-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/preloading#preload-delay)\n   */\n  defaultPreloadDelay?: number\n  /**\n   * The default `pendingMs` a route should use if no pendingMs is provided.\n   *\n   * @default 1000\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpendingms-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#avoiding-pending-component-flash)\n   */\n  defaultPendingMs?: number\n  /**\n   * The default `pendingMinMs` a route should use if no pendingMinMs is provided.\n   *\n   * @default 500\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpendingminms-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#avoiding-pending-component-flash)\n   */\n  defaultPendingMinMs?: number\n  /**\n   * The default `staleTime` a route should use if no staleTime is provided. This is the time in milliseconds that a route will be considered fresh.\n   *\n   * @default 0\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultstaletime-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#key-options)\n   */\n  defaultStaleTime?: number\n  /**\n   * The default `preloadStaleTime` a route should use if no preloadStaleTime is provided.\n   *\n   * @default 30_000 `(30 seconds)`\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpreloadstaletime-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/preloading)\n   */\n  defaultPreloadStaleTime?: number\n  /**\n   * The default `defaultPreloadGcTime` a route should use if no preloadGcTime is provided.\n   *\n   * @default 1_800_000 `(30 minutes)`\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultpreloadgctime-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/preloading)\n   */\n  defaultPreloadGcTime?: number\n  /**\n   * If `true`, route navigations will called using `document.startViewTransition()`.\n   *\n   * If the browser does not support this api, this option will be ignored.\n   *\n   * See [MDN](https://developer.mozilla.org/en-US/docs/Web/API/Document/startViewTransition) for more information on how this function works.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultviewtransition-property)\n   */\n  defaultViewTransition?: boolean | ViewTransitionOptions\n  /**\n   * The default `hashScrollIntoView` a route should use if no hashScrollIntoView is provided while navigating\n   *\n   * See [MDN](https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView) for more information on `ScrollIntoViewOptions`.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaulthashscrollintoview-property)\n   */\n  defaultHashScrollIntoView?: boolean | ScrollIntoViewOptions\n  /**\n   * @default 'fuzzy'\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#notfoundmode-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/not-found-errors#the-notfoundmode-option)\n   */\n  notFoundMode?: 'root' | 'fuzzy'\n  /**\n   * The default `gcTime` a route should use if no gcTime is provided.\n   *\n   * @default 1_800_000 `(30 minutes)`\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultgctime-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/data-loading#key-options)\n   */\n  defaultGcTime?: number\n  /**\n   * If `true`, all routes will be matched as case-sensitive.\n   *\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#casesensitive-property)\n   */\n  caseSensitive?: boolean\n  /**\n   *\n   * The route tree that will be used to configure the router instance.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#routetree-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/routing/route-trees)\n   */\n  routeTree?: TRouteTree\n  /**\n   * The basepath for then entire router. This is useful for mounting a router instance at a subpath.\n   *\n   * @default '/'\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#basepath-property)\n   */\n  basepath?: string\n  /**\n   * The root context that will be provided to all routes in the route tree.\n   *\n   * This can be used to provide a context to all routes in the tree without having to provide it to each route individually.\n   *\n   * Optional or required if the root route was created with [`createRootRouteWithContext()`](https://tanstack.com/router/latest/docs/framework/react/api/router/createRootRouteWithContextFunction).\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#context-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/router-context)\n   */\n  context?: InferRouterContext<TRouteTree>\n  /**\n   * A function that will be called when the router is dehydrated.\n   *\n   * The return value of this function will be serialized and stored in the router's dehydrated state.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#dehydrate-method)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/external-data-loading#critical-dehydrationhydration)\n   */\n  dehydrate?: () => TDehydrated\n  /**\n   * A function that will be called when the router is hydrated.\n   *\n   * The return value of this function will be serialized and stored in the router's dehydrated state.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#hydrate-method)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/external-data-loading#critical-dehydrationhydration)\n   */\n  hydrate?: (dehydrated: TDehydrated) => void\n  /**\n   * An array of route masks that will be used to mask routes in the route tree.\n   *\n   * Route masking is when you display a route at a different path than the one it is configured to match, like a modal popup that when shared will unmask to the modal's content instead of the modal's context.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#routemasks-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/route-masking)\n   */\n  routeMasks?: Array<RouteMask<TRouteTree>>\n  /**\n   * If `true`, route masks will, by default, be removed when the page is reloaded.\n   *\n   * This can be overridden on a per-mask basis by setting the `unmaskOnReload` option on the mask, or on a per-navigation basis by setting the `unmaskOnReload` option in the `Navigate` options.\n   *\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#unmaskonreload-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/route-masking#unmasking-on-page-reload)\n   */\n  unmaskOnReload?: boolean\n\n  /**\n   * Use `notFoundComponent` instead.\n   *\n   * @deprecated\n   * See https://tanstack.com/router/v1/docs/guide/not-found-errors#migrating-from-notfoundroute for more info.\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#notfoundroute-property)\n   */\n  notFoundRoute?: AnyRoute\n  /**\n   * Configures how trailing slashes are treated.\n   *\n   * - `'always'` will add a trailing slash if not present\n   * - `'never'` will remove the trailing slash if present\n   * - `'preserve'` will not modify the trailing slash.\n   *\n   * @default 'never'\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#trailingslash-property)\n   */\n  trailingSlash?: TTrailingSlashOption\n  /**\n   * While usually automatic, sometimes it can be useful to force the router into a server-side state, e.g. when using the router in a non-browser environment that has access to a global.document object.\n   *\n   * @default typeof document !== 'undefined'\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#isserver-property)\n   */\n  isServer?: boolean\n\n  defaultSsr?: boolean\n\n  search?: {\n    /**\n     * Configures how unknown search params (= not returned by any `validateSearch`) are treated.\n     *\n     * @default false\n     * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#search.strict-property)\n     */\n    strict?: boolean\n  }\n\n  /**\n   * Configures whether structural sharing is enabled by default for fine-grained selectors.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#defaultstructuralsharing-property)\n   */\n  defaultStructuralSharing?: TDefaultStructuralSharingOption\n\n  /**\n   * Configures which URI characters are allowed in path params that would ordinarily be escaped by encodeURIComponent.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/RouterOptionsType#pathparamsallowedcharacters-property)\n   * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/path-params#allowed-characters)\n   */\n  pathParamsAllowedCharacters?: Array<\n    ';' | ':' | '@' | '&' | '=' | '+' | '$' | ','\n  >\n\n  defaultRemountDeps?: DefaultRemountDepsFn<TRouteTree>\n\n  /**\n   * If `true`, scroll restoration will be enabled\n   *\n   * @default false\n   */\n  scrollRestoration?: boolean\n\n  /**\n   * A function that will be called to get the key for the scroll restoration cache.\n   *\n   * @default (location) => location.href\n   */\n  getScrollRestorationKey?: (location: ParsedLocation) => string\n  /**\n   * The default behavior for scroll restoration.\n   *\n   * @default 'auto'\n   */\n  scrollRestorationBehavior?: ScrollBehavior\n  /**\n   * An array of selectors that will be used to scroll to the top of the page in addition to `window`\n   *\n   * @default ['window']\n   */\n  scrollToTopSelectors?: Array<string>\n}\n\nexport interface RouterState<\n  in out TRouteTree extends AnyRoute = AnyRoute,\n  in out TRouteMatch = MakeRouteMatchUnion,\n> {\n  status: 'pending' | 'idle'\n  loadedAt: number\n  isLoading: boolean\n  isTransitioning: boolean\n  matches: Array<TRouteMatch>\n  pendingMatches?: Array<TRouteMatch>\n  cachedMatches: Array<TRouteMatch>\n  location: ParsedLocation<FullSearchSchema<TRouteTree>>\n  resolvedLocation?: ParsedLocation<FullSearchSchema<TRouteTree>>\n  statusCode: number\n  redirect?: ResolvedRedirect\n}\n\nexport interface BuildNextOptions {\n  to?: string | number | null\n  params?: true | Updater<unknown>\n  search?: true | Updater<unknown>\n  hash?: true | Updater<string>\n  state?: true | NonNullableUpdater<ParsedHistoryState, HistoryState>\n  mask?: {\n    to?: string | number | null\n    params?: true | Updater<unknown>\n    search?: true | Updater<unknown>\n    hash?: true | Updater<string>\n    state?: true | NonNullableUpdater<ParsedHistoryState, HistoryState>\n    unmaskOnReload?: boolean\n  }\n  from?: string\n  _fromLocation?: ParsedLocation\n  href?: string\n}\n\ntype NavigationEventInfo = {\n  fromLocation?: ParsedLocation\n  toLocation: ParsedLocation\n  pathChanged: boolean\n  hrefChanged: boolean\n  hashChanged: boolean\n}\n\nexport type RouterEvents = {\n  onBeforeNavigate: {\n    type: 'onBeforeNavigate'\n  } & NavigationEventInfo\n  onBeforeLoad: {\n    type: 'onBeforeLoad'\n  } & NavigationEventInfo\n  onLoad: {\n    type: 'onLoad'\n  } & NavigationEventInfo\n  onResolved: {\n    type: 'onResolved'\n  } & NavigationEventInfo\n  onBeforeRouteMount: {\n    type: 'onBeforeRouteMount'\n  } & NavigationEventInfo\n  onInjectedHtml: {\n    type: 'onInjectedHtml'\n    promise: Promise<string>\n  }\n  onRendered: {\n    type: 'onRendered'\n  } & NavigationEventInfo\n}\n\nexport type RouterEvent = RouterEvents[keyof RouterEvents]\n\nexport type ListenerFn<TEvent extends RouterEvent> = (event: TEvent) => void\n\nexport type RouterListener<TRouterEvent extends RouterEvent> = {\n  eventType: TRouterEvent['type']\n  fn: ListenerFn<TRouterEvent>\n}\n\nexport interface MatchRoutesOpts {\n  preload?: boolean\n  throwOnError?: boolean\n  _buildLocation?: boolean\n  dest?: BuildNextOptions\n}\n\nexport type InferRouterContext<TRouteTree extends AnyRoute> =\n  TRouteTree['types']['routerContext']\n\nexport type RouterContextOptions<TRouteTree extends AnyRoute> =\n  AnyContext extends InferRouterContext<TRouteTree>\n    ? {\n        context?: InferRouterContext<TRouteTree>\n      }\n    : {\n        context: InferRouterContext<TRouteTree>\n      }\n\nexport type RouterConstructorOptions<\n  TRouteTree extends AnyRoute,\n  TTrailingSlashOption extends TrailingSlashOption,\n  TDefaultStructuralSharingOption extends boolean,\n  TRouterHistory extends RouterHistory,\n  TDehydrated extends Record<string, any>,\n> = Omit<\n  RouterOptions<\n    TRouteTree,\n    TTrailingSlashOption,\n    TDefaultStructuralSharingOption,\n    TRouterHistory,\n    TDehydrated\n  >,\n  'context'\n> &\n  RouterContextOptions<TRouteTree>\n\nexport interface RouterErrorSerializer<TSerializedError> {\n  serialize: (err: unknown) => TSerializedError\n  deserialize: (err: TSerializedError) => unknown\n}\n\nexport interface MatchedRoutesResult {\n  matchedRoutes: Array<AnyRoute>\n  routeParams: Record<string, string>\n}\n\nexport type PreloadRouteFn<\n  TRouteTree extends AnyRoute,\n  TTrailingSlashOption extends TrailingSlashOption,\n  TDefaultStructuralSharingOption extends boolean,\n  TRouterHistory extends RouterHistory,\n> = <\n  TFrom extends RoutePaths<TRouteTree> | string = string,\n  TTo extends string | undefined = undefined,\n  TMaskFrom extends RoutePaths<TRouteTree> | string = TFrom,\n  TMaskTo extends string = '',\n>(\n  opts: NavigateOptions<\n    RouterCore<\n      TRouteTree,\n      TTrailingSlashOption,\n      TDefaultStructuralSharingOption,\n      TRouterHistory\n    >,\n    TFrom,\n    TTo,\n    TMaskFrom,\n    TMaskTo\n  >,\n) => Promise<Array<AnyRouteMatch> | undefined>\n\nexport type MatchRouteFn<\n  TRouteTree extends AnyRoute,\n  TTrailingSlashOption extends TrailingSlashOption,\n  TDefaultStructuralSharingOption extends boolean,\n  TRouterHistory extends RouterHistory,\n> = <\n  TFrom extends RoutePaths<TRouteTree> = '/',\n  TTo extends string | undefined = undefined,\n  TResolved = ResolveRelativePath<TFrom, NoInfer<TTo>>,\n>(\n  location: ToOptions<\n    RouterCore<\n      TRouteTree,\n      TTrailingSlashOption,\n      TDefaultStructuralSharingOption,\n      TRouterHistory\n    >,\n    TFrom,\n    TTo\n  >,\n  opts?: MatchRouteOptions,\n) => false | RouteById<TRouteTree, TResolved>['types']['allParams']\n\nexport type UpdateFn<\n  TRouteTree extends AnyRoute,\n  TTrailingSlashOption extends TrailingSlashOption,\n  TDefaultStructuralSharingOption extends boolean,\n  TRouterHistory extends RouterHistory,\n  TDehydrated extends Record<string, any>,\n> = (\n  newOptions: RouterConstructorOptions<\n    TRouteTree,\n    TTrailingSlashOption,\n    TDefaultStructuralSharingOption,\n    TRouterHistory,\n    TDehydrated\n  >,\n) => void\n\nexport type InvalidateFn<TRouter extends AnyRouter> = (opts?: {\n  filter?: (d: MakeRouteMatchUnion<TRouter>) => boolean\n  sync?: boolean\n}) => Promise<void>\n\nexport type ParseLocationFn<TRouteTree extends AnyRoute> = (\n  previousLocation?: ParsedLocation<FullSearchSchema<TRouteTree>>,\n  locationToParse?: HistoryLocation,\n) => ParsedLocation<FullSearchSchema<TRouteTree>>\n\nexport type GetMatchRoutesFn = (\n  next: ParsedLocation,\n  dest?: BuildNextOptions,\n) => {\n  matchedRoutes: Array<AnyRoute>\n  routeParams: Record<string, string>\n  foundRoute: AnyRoute | undefined\n}\n\nexport type EmitFn = (routerEvent: RouterEvent) => void\n\nexport type LoadFn = (opts?: { sync?: boolean }) => Promise<void>\n\nexport type CommitLocationFn = ({\n  viewTransition,\n  ignoreBlocker,\n  ...next\n}: ParsedLocation & CommitLocationOptions) => Promise<void>\n\nexport type StartTransitionFn = (fn: () => void) => void\n\nexport type SubscribeFn = <TType extends keyof RouterEvents>(\n  eventType: TType,\n  fn: ListenerFn<RouterEvents[TType]>,\n) => () => void\n\nexport interface MatchRoutesFn {\n  (\n    pathname: string,\n    locationSearch: AnySchema,\n    opts?: MatchRoutesOpts,\n  ): Array<AnyRouteMatch>\n  (next: ParsedLocation, opts?: MatchRoutesOpts): Array<AnyRouteMatch>\n  (\n    pathnameOrNext: string | ParsedLocation,\n    locationSearchOrOpts?: AnySchema | MatchRoutesOpts,\n    opts?: MatchRoutesOpts,\n  ): Array<AnyRouteMatch>\n}\n\nexport type GetMatchFn = (matchId: string) => AnyRouteMatch | undefined\n\nexport type UpdateMatchFn = (\n  id: string,\n  updater: (match: AnyRouteMatch) => AnyRouteMatch,\n) => AnyRouteMatch\n\nexport type LoadRouteChunkFn = (route: AnyRoute) => Promise<Array<void>>\n\nexport type ResolveRedirect = (err: AnyRedirect) => ResolvedRedirect\n\nexport type ClearCacheFn<TRouter extends AnyRouter> = (opts?: {\n  filter?: (d: MakeRouteMatchUnion<TRouter>) => boolean\n}) => void\n\nexport interface ServerSrr {\n  injectedHtml: Array<InjectedHtmlEntry>\n  injectHtml: (getHtml: () => string | Promise<string>) => Promise<void>\n  injectScript: (\n    getScript: () => string | Promise<string>,\n    opts?: { logScript?: boolean },\n  ) => Promise<void>\n  streamValue: (key: string, value: any) => void\n  streamedKeys: Set<string>\n  onMatchSettled: (opts: { router: AnyRouter; match: AnyRouteMatch }) => any\n}\n\nexport type AnyRouterWithContext<TContext> = RouterCore<\n  AnyRouteWithContext<TContext>,\n  any,\n  any,\n  any,\n  any\n>\n\nexport type AnyRouter = RouterCore<any, any, any, any, any>\n\nexport interface ViewTransitionOptions {\n  types:\n    | Array<string>\n    | ((locationChangeInfo: {\n        fromLocation?: ParsedLocation\n        toLocation: ParsedLocation\n        pathChanged: boolean\n        hrefChanged: boolean\n        hashChanged: boolean\n      }) => Array<string>)\n}\n\nexport function defaultSerializeError(err: unknown) {\n  if (err instanceof Error) {\n    const obj = {\n      name: err.name,\n      message: err.message,\n    }\n\n    if (process.env.NODE_ENV === 'development') {\n      ;(obj as any).stack = err.stack\n    }\n\n    return obj\n  }\n\n  return {\n    data: err,\n  }\n}\nexport interface ExtractedBaseEntry {\n  dataType: '__beforeLoadContext' | 'loaderData'\n  type: string\n  path: Array<string>\n  id: number\n  matchIndex: number\n}\n\nexport interface ExtractedStream extends ExtractedBaseEntry {\n  type: 'stream'\n  streamState: StreamState\n}\n\nexport interface ExtractedPromise extends ExtractedBaseEntry {\n  type: 'promise'\n  promiseState: DeferredPromiseState<any>\n}\n\nexport type ExtractedEntry = ExtractedStream | ExtractedPromise\n\nexport type StreamState = {\n  promises: Array<ControlledPromise<string | null>>\n}\n\nexport type TrailingSlashOption = 'always' | 'never' | 'preserve'\n\nexport function getLocationChangeInfo(routerState: {\n  resolvedLocation?: ParsedLocation\n  location: ParsedLocation\n}) {\n  const fromLocation = routerState.resolvedLocation\n  const toLocation = routerState.location\n  const pathChanged = fromLocation?.pathname !== toLocation.pathname\n  const hrefChanged = fromLocation?.href !== toLocation.href\n  const hashChanged = fromLocation?.hash !== toLocation.hash\n  return { fromLocation, toLocation, pathChanged, hrefChanged, hashChanged }\n}\n\nexport type CreateRouterFn = <\n  TRouteTree extends AnyRoute,\n  TTrailingSlashOption extends TrailingSlashOption = 'never',\n  TDefaultStructuralSharingOption extends boolean = false,\n  TRouterHistory extends RouterHistory = RouterHistory,\n  TDehydrated extends Record<string, any> = Record<string, any>,\n>(\n  options: undefined extends number\n    ? 'strictNullChecks must be enabled in tsconfig.json'\n    : RouterConstructorOptions<\n        TRouteTree,\n        TTrailingSlashOption,\n        TDefaultStructuralSharingOption,\n        TRouterHistory,\n        TDehydrated\n      >,\n) => RouterCore<\n  TRouteTree,\n  TTrailingSlashOption,\n  TDefaultStructuralSharingOption,\n  TRouterHistory,\n  TDehydrated\n>\n\nexport class RouterCore<\n  in out TRouteTree extends AnyRoute,\n  in out TTrailingSlashOption extends TrailingSlashOption,\n  in out TDefaultStructuralSharingOption extends boolean,\n  in out TRouterHistory extends RouterHistory = RouterHistory,\n  in out TDehydrated extends Record<string, any> = Record<string, any>,\n> {\n  // Option-independent properties\n  tempLocationKey: string | undefined = `${Math.round(\n    Math.random() * 10000000,\n  )}`\n  resetNextScroll = true\n  shouldViewTransition?: boolean | ViewTransitionOptions = undefined\n  isViewTransitionTypesSupported?: boolean = undefined\n  subscribers = new Set<RouterListener<RouterEvent>>()\n  viewTransitionPromise?: ControlledPromise<true>\n  isScrollRestoring = false\n  isScrollRestorationSetup = false\n\n  // Must build in constructor\n  __store!: Store<RouterState<TRouteTree>>\n  options!: PickAsRequired<\n    RouterOptions<\n      TRouteTree,\n      TTrailingSlashOption,\n      TDefaultStructuralSharingOption,\n      TRouterHistory,\n      TDehydrated\n    >,\n    'stringifySearch' | 'parseSearch' | 'context'\n  >\n  history!: TRouterHistory\n  latestLocation!: ParsedLocation<FullSearchSchema<TRouteTree>>\n  basepath!: string\n  routeTree!: TRouteTree\n  routesById!: RoutesById<TRouteTree>\n  routesByPath!: RoutesByPath<TRouteTree>\n  flatRoutes!: Array<AnyRoute>\n  isServer!: boolean\n  pathParamsDecodeCharMap?: Map<string, string>\n\n  /**\n   * @deprecated Use the `createRouter` function instead\n   */\n  constructor(\n    options: RouterConstructorOptions<\n      TRouteTree,\n      TTrailingSlashOption,\n      TDefaultStructuralSharingOption,\n      TRouterHistory,\n      TDehydrated\n    >,\n  ) {\n    this.update({\n      defaultPreloadDelay: 50,\n      defaultPendingMs: 1000,\n      defaultPendingMinMs: 500,\n      context: undefined!,\n      ...options,\n      caseSensitive: options.caseSensitive ?? false,\n      notFoundMode: options.notFoundMode ?? 'fuzzy',\n      stringifySearch: options.stringifySearch ?? defaultStringifySearch,\n      parseSearch: options.parseSearch ?? defaultParseSearch,\n    })\n\n    if (typeof document !== 'undefined') {\n      ;(window as any).__TSR_ROUTER__ = this\n    }\n  }\n\n  // These are default implementations that can optionally be overridden\n  // by the router provider once rendered. We provide these so that the\n  // router can be used in a non-react environment if necessary\n  startTransition: StartTransitionFn = (fn) => fn()\n\n  update: UpdateFn<\n    TRouteTree,\n    TTrailingSlashOption,\n    TDefaultStructuralSharingOption,\n    TRouterHistory,\n    TDehydrated\n  > = (newOptions) => {\n    if (newOptions.notFoundRoute) {\n      console.warn(\n        'The notFoundRoute API is deprecated and will be removed in the next major version. See https://tanstack.com/router/v1/docs/framework/react/guide/not-found-errors#migrating-from-notfoundroute for more info.',\n      )\n    }\n\n    const previousOptions = this.options\n    this.options = {\n      ...this.options,\n      ...newOptions,\n    }\n\n    this.isServer = this.options.isServer ?? typeof document === 'undefined'\n\n    this.pathParamsDecodeCharMap = this.options.pathParamsAllowedCharacters\n      ? new Map(\n          this.options.pathParamsAllowedCharacters.map((char) => [\n            encodeURIComponent(char),\n            char,\n          ]),\n        )\n      : undefined\n\n    if (\n      !this.basepath ||\n      (newOptions.basepath && newOptions.basepath !== previousOptions.basepath)\n    ) {\n      if (\n        newOptions.basepath === undefined ||\n        newOptions.basepath === '' ||\n        newOptions.basepath === '/'\n      ) {\n        this.basepath = '/'\n      } else {\n        this.basepath = `/${trimPath(newOptions.basepath)}`\n      }\n    }\n\n    if (\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      !this.history ||\n      (this.options.history && this.options.history !== this.history)\n    ) {\n      this.history =\n        this.options.history ??\n        ((this.isServer\n          ? createMemoryHistory({\n              initialEntries: [this.basepath || '/'],\n            })\n          : createBrowserHistory()) as TRouterHistory)\n      this.latestLocation = this.parseLocation()\n    }\n\n    if (this.options.routeTree !== this.routeTree) {\n      this.routeTree = this.options.routeTree as TRouteTree\n      this.buildRouteTree()\n    }\n\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!this.__store) {\n      this.__store = new Store(getInitialRouterState(this.latestLocation), {\n        onUpdate: () => {\n          this.__store.state = {\n            ...this.state,\n            cachedMatches: this.state.cachedMatches.filter(\n              (d) => !['redirected'].includes(d.status),\n            ),\n          }\n        },\n      })\n\n      setupScrollRestoration(this)\n    }\n\n    if (\n      typeof window !== 'undefined' &&\n      'CSS' in window &&\n      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n      typeof window.CSS?.supports === 'function'\n    ) {\n      this.isViewTransitionTypesSupported = window.CSS.supports(\n        'selector(:active-view-transition-type(a)',\n      )\n    }\n  }\n\n  get state() {\n    return this.__store.state\n  }\n\n  buildRouteTree = () => {\n    this.routesById = {} as RoutesById<TRouteTree>\n    this.routesByPath = {} as RoutesByPath<TRouteTree>\n\n    const notFoundRoute = this.options.notFoundRoute\n    if (notFoundRoute) {\n      notFoundRoute.init({\n        originalIndex: 99999999999,\n        defaultSsr: this.options.defaultSsr,\n      })\n      ;(this.routesById as any)[notFoundRoute.id] = notFoundRoute\n    }\n\n    const recurseRoutes = (childRoutes: Array<AnyRoute>) => {\n      childRoutes.forEach((childRoute, i) => {\n        childRoute.init({\n          originalIndex: i,\n          defaultSsr: this.options.defaultSsr,\n        })\n\n        const existingRoute = (this.routesById as any)[childRoute.id]\n\n        invariant(\n          !existingRoute,\n          `Duplicate routes found with id: ${String(childRoute.id)}`,\n        )\n        ;(this.routesById as any)[childRoute.id] = childRoute\n\n        if (!childRoute.isRoot && childRoute.path) {\n          const trimmedFullPath = trimPathRight(childRoute.fullPath)\n          if (\n            !(this.routesByPath as any)[trimmedFullPath] ||\n            childRoute.fullPath.endsWith('/')\n          ) {\n            ;(this.routesByPath as any)[trimmedFullPath] = childRoute\n          }\n        }\n\n        const children = childRoute.children\n\n        if (children?.length) {\n          recurseRoutes(children)\n        }\n      })\n    }\n\n    recurseRoutes([this.routeTree])\n\n    const scoredRoutes: Array<{\n      child: AnyRoute\n      trimmed: string\n      parsed: ReturnType<typeof parsePathname>\n      index: number\n      scores: Array<number>\n    }> = []\n\n    const routes: Array<AnyRoute> = Object.values(this.routesById)\n\n    routes.forEach((d, i) => {\n      if (d.isRoot || !d.path) {\n        return\n      }\n\n      const trimmed = trimPathLeft(d.fullPath)\n      const parsed = parsePathname(trimmed)\n\n      while (parsed.length > 1 && parsed[0]?.value === '/') {\n        parsed.shift()\n      }\n\n      const scores = parsed.map((segment) => {\n        if (segment.value === '/') {\n          return 0.75\n        }\n\n        if (segment.type === 'param') {\n          return 0.5\n        }\n\n        if (segment.type === 'wildcard') {\n          return 0.25\n        }\n\n        return 1\n      })\n\n      scoredRoutes.push({ child: d, trimmed, parsed, index: i, scores })\n    })\n\n    this.flatRoutes = scoredRoutes\n      .sort((a, b) => {\n        const minLength = Math.min(a.scores.length, b.scores.length)\n\n        // Sort by min available score\n        for (let i = 0; i < minLength; i++) {\n          if (a.scores[i] !== b.scores[i]) {\n            return b.scores[i]! - a.scores[i]!\n          }\n        }\n\n        // Sort by length of score\n        if (a.scores.length !== b.scores.length) {\n          return b.scores.length - a.scores.length\n        }\n\n        // Sort by min available parsed value\n        for (let i = 0; i < minLength; i++) {\n          if (a.parsed[i]!.value !== b.parsed[i]!.value) {\n            return a.parsed[i]!.value > b.parsed[i]!.value ? 1 : -1\n          }\n        }\n\n        // Sort by original index\n        return a.index - b.index\n      })\n      .map((d, i) => {\n        d.child.rank = i\n        return d.child\n      })\n  }\n\n  subscribe: SubscribeFn = (eventType, fn) => {\n    const listener: RouterListener<any> = {\n      eventType,\n      fn,\n    }\n\n    this.subscribers.add(listener)\n\n    return () => {\n      this.subscribers.delete(listener)\n    }\n  }\n\n  emit: EmitFn = (routerEvent) => {\n    this.subscribers.forEach((listener) => {\n      if (listener.eventType === routerEvent.type) {\n        listener.fn(routerEvent)\n      }\n    })\n  }\n\n  parseLocation: ParseLocationFn<TRouteTree> = (\n    previousLocation,\n    locationToParse,\n  ) => {\n    const parse = ({\n      pathname,\n      search,\n      hash,\n      state,\n    }: HistoryLocation): ParsedLocation<FullSearchSchema<TRouteTree>> => {\n      const parsedSearch = this.options.parseSearch(search)\n      const searchStr = this.options.stringifySearch(parsedSearch)\n\n      return {\n        pathname,\n        searchStr,\n        search: replaceEqualDeep(previousLocation?.search, parsedSearch) as any,\n        hash: hash.split('#').reverse()[0] ?? '',\n        href: `${pathname}${searchStr}${hash}`,\n        state: replaceEqualDeep(previousLocation?.state, state),\n      }\n    }\n\n    const location = parse(locationToParse ?? this.history.location)\n\n    const { __tempLocation, __tempKey } = location.state\n\n    if (__tempLocation && (!__tempKey || __tempKey === this.tempLocationKey)) {\n      // Sync up the location keys\n      const parsedTempLocation = parse(__tempLocation) as any\n      parsedTempLocation.state.key = location.state.key\n\n      delete parsedTempLocation.state.__tempLocation\n\n      return {\n        ...parsedTempLocation,\n        maskedLocation: location,\n      }\n    }\n\n    return location\n  }\n\n  resolvePathWithBase = (from: string, path: string) => {\n    const resolvedPath = resolvePath({\n      basepath: this.basepath,\n      base: from,\n      to: cleanPath(path),\n      trailingSlash: this.options.trailingSlash,\n      caseSensitive: this.options.caseSensitive,\n    })\n    return resolvedPath\n  }\n\n  get looseRoutesById() {\n    return this.routesById as Record<string, AnyRoute>\n  }\n\n  /**\n  @deprecated use the following signature instead\n  ```ts\n  matchRoutes (\n    next: ParsedLocation,\n    opts?: { preload?: boolean; throwOnError?: boolean },\n  ): Array<AnyRouteMatch>;\n  ```\n*/\n  matchRoutes: MatchRoutesFn = (\n    pathnameOrNext: string | ParsedLocation,\n    locationSearchOrOpts?: AnySchema | MatchRoutesOpts,\n    opts?: MatchRoutesOpts,\n  ) => {\n    if (typeof pathnameOrNext === 'string') {\n      return this.matchRoutesInternal(\n        {\n          pathname: pathnameOrNext,\n          search: locationSearchOrOpts,\n        } as ParsedLocation,\n        opts,\n      )\n    } else {\n      return this.matchRoutesInternal(pathnameOrNext, locationSearchOrOpts)\n    }\n  }\n\n  private matchRoutesInternal(\n    next: ParsedLocation,\n    opts?: MatchRoutesOpts,\n  ): Array<AnyRouteMatch> {\n    const { foundRoute, matchedRoutes, routeParams } = this.getMatchedRoutes(\n      next,\n      opts?.dest,\n    )\n    let isGlobalNotFound = false\n\n    // Check to see if the route needs a 404 entry\n    if (\n      // If we found a route, and it's not an index route and we have left over path\n      foundRoute\n        ? foundRoute.path !== '/' && routeParams['**']\n        : // Or if we didn't find a route and we have left over path\n          trimPathRight(next.pathname)\n    ) {\n      // If the user has defined an (old) 404 route, use it\n      if (this.options.notFoundRoute) {\n        matchedRoutes.push(this.options.notFoundRoute)\n      } else {\n        // If there is no routes found during path matching\n        isGlobalNotFound = true\n      }\n    }\n\n    const globalNotFoundRouteId = (() => {\n      if (!isGlobalNotFound) {\n        return undefined\n      }\n\n      if (this.options.notFoundMode !== 'root') {\n        for (let i = matchedRoutes.length - 1; i >= 0; i--) {\n          const route = matchedRoutes[i]!\n          if (route.children) {\n            return route.id\n          }\n        }\n      }\n\n      return rootRouteId\n    })()\n\n    const parseErrors = matchedRoutes.map((route) => {\n      let parsedParamsError\n\n      const parseParams =\n        route.options.params?.parse ?? route.options.parseParams\n\n      if (parseParams) {\n        try {\n          const parsedParams = parseParams(routeParams)\n          // Add the parsed params to the accumulated params bag\n          Object.assign(routeParams, parsedParams)\n        } catch (err: any) {\n          parsedParamsError = new PathParamError(err.message, {\n            cause: err,\n          })\n\n          if (opts?.throwOnError) {\n            throw parsedParamsError\n          }\n\n          return parsedParamsError\n        }\n      }\n\n      return\n    })\n\n    const matches: Array<AnyRouteMatch> = []\n\n    const getParentContext = (parentMatch?: AnyRouteMatch) => {\n      const parentMatchId = parentMatch?.id\n\n      const parentContext = !parentMatchId\n        ? ((this.options.context as any) ?? {})\n        : (parentMatch.context ?? this.options.context ?? {})\n\n      return parentContext\n    }\n\n    matchedRoutes.forEach((route, index) => {\n      // Take each matched route and resolve + validate its search params\n      // This has to happen serially because each route's search params\n      // can depend on the parent route's search params\n      // It must also happen before we create the match so that we can\n      // pass the search params to the route's potential key function\n      // which is used to uniquely identify the route match in state\n\n      const parentMatch = matches[index - 1]\n\n      const [preMatchSearch, strictMatchSearch, searchError]: [\n        Record<string, any>,\n        Record<string, any>,\n        any,\n      ] = (() => {\n        // Validate the search params and stabilize them\n        const parentSearch = parentMatch?.search ?? next.search\n        const parentStrictSearch = parentMatch?._strictSearch ?? {}\n\n        try {\n          const strictSearch =\n            validateSearch(route.options.validateSearch, { ...parentSearch }) ??\n            {}\n\n          return [\n            {\n              ...parentSearch,\n              ...strictSearch,\n            },\n            { ...parentStrictSearch, ...strictSearch },\n            undefined,\n          ]\n        } catch (err: any) {\n          let searchParamError = err\n          if (!(err instanceof SearchParamError)) {\n            searchParamError = new SearchParamError(err.message, {\n              cause: err,\n            })\n          }\n\n          if (opts?.throwOnError) {\n            throw searchParamError\n          }\n\n          return [parentSearch, {}, searchParamError]\n        }\n      })()\n\n      // This is where we need to call route.options.loaderDeps() to get any additional\n      // deps that the route's loader function might need to run. We need to do this\n      // before we create the match so that we can pass the deps to the route's\n      // potential key function which is used to uniquely identify the route match in state\n\n      const loaderDeps =\n        route.options.loaderDeps?.({\n          search: preMatchSearch,\n        }) ?? ''\n\n      const loaderDepsHash = loaderDeps ? JSON.stringify(loaderDeps) : ''\n\n      const { usedParams, interpolatedPath } = interpolatePath({\n        path: route.fullPath,\n        params: routeParams,\n        decodeCharMap: this.pathParamsDecodeCharMap,\n      })\n\n      const matchId =\n        interpolatePath({\n          path: route.id,\n          params: routeParams,\n          leaveWildcards: true,\n          decodeCharMap: this.pathParamsDecodeCharMap,\n        }).interpolatedPath + loaderDepsHash\n\n      // Waste not, want not. If we already have a match for this route,\n      // reuse it. This is important for layout routes, which might stick\n      // around between navigation actions that only change leaf routes.\n\n      // Existing matches are matches that are already loaded along with\n      // pending matches that are still loading\n      const existingMatch = this.getMatch(matchId)\n\n      const previousMatch = this.state.matches.find(\n        (d) => d.routeId === route.id,\n      )\n\n      const cause = previousMatch ? 'stay' : 'enter'\n\n      let match: AnyRouteMatch\n\n      if (existingMatch) {\n        match = {\n          ...existingMatch,\n          cause,\n          params: previousMatch\n            ? replaceEqualDeep(previousMatch.params, routeParams)\n            : routeParams,\n          _strictParams: usedParams,\n          search: previousMatch\n            ? replaceEqualDeep(previousMatch.search, preMatchSearch)\n            : replaceEqualDeep(existingMatch.search, preMatchSearch),\n          _strictSearch: strictMatchSearch,\n        }\n      } else {\n        const status =\n          route.options.loader ||\n          route.options.beforeLoad ||\n          route.lazyFn ||\n          routeNeedsPreload(route)\n            ? 'pending'\n            : 'success'\n\n        match = {\n          id: matchId,\n          index,\n          routeId: route.id,\n          params: previousMatch\n            ? replaceEqualDeep(previousMatch.params, routeParams)\n            : routeParams,\n          _strictParams: usedParams,\n          pathname: joinPaths([this.basepath, interpolatedPath]),\n          updatedAt: Date.now(),\n          search: previousMatch\n            ? replaceEqualDeep(previousMatch.search, preMatchSearch)\n            : preMatchSearch,\n          _strictSearch: strictMatchSearch,\n          searchError: undefined,\n          status,\n          isFetching: false,\n          error: undefined,\n          paramsError: parseErrors[index],\n          __routeContext: {},\n          __beforeLoadContext: {},\n          context: {},\n          abortController: new AbortController(),\n          fetchCount: 0,\n          cause,\n          loaderDeps: previousMatch\n            ? replaceEqualDeep(previousMatch.loaderDeps, loaderDeps)\n            : loaderDeps,\n          invalid: false,\n          preload: false,\n          links: undefined,\n          scripts: undefined,\n          headScripts: undefined,\n          meta: undefined,\n          staticData: route.options.staticData || {},\n          loadPromise: createControlledPromise(),\n          fullPath: route.fullPath,\n        }\n      }\n\n      if (!opts?.preload) {\n        // If we have a global not found, mark the right match as global not found\n        match.globalNotFound = globalNotFoundRouteId === route.id\n      }\n\n      // update the searchError if there is one\n      match.searchError = searchError\n\n      const parentContext = getParentContext(parentMatch)\n\n      match.context = {\n        ...parentContext,\n        ...match.__routeContext,\n        ...match.__beforeLoadContext,\n      }\n\n      matches.push(match)\n    })\n\n    matches.forEach((match, index) => {\n      const route = this.looseRoutesById[match.routeId]!\n      const existingMatch = this.getMatch(match.id)\n\n      // only execute `context` if we are not just building a location\n      if (!existingMatch && opts?._buildLocation !== true) {\n        const parentMatch = matches[index - 1]\n        const parentContext = getParentContext(parentMatch)\n\n        // Update the match's context\n        const contextFnContext: RouteContextOptions<any, any, any, any> = {\n          deps: match.loaderDeps,\n          params: match.params,\n          context: parentContext,\n          location: next,\n          navigate: (opts: any) =>\n            this.navigate({ ...opts, _fromLocation: next }),\n          buildLocation: this.buildLocation,\n          cause: match.cause,\n          abortController: match.abortController,\n          preload: !!match.preload,\n          matches,\n        }\n\n        // Get the route context\n        match.__routeContext = route.options.context?.(contextFnContext) ?? {}\n\n        match.context = {\n          ...parentContext,\n          ...match.__routeContext,\n          ...match.__beforeLoadContext,\n        }\n      }\n    })\n\n    return matches\n  }\n\n  getMatchedRoutes: GetMatchRoutesFn = (next, dest) => {\n    let routeParams: Record<string, string> = {}\n    const trimmedPath = trimPathRight(next.pathname)\n    const getMatchedParams = (route: AnyRoute) => {\n      const result = matchPathname(this.basepath, trimmedPath, {\n        to: route.fullPath,\n        caseSensitive:\n          route.options.caseSensitive ?? this.options.caseSensitive,\n        fuzzy: true,\n      })\n      return result\n    }\n\n    let foundRoute: AnyRoute | undefined =\n      dest?.to !== undefined ? this.routesByPath[dest.to!] : undefined\n    if (foundRoute) {\n      routeParams = getMatchedParams(foundRoute)!\n    } else {\n      foundRoute = this.flatRoutes.find((route) => {\n        const matchedParams = getMatchedParams(route)\n\n        if (matchedParams) {\n          routeParams = matchedParams\n          return true\n        }\n\n        return false\n      })\n    }\n\n    let routeCursor: AnyRoute =\n      foundRoute || (this.routesById as any)[rootRouteId]\n\n    const matchedRoutes: Array<AnyRoute> = [routeCursor]\n\n    while (routeCursor.parentRoute) {\n      routeCursor = routeCursor.parentRoute\n      matchedRoutes.unshift(routeCursor)\n    }\n\n    return { matchedRoutes, routeParams, foundRoute }\n  }\n\n  cancelMatch = (id: string) => {\n    const match = this.getMatch(id)\n\n    if (!match) return\n\n    match.abortController.abort()\n    clearTimeout(match.pendingTimeout)\n  }\n\n  cancelMatches = () => {\n    this.state.pendingMatches?.forEach((match) => {\n      this.cancelMatch(match.id)\n    })\n  }\n\n  buildLocation: BuildLocationFn = (opts) => {\n    const build = (\n      dest: BuildNextOptions & {\n        unmaskOnReload?: boolean\n      } = {},\n      matchedRoutesResult?: MatchedRoutesResult,\n    ): ParsedLocation => {\n      const fromMatches = dest._fromLocation\n        ? this.matchRoutes(dest._fromLocation, { _buildLocation: true })\n        : this.state.matches\n\n      const fromMatch =\n        dest.from != null\n          ? fromMatches.find((d) =>\n              matchPathname(this.basepath, trimPathRight(d.pathname), {\n                to: dest.from,\n                caseSensitive: false,\n                fuzzy: false,\n              }),\n            )\n          : undefined\n\n      const fromPath = fromMatch?.pathname || this.latestLocation.pathname\n\n      invariant(\n        dest.from == null || fromMatch != null,\n        'Could not find match for from: ' + dest.from,\n      )\n\n      const fromSearch = this.state.pendingMatches?.length\n        ? last(this.state.pendingMatches)?.search\n        : last(fromMatches)?.search || this.latestLocation.search\n\n      const stayingMatches = matchedRoutesResult?.matchedRoutes.filter((d) =>\n        fromMatches.find((e) => e.routeId === d.id),\n      )\n      let pathname: string\n      if (dest.to) {\n        const resolvePathTo =\n          fromMatch?.fullPath ||\n          last(fromMatches)?.fullPath ||\n          this.latestLocation.pathname\n        pathname = this.resolvePathWithBase(resolvePathTo, `${dest.to}`)\n      } else {\n        const fromRouteByFromPathRouteId =\n          this.routesById[\n            stayingMatches?.find((route) => {\n              const interpolatedPath = interpolatePath({\n                path: route.fullPath,\n                params: matchedRoutesResult?.routeParams ?? {},\n                decodeCharMap: this.pathParamsDecodeCharMap,\n              }).interpolatedPath\n              const pathname = joinPaths([this.basepath, interpolatedPath])\n              return pathname === fromPath\n            })?.id as keyof this['routesById']\n          ]\n        pathname = this.resolvePathWithBase(\n          fromPath,\n          fromRouteByFromPathRouteId?.to ?? fromPath,\n        )\n      }\n\n      const prevParams = { ...last(fromMatches)?.params }\n\n      let nextParams =\n        (dest.params ?? true) === true\n          ? prevParams\n          : {\n              ...prevParams,\n              ...functionalUpdate(dest.params as any, prevParams),\n            }\n\n      if (Object.keys(nextParams).length > 0) {\n        matchedRoutesResult?.matchedRoutes\n          .map((route) => {\n            return (\n              route.options.params?.stringify ?? route.options.stringifyParams\n            )\n          })\n          .filter(Boolean)\n          .forEach((fn) => {\n            nextParams = { ...nextParams!, ...fn!(nextParams) }\n          })\n      }\n\n      pathname = interpolatePath({\n        path: pathname,\n        params: nextParams ?? {},\n        leaveWildcards: false,\n        leaveParams: opts.leaveParams,\n        decodeCharMap: this.pathParamsDecodeCharMap,\n      }).interpolatedPath\n\n      let search = fromSearch\n      if (opts._includeValidateSearch && this.options.search?.strict) {\n        let validatedSearch = {}\n        matchedRoutesResult?.matchedRoutes.forEach((route) => {\n          try {\n            if (route.options.validateSearch) {\n              validatedSearch = {\n                ...validatedSearch,\n                ...(validateSearch(route.options.validateSearch, {\n                  ...validatedSearch,\n                  ...search,\n                }) ?? {}),\n              }\n            }\n          } catch {\n            // ignore errors here because they are already handled in matchRoutes\n          }\n        })\n        search = validatedSearch\n      }\n\n      const applyMiddlewares = (search: any) => {\n        const allMiddlewares =\n          matchedRoutesResult?.matchedRoutes.reduce(\n            (acc, route) => {\n              const middlewares: Array<SearchMiddleware<any>> = []\n              if ('search' in route.options) {\n                if (route.options.search?.middlewares) {\n                  middlewares.push(...route.options.search.middlewares)\n                }\n              }\n              // TODO remove preSearchFilters and postSearchFilters in v2\n              else if (\n                route.options.preSearchFilters ||\n                route.options.postSearchFilters\n              ) {\n                const legacyMiddleware: SearchMiddleware<any> = ({\n                  search,\n                  next,\n                }) => {\n                  let nextSearch = search\n                  if (\n                    'preSearchFilters' in route.options &&\n                    route.options.preSearchFilters\n                  ) {\n                    nextSearch = route.options.preSearchFilters.reduce(\n                      (prev, next) => next(prev),\n                      search,\n                    )\n                  }\n                  const result = next(nextSearch)\n                  if (\n                    'postSearchFilters' in route.options &&\n                    route.options.postSearchFilters\n                  ) {\n                    return route.options.postSearchFilters.reduce(\n                      (prev, next) => next(prev),\n                      result,\n                    )\n                  }\n                  return result\n                }\n                middlewares.push(legacyMiddleware)\n              }\n              if (opts._includeValidateSearch && route.options.validateSearch) {\n                const validate: SearchMiddleware<any> = ({ search, next }) => {\n                  const result = next(search)\n                  try {\n                    const validatedSearch = {\n                      ...result,\n                      ...(validateSearch(\n                        route.options.validateSearch,\n                        result,\n                      ) ?? {}),\n                    }\n                    return validatedSearch\n                  } catch {\n                    // ignore errors here because they are already handled in matchRoutes\n                    return result\n                  }\n                }\n                middlewares.push(validate)\n              }\n              return acc.concat(middlewares)\n            },\n            [] as Array<SearchMiddleware<any>>,\n          ) ?? []\n\n        // the chain ends here since `next` is not called\n        const final: SearchMiddleware<any> = ({ search }) => {\n          if (!dest.search) {\n            return {}\n          }\n          if (dest.search === true) {\n            return search\n          }\n          return functionalUpdate(dest.search, search)\n        }\n        allMiddlewares.push(final)\n\n        const applyNext = (index: number, currentSearch: any): any => {\n          // no more middlewares left, return the current search\n          if (index >= allMiddlewares.length) {\n            return currentSearch\n          }\n\n          const middleware = allMiddlewares[index]!\n\n          const next = (newSearch: any): any => {\n            return applyNext(index + 1, newSearch)\n          }\n\n          return middleware({ search: currentSearch, next })\n        }\n\n        // Start applying middlewares\n        return applyNext(0, search)\n      }\n\n      search = applyMiddlewares(search)\n\n      search = replaceEqualDeep(fromSearch, search)\n      const searchStr = this.options.stringifySearch(search)\n\n      const hash =\n        dest.hash === true\n          ? this.latestLocation.hash\n          : dest.hash\n            ? functionalUpdate(dest.hash, this.latestLocation.hash)\n            : undefined\n\n      const hashStr = hash ? `#${hash}` : ''\n\n      let nextState =\n        dest.state === true\n          ? this.latestLocation.state\n          : dest.state\n            ? functionalUpdate(dest.state, this.latestLocation.state)\n            : {}\n\n      nextState = replaceEqualDeep(this.latestLocation.state, nextState)\n\n      return {\n        pathname,\n        search,\n        searchStr,\n        state: nextState as any,\n        hash: hash ?? '',\n        href: `${pathname}${searchStr}${hashStr}`,\n        unmaskOnReload: dest.unmaskOnReload,\n      }\n    }\n\n    const buildWithMatches = (\n      dest: BuildNextOptions = {},\n      maskedDest?: BuildNextOptions,\n    ) => {\n      const next = build(dest)\n      let maskedNext = maskedDest ? build(maskedDest) : undefined\n\n      if (!maskedNext) {\n        let params = {}\n\n        const foundMask = this.options.routeMasks?.find((d) => {\n          const match = matchPathname(this.basepath, next.pathname, {\n            to: d.from,\n            caseSensitive: false,\n            fuzzy: false,\n          })\n\n          if (match) {\n            params = match\n            return true\n          }\n\n          return false\n        })\n\n        if (foundMask) {\n          const { from: _from, ...maskProps } = foundMask\n          maskedDest = {\n            ...pick(opts, ['from']),\n            ...maskProps,\n            params,\n          }\n          maskedNext = build(maskedDest)\n        }\n      }\n\n      const nextMatches = this.getMatchedRoutes(next, dest)\n      const final = build(dest, nextMatches)\n\n      if (maskedNext) {\n        const maskedMatches = this.getMatchedRoutes(maskedNext, maskedDest)\n        const maskedFinal = build(maskedDest, maskedMatches)\n        final.maskedLocation = maskedFinal\n      }\n\n      return final\n    }\n\n    if (opts.mask) {\n      return buildWithMatches(opts, {\n        ...pick(opts, ['from']),\n        ...opts.mask,\n      })\n    }\n\n    return buildWithMatches(opts)\n  }\n\n  commitLocationPromise: undefined | ControlledPromise<void>\n\n  commitLocation: CommitLocationFn = ({\n    viewTransition,\n    ignoreBlocker,\n    ...next\n  }) => {\n    const isSameState = () => {\n      // the following props are ignored but may still be provided when navigating,\n      // temporarily add the previous values to the next state so they don't affect\n      // the comparison\n      const ignoredProps = [\n        'key',\n        '__TSR_index',\n        '__hashScrollIntoViewOptions',\n      ] as const\n      ignoredProps.forEach((prop) => {\n        ;(next.state as any)[prop] = this.latestLocation.state[prop]\n      })\n      const isEqual = deepEqual(next.state, this.latestLocation.state)\n      ignoredProps.forEach((prop) => {\n        delete next.state[prop]\n      })\n      return isEqual\n    }\n\n    const isSameUrl = this.latestLocation.href === next.href\n\n    const previousCommitPromise = this.commitLocationPromise\n    this.commitLocationPromise = createControlledPromise<void>(() => {\n      previousCommitPromise?.resolve()\n    })\n\n    // Don't commit to history if nothing changed\n    if (isSameUrl && isSameState()) {\n      this.load()\n    } else {\n      // eslint-disable-next-line prefer-const\n      let { maskedLocation, hashScrollIntoView, ...nextHistory } = next\n\n      if (maskedLocation) {\n        nextHistory = {\n          ...maskedLocation,\n          state: {\n            ...maskedLocation.state,\n            __tempKey: undefined,\n            __tempLocation: {\n              ...nextHistory,\n              search: nextHistory.searchStr,\n              state: {\n                ...nextHistory.state,\n                __tempKey: undefined!,\n                __tempLocation: undefined!,\n                key: undefined!,\n              },\n            },\n          },\n        }\n\n        if (\n          nextHistory.unmaskOnReload ??\n          this.options.unmaskOnReload ??\n          false\n        ) {\n          nextHistory.state.__tempKey = this.tempLocationKey\n        }\n      }\n\n      nextHistory.state.__hashScrollIntoViewOptions =\n        hashScrollIntoView ?? this.options.defaultHashScrollIntoView ?? true\n\n      this.shouldViewTransition = viewTransition\n\n      this.history[next.replace ? 'replace' : 'push'](\n        nextHistory.href,\n        nextHistory.state,\n        { ignoreBlocker },\n      )\n    }\n\n    this.resetNextScroll = next.resetScroll ?? true\n\n    if (!this.history.subscribers.size) {\n      this.load()\n    }\n\n    return this.commitLocationPromise\n  }\n\n  buildAndCommitLocation = ({\n    replace,\n    resetScroll,\n    hashScrollIntoView,\n    viewTransition,\n    ignoreBlocker,\n    href,\n    ...rest\n  }: BuildNextOptions & CommitLocationOptions = {}) => {\n    if (href) {\n      const currentIndex = this.history.location.state.__TSR_index\n      const parsed = parseHref(href, {\n        __TSR_index: replace ? currentIndex : currentIndex + 1,\n      })\n      rest.to = parsed.pathname\n      rest.search = this.options.parseSearch(parsed.search)\n      // remove the leading `#` from the hash\n      rest.hash = parsed.hash.slice(1)\n    }\n\n    const location = this.buildLocation({\n      ...(rest as any),\n      _includeValidateSearch: true,\n    })\n    return this.commitLocation({\n      ...location,\n      viewTransition,\n      replace,\n      resetScroll,\n      hashScrollIntoView,\n      ignoreBlocker,\n    })\n  }\n\n  navigate: NavigateFn = ({ to, reloadDocument, href, ...rest }) => {\n    if (reloadDocument) {\n      if (!href) {\n        const location = this.buildLocation({ to, ...rest } as any)\n        href = this.history.createHref(location.href)\n      }\n      if (rest.replace) {\n        window.location.replace(href)\n      } else {\n        window.location.href = href\n      }\n      return\n    }\n\n    return this.buildAndCommitLocation({\n      ...rest,\n      href,\n      to: to as string,\n    })\n  }\n\n  latestLoadPromise: undefined | Promise<void>\n\n  load: LoadFn = async (opts?: { sync?: boolean }): Promise<void> => {\n    this.latestLocation = this.parseLocation(this.latestLocation)\n\n    let redirect: ResolvedRedirect | undefined\n    let notFound: NotFoundError | undefined\n\n    let loadPromise: Promise<void>\n\n    // eslint-disable-next-line prefer-const\n    loadPromise = new Promise<void>((resolve) => {\n      this.startTransition(async () => {\n        try {\n          const next = this.latestLocation\n          const prevLocation = this.state.resolvedLocation\n\n          // Cancel any pending matches\n          this.cancelMatches()\n\n          let pendingMatches!: Array<AnyRouteMatch>\n\n          batch(() => {\n            // this call breaks a route context of destination route after a redirect\n            // we should be fine not eagerly calling this since we call it later\n            // this.clearExpiredCache()\n\n            // Match the routes\n            pendingMatches = this.matchRoutes(next)\n\n            // Ingest the new matches\n            this.__store.setState((s) => ({\n              ...s,\n              status: 'pending',\n              isLoading: true,\n              location: next,\n              pendingMatches,\n              // If a cached moved to pendingMatches, remove it from cachedMatches\n              cachedMatches: s.cachedMatches.filter((d) => {\n                return !pendingMatches.find((e) => e.id === d.id)\n              }),\n            }))\n          })\n\n          if (!this.state.redirect) {\n            this.emit({\n              type: 'onBeforeNavigate',\n              ...getLocationChangeInfo({\n                resolvedLocation: prevLocation,\n                location: next,\n              }),\n            })\n          }\n\n          this.emit({\n            type: 'onBeforeLoad',\n            ...getLocationChangeInfo({\n              resolvedLocation: prevLocation,\n              location: next,\n            }),\n          })\n\n          await this.loadMatches({\n            sync: opts?.sync,\n            matches: pendingMatches,\n            location: next,\n            // eslint-disable-next-line @typescript-eslint/require-await\n            onReady: async () => {\n              // eslint-disable-next-line @typescript-eslint/require-await\n              this.startViewTransition(async () => {\n                // this.viewTransitionPromise = createControlledPromise<true>()\n\n                // Commit the pending matches. If a previous match was\n                // removed, place it in the cachedMatches\n                let exitingMatches!: Array<AnyRouteMatch>\n                let enteringMatches!: Array<AnyRouteMatch>\n                let stayingMatches!: Array<AnyRouteMatch>\n\n                batch(() => {\n                  this.__store.setState((s) => {\n                    const previousMatches = s.matches\n                    const newMatches = s.pendingMatches || s.matches\n\n                    exitingMatches = previousMatches.filter(\n                      (match) => !newMatches.find((d) => d.id === match.id),\n                    )\n                    enteringMatches = newMatches.filter(\n                      (match) =>\n                        !previousMatches.find((d) => d.id === match.id),\n                    )\n                    stayingMatches = previousMatches.filter((match) =>\n                      newMatches.find((d) => d.id === match.id),\n                    )\n\n                    return {\n                      ...s,\n                      isLoading: false,\n                      loadedAt: Date.now(),\n                      matches: newMatches,\n                      pendingMatches: undefined,\n                      cachedMatches: [\n                        ...s.cachedMatches,\n                        ...exitingMatches.filter((d) => d.status !== 'error'),\n                      ],\n                    }\n                  })\n                  this.clearExpiredCache()\n                })\n\n                //\n                ;(\n                  [\n                    [exitingMatches, 'onLeave'],\n                    [enteringMatches, 'onEnter'],\n                    [stayingMatches, 'onStay'],\n                  ] as const\n                ).forEach(([matches, hook]) => {\n                  matches.forEach((match) => {\n                    this.looseRoutesById[match.routeId]!.options[hook]?.(match)\n                  })\n                })\n              })\n            },\n          })\n        } catch (err) {\n          if (isResolvedRedirect(err)) {\n            redirect = err\n            if (!this.isServer) {\n              this.navigate({\n                ...redirect,\n                replace: true,\n                ignoreBlocker: true,\n              })\n            }\n          } else if (isNotFound(err)) {\n            notFound = err\n          }\n\n          this.__store.setState((s) => ({\n            ...s,\n            statusCode: redirect\n              ? redirect.statusCode\n              : notFound\n                ? 404\n                : s.matches.some((d) => d.status === 'error')\n                  ? 500\n                  : 200,\n            redirect,\n          }))\n        }\n\n        if (this.latestLoadPromise === loadPromise) {\n          this.commitLocationPromise?.resolve()\n          this.latestLoadPromise = undefined\n          this.commitLocationPromise = undefined\n        }\n        resolve()\n      })\n    })\n\n    this.latestLoadPromise = loadPromise\n\n    await loadPromise\n\n    while (\n      (this.latestLoadPromise as any) &&\n      loadPromise !== this.latestLoadPromise\n    ) {\n      await this.latestLoadPromise\n    }\n\n    if (this.hasNotFoundMatch()) {\n      this.__store.setState((s) => ({\n        ...s,\n        statusCode: 404,\n      }))\n    }\n  }\n\n  startViewTransition = (fn: () => Promise<void>) => {\n    // Determine if we should start a view transition from the navigation\n    // or from the router default\n    const shouldViewTransition =\n      this.shouldViewTransition ?? this.options.defaultViewTransition\n\n    // Reset the view transition flag\n    delete this.shouldViewTransition\n    // Attempt to start a view transition (or just apply the changes if we can't)\n    if (\n      shouldViewTransition &&\n      typeof document !== 'undefined' &&\n      'startViewTransition' in document &&\n      typeof document.startViewTransition === 'function'\n    ) {\n      // lib.dom.ts doesn't support viewTransition types variant yet.\n      // TODO: Fix this when dom types are updated\n      let startViewTransitionParams: any\n\n      if (\n        typeof shouldViewTransition === 'object' &&\n        this.isViewTransitionTypesSupported\n      ) {\n        const next = this.latestLocation\n        const prevLocation = this.state.resolvedLocation\n\n        const resolvedViewTransitionTypes =\n          typeof shouldViewTransition.types === 'function'\n            ? shouldViewTransition.types(\n                getLocationChangeInfo({\n                  resolvedLocation: prevLocation,\n                  location: next,\n                }),\n              )\n            : shouldViewTransition.types\n\n        startViewTransitionParams = {\n          update: fn,\n          types: resolvedViewTransitionTypes,\n        }\n      } else {\n        startViewTransitionParams = fn\n      }\n\n      document.startViewTransition(startViewTransitionParams)\n    } else {\n      fn()\n    }\n  }\n\n  updateMatch: UpdateMatchFn = (id, updater) => {\n    let updated!: AnyRouteMatch\n    const isPending = this.state.pendingMatches?.find((d) => d.id === id)\n    const isMatched = this.state.matches.find((d) => d.id === id)\n    const isCached = this.state.cachedMatches.find((d) => d.id === id)\n\n    const matchesKey = isPending\n      ? 'pendingMatches'\n      : isMatched\n        ? 'matches'\n        : isCached\n          ? 'cachedMatches'\n          : ''\n\n    if (matchesKey) {\n      this.__store.setState((s) => ({\n        ...s,\n        [matchesKey]: s[matchesKey]?.map((d) =>\n          d.id === id ? (updated = updater(d)) : d,\n        ),\n      }))\n    }\n\n    return updated\n  }\n\n  getMatch: GetMatchFn = (matchId: string) => {\n    return [\n      ...this.state.cachedMatches,\n      ...(this.state.pendingMatches ?? []),\n      ...this.state.matches,\n    ].find((d) => d.id === matchId)\n  }\n\n  loadMatches = async ({\n    location,\n    matches,\n    preload: allPreload,\n    onReady,\n    updateMatch = this.updateMatch,\n    sync,\n  }: {\n    location: ParsedLocation\n    matches: Array<AnyRouteMatch>\n    preload?: boolean\n    onReady?: () => Promise<void>\n    updateMatch?: (\n      id: string,\n      updater: (match: AnyRouteMatch) => AnyRouteMatch,\n    ) => void\n    getMatch?: (matchId: string) => AnyRouteMatch | undefined\n    sync?: boolean\n  }): Promise<Array<MakeRouteMatch>> => {\n    let firstBadMatchIndex: number | undefined\n    let rendered = false\n\n    const triggerOnReady = async () => {\n      if (!rendered) {\n        rendered = true\n        await onReady?.()\n      }\n    }\n\n    const resolvePreload = (matchId: string) => {\n      return !!(allPreload && !this.state.matches.find((d) => d.id === matchId))\n    }\n\n    const handleRedirectAndNotFound = (match: AnyRouteMatch, err: any) => {\n      if (isResolvedRedirect(err)) {\n        if (!err.reloadDocument) {\n          throw err\n        }\n      }\n\n      if (isRedirect(err) || isNotFound(err)) {\n        updateMatch(match.id, (prev) => ({\n          ...prev,\n          status: isRedirect(err)\n            ? 'redirected'\n            : isNotFound(err)\n              ? 'notFound'\n              : 'error',\n          isFetching: false,\n          error: err,\n          beforeLoadPromise: undefined,\n          loaderPromise: undefined,\n        }))\n\n        if (!(err as any).routeId) {\n          ;(err as any).routeId = match.routeId\n        }\n\n        match.beforeLoadPromise?.resolve()\n        match.loaderPromise?.resolve()\n        match.loadPromise?.resolve()\n\n        if (isRedirect(err)) {\n          rendered = true\n          err = this.resolveRedirect({ ...err, _fromLocation: location })\n          throw err\n        } else if (isNotFound(err)) {\n          this._handleNotFound(matches, err, {\n            updateMatch,\n          })\n          this.serverSsr?.onMatchSettled({\n            router: this,\n            match: this.getMatch(match.id)!,\n          })\n          throw err\n        }\n      }\n    }\n\n    try {\n      await new Promise<void>((resolveAll, rejectAll) => {\n        ;(async () => {\n          try {\n            const handleSerialError = (\n              index: number,\n              err: any,\n              routerCode: string,\n            ) => {\n              const { id: matchId, routeId } = matches[index]!\n              const route = this.looseRoutesById[routeId]!\n\n              // Much like suspense, we use a promise here to know if\n              // we've been outdated by a new loadMatches call and\n              // should abort the current async operation\n              if (err instanceof Promise) {\n                throw err\n              }\n\n              err.routerCode = routerCode\n              firstBadMatchIndex = firstBadMatchIndex ?? index\n              handleRedirectAndNotFound(this.getMatch(matchId)!, err)\n\n              try {\n                route.options.onError?.(err)\n              } catch (errorHandlerErr) {\n                err = errorHandlerErr\n                handleRedirectAndNotFound(this.getMatch(matchId)!, err)\n              }\n\n              updateMatch(matchId, (prev) => {\n                prev.beforeLoadPromise?.resolve()\n                prev.loadPromise?.resolve()\n\n                return {\n                  ...prev,\n                  error: err,\n                  status: 'error',\n                  isFetching: false,\n                  updatedAt: Date.now(),\n                  abortController: new AbortController(),\n                  beforeLoadPromise: undefined,\n                }\n              })\n            }\n\n            for (const [index, { id: matchId, routeId }] of matches.entries()) {\n              const existingMatch = this.getMatch(matchId)!\n              const parentMatchId = matches[index - 1]?.id\n\n              const route = this.looseRoutesById[routeId]!\n\n              const pendingMs =\n                route.options.pendingMs ?? this.options.defaultPendingMs\n\n              const shouldPending = !!(\n                onReady &&\n                !this.isServer &&\n                !resolvePreload(matchId) &&\n                (route.options.loader ||\n                  route.options.beforeLoad ||\n                  routeNeedsPreload(route)) &&\n                typeof pendingMs === 'number' &&\n                pendingMs !== Infinity &&\n                (route.options.pendingComponent ??\n                  (this.options as any)?.defaultPendingComponent)\n              )\n\n              let executeBeforeLoad = true\n              if (\n                // If we are in the middle of a load, either of these will be present\n                // (not to be confused with `loadPromise`, which is always defined)\n                existingMatch.beforeLoadPromise ||\n                existingMatch.loaderPromise\n              ) {\n                if (shouldPending) {\n                  setTimeout(() => {\n                    try {\n                      // Update the match and prematurely resolve the loadMatches promise so that\n                      // the pending component can start rendering\n                      triggerOnReady()\n                    } catch {}\n                  }, pendingMs)\n                }\n\n                // Wait for the beforeLoad to resolve before we continue\n                await existingMatch.beforeLoadPromise\n                executeBeforeLoad = this.getMatch(matchId)!.status !== 'success'\n              }\n              if (executeBeforeLoad) {\n                // If we are not in the middle of a load OR the previous load failed, start it\n                try {\n                  updateMatch(matchId, (prev) => {\n                    // explicitly capture the previous loadPromise\n                    const prevLoadPromise = prev.loadPromise\n                    return {\n                      ...prev,\n                      loadPromise: createControlledPromise<void>(() => {\n                        prevLoadPromise?.resolve()\n                      }),\n                      beforeLoadPromise: createControlledPromise<void>(),\n                    }\n                  })\n                  const abortController = new AbortController()\n\n                  let pendingTimeout: ReturnType<typeof setTimeout>\n\n                  if (shouldPending) {\n                    // If we might show a pending component, we need to wait for the\n                    // pending promise to resolve before we start showing that state\n                    pendingTimeout = setTimeout(() => {\n                      try {\n                        // Update the match and prematurely resolve the loadMatches promise so that\n                        // the pending component can start rendering\n                        triggerOnReady()\n                      } catch {}\n                    }, pendingMs)\n                  }\n\n                  const { paramsError, searchError } = this.getMatch(matchId)!\n\n                  if (paramsError) {\n                    handleSerialError(index, paramsError, 'PARSE_PARAMS')\n                  }\n\n                  if (searchError) {\n                    handleSerialError(index, searchError, 'VALIDATE_SEARCH')\n                  }\n\n                  const getParentMatchContext = () =>\n                    parentMatchId\n                      ? this.getMatch(parentMatchId)!.context\n                      : (this.options.context ?? {})\n\n                  updateMatch(matchId, (prev) => ({\n                    ...prev,\n                    isFetching: 'beforeLoad',\n                    fetchCount: prev.fetchCount + 1,\n                    abortController,\n                    pendingTimeout,\n                    context: {\n                      ...getParentMatchContext(),\n                      ...prev.__routeContext,\n                    },\n                  }))\n\n                  const { search, params, context, cause } =\n                    this.getMatch(matchId)!\n\n                  const preload = resolvePreload(matchId)\n\n                  const beforeLoadFnContext: BeforeLoadContextOptions<\n                    any,\n                    any,\n                    any,\n                    any,\n                    any\n                  > = {\n                    search,\n                    abortController,\n                    params,\n                    preload,\n                    context,\n                    location,\n                    navigate: (opts: any) =>\n                      this.navigate({ ...opts, _fromLocation: location }),\n                    buildLocation: this.buildLocation,\n                    cause: preload ? 'preload' : cause,\n                    matches,\n                  }\n\n                  const beforeLoadContext =\n                    (await route.options.beforeLoad?.(beforeLoadFnContext)) ??\n                    {}\n\n                  if (\n                    isRedirect(beforeLoadContext) ||\n                    isNotFound(beforeLoadContext)\n                  ) {\n                    handleSerialError(index, beforeLoadContext, 'BEFORE_LOAD')\n                  }\n\n                  updateMatch(matchId, (prev) => {\n                    return {\n                      ...prev,\n                      __beforeLoadContext: beforeLoadContext,\n                      context: {\n                        ...getParentMatchContext(),\n                        ...prev.__routeContext,\n                        ...beforeLoadContext,\n                      },\n                      abortController,\n                    }\n                  })\n                } catch (err) {\n                  handleSerialError(index, err, 'BEFORE_LOAD')\n                }\n\n                updateMatch(matchId, (prev) => {\n                  prev.beforeLoadPromise?.resolve()\n\n                  return {\n                    ...prev,\n                    beforeLoadPromise: undefined,\n                    isFetching: false,\n                  }\n                })\n              }\n            }\n\n            const validResolvedMatches = matches.slice(0, firstBadMatchIndex)\n            const matchPromises: Array<Promise<AnyRouteMatch>> = []\n\n            validResolvedMatches.forEach(({ id: matchId, routeId }, index) => {\n              matchPromises.push(\n                (async () => {\n                  const { loaderPromise: prevLoaderPromise } =\n                    this.getMatch(matchId)!\n\n                  let loaderShouldRunAsync = false\n                  let loaderIsRunningAsync = false\n\n                  if (prevLoaderPromise) {\n                    await prevLoaderPromise\n                    const match = this.getMatch(matchId)!\n                    if (match.error) {\n                      handleRedirectAndNotFound(match, match.error)\n                    }\n                  } else {\n                    const parentMatchPromise = matchPromises[index - 1] as any\n                    const route = this.looseRoutesById[routeId]!\n\n                    const getLoaderContext = (): LoaderFnContext => {\n                      const {\n                        params,\n                        loaderDeps,\n                        abortController,\n                        context,\n                        cause,\n                      } = this.getMatch(matchId)!\n\n                      const preload = resolvePreload(matchId)\n\n                      return {\n                        params,\n                        deps: loaderDeps,\n                        preload: !!preload,\n                        parentMatchPromise,\n                        abortController: abortController,\n                        context,\n                        location,\n                        navigate: (opts) =>\n                          this.navigate({ ...opts, _fromLocation: location }),\n                        cause: preload ? 'preload' : cause,\n                        route,\n                      }\n                    }\n\n                    // This is where all of the stale-while-revalidate magic happens\n                    const age = Date.now() - this.getMatch(matchId)!.updatedAt\n\n                    const preload = resolvePreload(matchId)\n\n                    const staleAge = preload\n                      ? (route.options.preloadStaleTime ??\n                        this.options.defaultPreloadStaleTime ??\n                        30_000) // 30 seconds for preloads by default\n                      : (route.options.staleTime ??\n                        this.options.defaultStaleTime ??\n                        0)\n\n                    const shouldReloadOption = route.options.shouldReload\n\n                    // Default to reloading the route all the time\n                    // Allow shouldReload to get the last say,\n                    // if provided.\n                    const shouldReload =\n                      typeof shouldReloadOption === 'function'\n                        ? shouldReloadOption(getLoaderContext())\n                        : shouldReloadOption\n\n                    updateMatch(matchId, (prev) => ({\n                      ...prev,\n                      loaderPromise: createControlledPromise<void>(),\n                      preload:\n                        !!preload &&\n                        !this.state.matches.find((d) => d.id === matchId),\n                    }))\n\n                    const executeHead = () => {\n                      const match = this.getMatch(matchId)\n                      // in case of a redirecting match during preload, the match does not exist\n                      if (!match) {\n                        return\n                      }\n                      const assetContext = {\n                        matches,\n                        match,\n                        params: match.params,\n                        loaderData: match.loaderData,\n                      }\n                      const headFnContent = route.options.head?.(assetContext)\n                      const meta = headFnContent?.meta\n                      const links = headFnContent?.links\n                      const headScripts = headFnContent?.scripts\n\n                      const scripts = route.options.scripts?.(assetContext)\n                      const headers = route.options.headers?.(assetContext)\n                      updateMatch(matchId, (prev) => ({\n                        ...prev,\n                        meta,\n                        links,\n                        headScripts,\n                        headers,\n                        scripts,\n                      }))\n                    }\n\n                    const runLoader = async () => {\n                      try {\n                        // If the Matches component rendered\n                        // the pending component and needs to show it for\n                        // a minimum duration, we''ll wait for it to resolve\n                        // before committing to the match and resolving\n                        // the loadPromise\n                        const potentialPendingMinPromise = async () => {\n                          const latestMatch = this.getMatch(matchId)!\n\n                          if (latestMatch.minPendingPromise) {\n                            await latestMatch.minPendingPromise\n                          }\n                        }\n\n                        // Actually run the loader and handle the result\n                        try {\n                          this.loadRouteChunk(route)\n\n                          updateMatch(matchId, (prev) => ({\n                            ...prev,\n                            isFetching: 'loader',\n                          }))\n\n                          // Kick off the loader!\n                          const loaderData =\n                            await route.options.loader?.(getLoaderContext())\n\n                          handleRedirectAndNotFound(\n                            this.getMatch(matchId)!,\n                            loaderData,\n                          )\n\n                          // Lazy option can modify the route options,\n                          // so we need to wait for it to resolve before\n                          // we can use the options\n                          await route._lazyPromise\n\n                          await potentialPendingMinPromise()\n\n                          // Last but not least, wait for the the components\n                          // to be preloaded before we resolve the match\n                          await route._componentsPromise\n\n                          batch(() => {\n                            updateMatch(matchId, (prev) => ({\n                              ...prev,\n                              error: undefined,\n                              status: 'success',\n                              isFetching: false,\n                              updatedAt: Date.now(),\n                              loaderData,\n                            }))\n                            executeHead()\n                          })\n                        } catch (e) {\n                          let error = e\n\n                          await potentialPendingMinPromise()\n\n                          handleRedirectAndNotFound(this.getMatch(matchId)!, e)\n\n                          try {\n                            route.options.onError?.(e)\n                          } catch (onErrorError) {\n                            error = onErrorError\n                            handleRedirectAndNotFound(\n                              this.getMatch(matchId)!,\n                              onErrorError,\n                            )\n                          }\n\n                          batch(() => {\n                            updateMatch(matchId, (prev) => ({\n                              ...prev,\n                              error,\n                              status: 'error',\n                              isFetching: false,\n                            }))\n                            executeHead()\n                          })\n                        }\n\n                        this.serverSsr?.onMatchSettled({\n                          router: this,\n                          match: this.getMatch(matchId)!,\n                        })\n                      } catch (err) {\n                        batch(() => {\n                          updateMatch(matchId, (prev) => ({\n                            ...prev,\n                            loaderPromise: undefined,\n                          }))\n                          executeHead()\n                        })\n                        handleRedirectAndNotFound(this.getMatch(matchId)!, err)\n                      }\n                    }\n\n                    // If the route is successful and still fresh, just resolve\n                    const { status, invalid } = this.getMatch(matchId)!\n                    loaderShouldRunAsync =\n                      status === 'success' &&\n                      (invalid || (shouldReload ?? age > staleAge))\n                    if (preload && route.options.preload === false) {\n                      // Do nothing\n                    } else if (loaderShouldRunAsync && !sync) {\n                      loaderIsRunningAsync = true\n                      ;(async () => {\n                        try {\n                          await runLoader()\n                          const { loaderPromise, loadPromise } =\n                            this.getMatch(matchId)!\n                          loaderPromise?.resolve()\n                          loadPromise?.resolve()\n                          updateMatch(matchId, (prev) => ({\n                            ...prev,\n                            loaderPromise: undefined,\n                          }))\n                        } catch (err) {\n                          if (isResolvedRedirect(err)) {\n                            await this.navigate(err)\n                          }\n                        }\n                      })()\n                    } else if (\n                      status !== 'success' ||\n                      (loaderShouldRunAsync && sync)\n                    ) {\n                      await runLoader()\n                    } else {\n                      // if the loader did not run, still update head.\n                      // reason: parent's beforeLoad may have changed the route context\n                      // and only now do we know the route context (and that the loader would not run)\n                      executeHead()\n                    }\n                  }\n                  if (!loaderIsRunningAsync) {\n                    const { loaderPromise, loadPromise } =\n                      this.getMatch(matchId)!\n                    loaderPromise?.resolve()\n                    loadPromise?.resolve()\n                  }\n\n                  updateMatch(matchId, (prev) => ({\n                    ...prev,\n                    isFetching: loaderIsRunningAsync ? prev.isFetching : false,\n                    loaderPromise: loaderIsRunningAsync\n                      ? prev.loaderPromise\n                      : undefined,\n                    invalid: false,\n                  }))\n                  return this.getMatch(matchId)!\n                })(),\n              )\n            })\n\n            await Promise.all(matchPromises)\n\n            resolveAll()\n          } catch (err) {\n            rejectAll(err)\n          }\n        })()\n      })\n      await triggerOnReady()\n    } catch (err) {\n      if (isRedirect(err) || isNotFound(err)) {\n        if (isNotFound(err) && !allPreload) {\n          await triggerOnReady()\n        }\n\n        throw err\n      }\n    }\n\n    return matches\n  }\n\n  invalidate: InvalidateFn<\n    RouterCore<\n      TRouteTree,\n      TTrailingSlashOption,\n      TDefaultStructuralSharingOption,\n      TRouterHistory,\n      TDehydrated\n    >\n  > = (opts) => {\n    const invalidate = (d: MakeRouteMatch<TRouteTree>) => {\n      if (opts?.filter?.(d as MakeRouteMatchUnion<this>) ?? true) {\n        return {\n          ...d,\n          invalid: true,\n          ...(d.status === 'error'\n            ? ({ status: 'pending', error: undefined } as const)\n            : {}),\n        }\n      }\n      return d\n    }\n\n    this.__store.setState((s) => ({\n      ...s,\n      matches: s.matches.map(invalidate),\n      cachedMatches: s.cachedMatches.map(invalidate),\n      pendingMatches: s.pendingMatches?.map(invalidate),\n    }))\n\n    return this.load({ sync: opts?.sync })\n  }\n\n  resolveRedirect = (err: AnyRedirect): ResolvedRedirect => {\n    const redirect = err as ResolvedRedirect\n\n    if (!redirect.href) {\n      redirect.href = this.buildLocation(redirect as any).href\n    }\n\n    return redirect\n  }\n\n  clearCache: ClearCacheFn<this> = (opts) => {\n    const filter = opts?.filter\n    if (filter !== undefined) {\n      this.__store.setState((s) => {\n        return {\n          ...s,\n          cachedMatches: s.cachedMatches.filter(\n            (m) => !filter(m as MakeRouteMatchUnion<this>),\n          ),\n        }\n      })\n    } else {\n      this.__store.setState((s) => {\n        return {\n          ...s,\n          cachedMatches: [],\n        }\n      })\n    }\n  }\n\n  clearExpiredCache = () => {\n    // This is where all of the garbage collection magic happens\n    const filter = (d: MakeRouteMatch<TRouteTree>) => {\n      const route = this.looseRoutesById[d.routeId]!\n\n      if (!route.options.loader) {\n        return true\n      }\n\n      // If the route was preloaded, use the preloadGcTime\n      // otherwise, use the gcTime\n      const gcTime =\n        (d.preload\n          ? (route.options.preloadGcTime ?? this.options.defaultPreloadGcTime)\n          : (route.options.gcTime ?? this.options.defaultGcTime)) ??\n        5 * 60 * 1000\n\n      return !(d.status !== 'error' && Date.now() - d.updatedAt < gcTime)\n    }\n    this.clearCache({ filter })\n  }\n\n  loadRouteChunk = (route: AnyRoute) => {\n    if (route._lazyPromise === undefined) {\n      if (route.lazyFn) {\n        route._lazyPromise = route.lazyFn().then((lazyRoute) => {\n          // explicitly don't copy over the lazy route's id\n          const { id: _id, ...options } = lazyRoute.options\n          Object.assign(route.options, options)\n        })\n      } else {\n        route._lazyPromise = Promise.resolve()\n      }\n    }\n\n    // If for some reason lazy resolves more lazy components...\n    // We'll wait for that before pre attempt to preload any\n    // components themselves.\n    if (route._componentsPromise === undefined) {\n      route._componentsPromise = route._lazyPromise.then(() =>\n        Promise.all(\n          componentTypes.map(async (type) => {\n            const component = route.options[type]\n            if ((component as any)?.preload) {\n              await (component as any).preload()\n            }\n          }),\n        ),\n      )\n    }\n    return route._componentsPromise\n  }\n\n  preloadRoute: PreloadRouteFn<\n    TRouteTree,\n    TTrailingSlashOption,\n    TDefaultStructuralSharingOption,\n    TRouterHistory\n  > = async (opts) => {\n    const next = this.buildLocation(opts as any)\n\n    let matches = this.matchRoutes(next, {\n      throwOnError: true,\n      preload: true,\n      dest: opts,\n    })\n\n    const activeMatchIds = new Set(\n      [...this.state.matches, ...(this.state.pendingMatches ?? [])].map(\n        (d) => d.id,\n      ),\n    )\n\n    const loadedMatchIds = new Set([\n      ...activeMatchIds,\n      ...this.state.cachedMatches.map((d) => d.id),\n    ])\n\n    // If the matches are already loaded, we need to add them to the cachedMatches\n    batch(() => {\n      matches.forEach((match) => {\n        if (!loadedMatchIds.has(match.id)) {\n          this.__store.setState((s) => ({\n            ...s,\n            cachedMatches: [...(s.cachedMatches as any), match],\n          }))\n        }\n      })\n    })\n\n    try {\n      matches = await this.loadMatches({\n        matches,\n        location: next,\n        preload: true,\n        updateMatch: (id, updater) => {\n          // Don't update the match if it's currently loaded\n          if (activeMatchIds.has(id)) {\n            matches = matches.map((d) => (d.id === id ? updater(d) : d))\n          } else {\n            this.updateMatch(id, updater)\n          }\n        },\n      })\n\n      return matches\n    } catch (err) {\n      if (isRedirect(err)) {\n        if (err.reloadDocument) {\n          return undefined\n        }\n        return await this.preloadRoute({\n          ...(err as any),\n          _fromLocation: next,\n        })\n      }\n      if (!isNotFound(err)) {\n        // Preload errors are not fatal, but we should still log them\n        console.error(err)\n      }\n      return undefined\n    }\n  }\n\n  matchRoute: MatchRouteFn<\n    TRouteTree,\n    TTrailingSlashOption,\n    TDefaultStructuralSharingOption,\n    TRouterHistory\n  > = (location, opts) => {\n    const matchLocation = {\n      ...location,\n      to: location.to\n        ? this.resolvePathWithBase(\n            (location.from || '') as string,\n            location.to as string,\n          )\n        : undefined,\n      params: location.params || {},\n      leaveParams: true,\n    }\n    const next = this.buildLocation(matchLocation as any)\n\n    if (opts?.pending && this.state.status !== 'pending') {\n      return false\n    }\n\n    const pending =\n      opts?.pending === undefined ? !this.state.isLoading : opts.pending\n\n    const baseLocation = pending\n      ? this.latestLocation\n      : this.state.resolvedLocation || this.state.location\n\n    const match = matchPathname(this.basepath, baseLocation.pathname, {\n      ...opts,\n      to: next.pathname,\n    }) as any\n\n    if (!match) {\n      return false\n    }\n    if (location.params) {\n      if (!deepEqual(match, location.params, { partial: true })) {\n        return false\n      }\n    }\n\n    if (match && (opts?.includeSearch ?? true)) {\n      return deepEqual(baseLocation.search, next.search, { partial: true })\n        ? match\n        : false\n    }\n\n    return match\n  }\n\n  ssr?: {\n    manifest: Manifest | undefined\n    serializer: StartSerializer\n  }\n\n  serverSsr?: {\n    injectedHtml: Array<InjectedHtmlEntry>\n    injectHtml: (getHtml: () => string | Promise<string>) => Promise<void>\n    injectScript: (\n      getScript: () => string | Promise<string>,\n      opts?: { logScript?: boolean },\n    ) => Promise<void>\n    streamValue: (key: string, value: any) => void\n    streamedKeys: Set<string>\n    onMatchSettled: (opts: { router: AnyRouter; match: AnyRouteMatch }) => any\n  }\n\n  clientSsr?: {\n    getStreamedValue: <T>(key: string) => T | undefined\n  }\n\n  _handleNotFound = (\n    matches: Array<AnyRouteMatch>,\n    err: NotFoundError,\n    {\n      updateMatch = this.updateMatch,\n    }: {\n      updateMatch?: (\n        id: string,\n        updater: (match: AnyRouteMatch) => AnyRouteMatch,\n      ) => void\n    } = {},\n  ) => {\n    // Find the route that should handle the not found error\n    // First check if a specific route is requested to show the error\n    const routeCursor = this.routesById[err.routeId ?? ''] ?? this.routeTree\n    const matchesByRouteId: Record<string, AnyRouteMatch> = {}\n\n    // Setup routesByRouteId object for quick access\n    for (const match of matches) {\n      matchesByRouteId[match.routeId] = match\n    }\n\n    // Ensure a NotFoundComponent exists on the route\n    if (\n      !routeCursor.options.notFoundComponent &&\n      (this.options as any)?.defaultNotFoundComponent\n    ) {\n      routeCursor.options.notFoundComponent = (\n        this.options as any\n      ).defaultNotFoundComponent\n    }\n\n    // Ensure we have a notFoundComponent\n    invariant(\n      routeCursor.options.notFoundComponent,\n      'No notFoundComponent found. Please set a notFoundComponent on your route or provide a defaultNotFoundComponent to the router.',\n    )\n\n    // Find the match for this route\n    const matchForRoute = matchesByRouteId[routeCursor.id]\n\n    invariant(\n      matchForRoute,\n      'Could not find match for route: ' + routeCursor.id,\n    )\n\n    // Assign the error to the match - using non-null assertion since we've checked with invariant\n    updateMatch(matchForRoute.id, (prev) => ({\n      ...prev,\n      status: 'notFound',\n      error: err,\n      isFetching: false,\n    }))\n\n    if ((err as any).routerCode === 'BEFORE_LOAD' && routeCursor.parentRoute) {\n      err.routeId = routeCursor.parentRoute.id\n      this._handleNotFound(matches, err, {\n        updateMatch,\n      })\n    }\n  }\n\n  hasNotFoundMatch = () => {\n    return this.__store.state.matches.some(\n      (d) => d.status === 'notFound' || d.globalNotFound,\n    )\n  }\n}\n\nexport class SearchParamError extends Error {}\n\nexport class PathParamError extends Error {}\n\n// A function that takes an import() argument which is a function and returns a new function that will\n// proxy arguments from the caller to the imported function, retaining all type\n// information along the way\nexport function lazyFn<\n  T extends Record<string, (...args: Array<any>) => any>,\n  TKey extends keyof T = 'default',\n>(fn: () => Promise<T>, key?: TKey) {\n  return async (\n    ...args: Parameters<T[TKey]>\n  ): Promise<Awaited<ReturnType<T[TKey]>>> => {\n    const imported = await fn()\n    return imported[key || 'default'](...args)\n  }\n}\n\nexport function getInitialRouterState(\n  location: ParsedLocation,\n): RouterState<any> {\n  return {\n    loadedAt: 0,\n    isLoading: false,\n    isTransitioning: false,\n    status: 'idle',\n    resolvedLocation: undefined,\n    location,\n    matches: [],\n    pendingMatches: [],\n    cachedMatches: [],\n    statusCode: 200,\n  }\n}\n\nfunction validateSearch(validateSearch: AnyValidator, input: unknown): unknown {\n  if (validateSearch == null) return {}\n\n  if ('~standard' in validateSearch) {\n    const result = validateSearch['~standard'].validate(input)\n\n    if (result instanceof Promise)\n      throw new SearchParamError('Async validation not supported')\n\n    if (result.issues)\n      throw new SearchParamError(JSON.stringify(result.issues, undefined, 2), {\n        cause: result,\n      })\n\n    return result.value\n  }\n\n  if ('parse' in validateSearch) {\n    return validateSearch.parse(input)\n  }\n\n  if (typeof validateSearch === 'function') {\n    return validateSearch(input)\n  }\n\n  return {}\n}\n\nexport const componentTypes = [\n  'component',\n  'errorComponent',\n  'pendingComponent',\n  'notFoundComponent',\n] as const\n\nfunction routeNeedsPreload(route: AnyRoute) {\n  for (const componentType of componentTypes) {\n    if ((route.options[componentType] as any)?.preload) {\n      return true\n    }\n  }\n  return false\n}\n", "import { defaultSerializeError } from './router'\n\nexport const TSR_DEFERRED_PROMISE = Symbol.for('TSR_DEFERRED_PROMISE')\n\nexport type DeferredPromiseState<T> =\n  | {\n      status: 'pending'\n      data?: T\n      error?: unknown\n    }\n  | {\n      status: 'success'\n      data: T\n    }\n  | {\n      status: 'error'\n      data?: T\n      error: unknown\n    }\n\nexport type DeferredPromise<T> = Promise<T> & {\n  [TSR_DEFERRED_PROMISE]: DeferredPromiseState<T>\n}\n\nexport function defer<T>(\n  _promise: Promise<T>,\n  options?: {\n    serializeError?: typeof defaultSerializeError\n  },\n) {\n  const promise = _promise as DeferredPromise<T>\n  // this is already deferred promise\n  if ((promise as any)[TSR_DEFERRED_PROMISE]) {\n    return promise\n  }\n  promise[TSR_DEFERRED_PROMISE] = { status: 'pending' }\n\n  promise\n    .then((data) => {\n      promise[TSR_DEFERRED_PROMISE].status = 'success'\n      promise[TSR_DEFERRED_PROMISE].data = data\n    })\n    .catch((error) => {\n      promise[TSR_DEFERRED_PROMISE].status = 'error'\n      ;(promise[TSR_DEFERRED_PROMISE] as any).error = {\n        data: (options?.serializeError ?? defaultSerializeError)(error),\n        __isServerError: true,\n      }\n    })\n\n  return promise\n}\n", "import type { <PERSON><PERSON>out<PERSON>, StaticDataRouteOption } from './route'\nimport type {\n  AllContext,\n  AllLoaderData,\n  AllParams,\n  FullSearchSchema,\n  ParseRoute,\n  RouteById,\n  RouteIds,\n} from './routeInfo'\nimport type { AnyRouter, RegisteredRouter } from './router'\nimport type { Constrain, ControlledPromise } from './utils'\n\nexport type AnyMatchAndValue = { match: any; value: any }\n\nexport type FindValueByIndex<\n  TKey,\n  TValue extends ReadonlyArray<any>,\n> = T<PERSON><PERSON> extends `${infer TIndex extends number}` ? TValue[TIndex] : never\n\nexport type FindValueByKey<TKey, TValue> =\n  TValue extends ReadonlyArray<any>\n    ? FindValueByIndex<TKey, TValue>\n    : TValue[TKey & keyof TValue]\n\nexport type CreateMatchAndValue<TMatch, TValue> = TValue extends any\n  ? {\n      match: TMatch\n      value: TValue\n    }\n  : never\n\nexport type NextMatchAndValue<\n  TK<PERSON>,\n  TMatchAndValue extends AnyMatchAndValue,\n> = TMatchAndValue extends any\n  ? CreateMatchAndValue<\n      TMatchAndValue['match'],\n      FindValueByKey<TKey, TMatchAndValue['value']>\n    >\n  : never\n\nexport type IsMatchKeyOf<TValue> =\n  TValue extends ReadonlyArray<any>\n    ? number extends TValue['length']\n      ? `${number}`\n      : keyof TValue & `${number}`\n    : TValue extends object\n      ? keyof TValue & string\n      : never\n\nexport type IsMatchPath<\n  TParentPath extends string,\n  TMatchAndValue extends AnyMatchAndValue,\n> = `${TParentPath}${IsMatchKeyOf<TMatchAndValue['value']>}`\n\nexport type IsMatchResult<\n  TKey,\n  TMatchAndValue extends AnyMatchAndValue,\n> = TMatchAndValue extends any\n  ? TKey extends keyof TMatchAndValue['value']\n    ? TMatchAndValue['match']\n    : never\n  : never\n\nexport type IsMatchParse<\n  TPath,\n  TMatchAndValue extends AnyMatchAndValue,\n  TParentPath extends string = '',\n> = TPath extends `${string}.${string}`\n  ? TPath extends `${infer TFirst}.${infer TRest}`\n    ? IsMatchParse<\n        TRest,\n        NextMatchAndValue<TFirst, TMatchAndValue>,\n        `${TParentPath}${TFirst}.`\n      >\n    : never\n  : {\n      path: IsMatchPath<TParentPath, TMatchAndValue>\n      result: IsMatchResult<TPath, TMatchAndValue>\n    }\n\nexport type IsMatch<TMatch, TPath> = IsMatchParse<\n  TPath,\n  TMatch extends any ? { match: TMatch; value: TMatch } : never\n>\n\n/**\n * Narrows matches based on a path\n * @experimental\n */\nexport const isMatch = <TMatch, TPath extends string>(\n  match: TMatch,\n  path: Constrain<TPath, IsMatch<TMatch, TPath>['path']>,\n): match is IsMatch<TMatch, TPath>['result'] => {\n  const parts = (path as string).split('.')\n  let part\n  let value: any = match\n\n  while ((part = parts.shift()) != null && value != null) {\n    value = value[part]\n  }\n\n  return value != null\n}\n\nexport interface DefaultRouteMatchExtensions {\n  scripts?: unknown\n  links?: unknown\n  headScripts?: unknown\n  meta?: unknown\n}\n\nexport interface RouteMatchExtensions extends DefaultRouteMatchExtensions {}\n\nexport interface RouteMatch<\n  out TRouteId,\n  out TFullPath,\n  out TAllParams,\n  out TFullSearchSchema,\n  out TLoaderData,\n  out TAllContext,\n  out TLoaderDeps,\n> extends RouteMatchExtensions {\n  id: string\n  routeId: TRouteId\n  fullPath: TFullPath\n  index: number\n  pathname: string\n  params: TAllParams\n  _strictParams: TAllParams\n  status: 'pending' | 'success' | 'error' | 'redirected' | 'notFound'\n  isFetching: false | 'beforeLoad' | 'loader'\n  error: unknown\n  paramsError: unknown\n  searchError: unknown\n  updatedAt: number\n  loadPromise?: ControlledPromise<void>\n  beforeLoadPromise?: ControlledPromise<void>\n  loaderPromise?: ControlledPromise<void>\n  loaderData?: TLoaderData\n  __routeContext: Record<string, unknown>\n  __beforeLoadContext: Record<string, unknown>\n  context: TAllContext\n  search: TFullSearchSchema\n  _strictSearch: TFullSearchSchema\n  fetchCount: number\n  abortController: AbortController\n  cause: 'preload' | 'enter' | 'stay'\n  loaderDeps: TLoaderDeps\n  preload: boolean\n  invalid: boolean\n  headers?: Record<string, string>\n  globalNotFound?: boolean\n  staticData: StaticDataRouteOption\n  minPendingPromise?: ControlledPromise<void>\n  pendingTimeout?: ReturnType<typeof setTimeout>\n}\n\nexport type MakeRouteMatchFromRoute<TRoute extends AnyRoute> = RouteMatch<\n  TRoute['types']['id'],\n  TRoute['types']['fullPath'],\n  TRoute['types']['allParams'],\n  TRoute['types']['fullSearchSchema'],\n  TRoute['types']['loaderData'],\n  TRoute['types']['allContext'],\n  TRoute['types']['loaderDeps']\n>\n\nexport type MakeRouteMatch<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n  TRouteId = RouteIds<TRouteTree>,\n  TStrict extends boolean = true,\n> = RouteMatch<\n  TRouteId,\n  RouteById<TRouteTree, TRouteId>['types']['fullPath'],\n  TStrict extends false\n    ? AllParams<TRouteTree>\n    : RouteById<TRouteTree, TRouteId>['types']['allParams'],\n  TStrict extends false\n    ? FullSearchSchema<TRouteTree>\n    : RouteById<TRouteTree, TRouteId>['types']['fullSearchSchema'],\n  TStrict extends false\n    ? AllLoaderData<TRouteTree>\n    : RouteById<TRouteTree, TRouteId>['types']['loaderData'],\n  TStrict extends false\n    ? AllContext<TRouteTree>\n    : RouteById<TRouteTree, TRouteId>['types']['allContext'],\n  RouteById<TRouteTree, TRouteId>['types']['loaderDeps']\n>\n\nexport type AnyRouteMatch = RouteMatch<any, any, any, any, any, any, any>\n\nexport type MakeRouteMatchUnion<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TRoute extends AnyRoute = ParseRoute<TRouter['routeTree']>,\n> = TRoute extends any\n  ? RouteMatch<\n      TRoute['id'],\n      TRoute['fullPath'],\n      TRoute['types']['allParams'],\n      TRoute['types']['fullSearchSchema'],\n      TRoute['types']['loaderData'],\n      TRoute['types']['allContext'],\n      TRoute['types']['loaderDeps']\n    >\n  : never\n\n/**\n * The `MatchRouteOptions` type is used to describe the options that can be used when matching a route.\n *\n * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/MatchRouteOptionsType#matchrouteoptions-type)\n */\nexport interface MatchRouteOptions {\n  /**\n   * If `true`, will match against pending location instead of the current location.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/MatchRouteOptionsType#pending-property)\n   */\n  pending?: boolean\n  /**\n   * If `true`, will match against the current location with case sensitivity.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/MatchRouteOptionsType#casesensitive-property)\n   */\n  caseSensitive?: boolean\n  /**\n   * If `true`, will match against the current location's search params using a deep inclusive check. e.g. `{ a: 1 }` will match for a current location of `{ a: 1, b: 2 }`.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/MatchRouteOptionsType#includesearch-property)\n   */\n  includeSearch?: boolean\n  /**\n   * If `true`, will match against the current location using a fuzzy match. e.g. `/posts` will match for a current location of `/posts/123`.\n   *\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/MatchRouteOptionsType#fuzzy-property)\n   */\n  fuzzy?: boolean\n}\n", "import { deepEqual } from './utils'\nimport type { NoInfer, PickOptional } from './utils'\nimport type { SearchMiddleware } from './route'\nimport type { IsRequiredParams } from './link'\n\nexport function retainSearchParams<TSearchSchema extends object>(\n  keys: Array<keyof TSearchSchema> | true,\n): SearchMiddleware<TSearchSchema> {\n  return ({ search, next }) => {\n    const result = next(search)\n    if (keys === true) {\n      return { ...search, ...result }\n    }\n    // add missing keys from search to result\n    keys.forEach((key) => {\n      if (!(key in result)) {\n        result[key] = search[key]\n      }\n    })\n    return result\n  }\n}\n\nexport function stripSearchParams<\n  TSearchSchema,\n  TOptionalProps = PickOptional<NoInfer<TSearchSchema>>,\n  const TValues =\n    | Partial<NoInfer<TOptionalProps>>\n    | Array<keyof TOptionalProps>,\n  const TInput = IsRequiredParams<TSearchSchema> extends never\n    ? TValues | true\n    : TValues,\n>(input: NoInfer<TInput>): SearchMiddleware<TSearchSchema> {\n  return ({ search, next }) => {\n    if (input === true) {\n      return {}\n    }\n    const result = next(search) as Record<string, unknown>\n    if (Array.isArray(input)) {\n      input.forEach((key) => {\n        delete result[key]\n      })\n    } else {\n      Object.entries(input as Record<string, unknown>).forEach(\n        ([key, value]) => {\n          if (deepEqual(result[key], value)) {\n            delete result[key]\n          }\n        },\n      )\n    }\n    return result as any\n  }\n}\n", "import type { HistoryState, ParsedHistoryState } from '@tanstack/history'\nimport type {\n  AllParams,\n  CatchAllPaths,\n  CurrentPath,\n  FullSearchSchema,\n  FullSearchSchemaInput,\n  ParentPath,\n  RouteByPath,\n  RouteByToPath,\n  RoutePaths,\n  RouteToPath,\n  ToPath,\n} from './routeInfo'\nimport type {\n  AnyRouter,\n  RegisteredRouter,\n  ViewTransitionOptions,\n} from './router'\nimport type {\n  ConstrainLiteral,\n  Expand,\n  MakeDifferenceOptional,\n  NoInfer,\n  NonNullableUpdater,\n  Updater,\n} from './utils'\nimport type { ParsedLocation } from './location'\n\nexport type IsRequiredParams<TParams> =\n  Record<never, never> extends TParams ? never : true\n\nexport type ParsePathParams<T extends string, TAcc = never> = T &\n  `${string}$${string}` extends never\n  ? TAcc\n  : T extends `${string}$${infer TPossiblyParam}`\n    ? TPossiblyParam extends ''\n      ? TAcc\n      : TPossiblyParam & `${string}/${string}` extends never\n        ? TPossiblyParam | TAcc\n        : TPossiblyParam extends `${infer TParam}/${infer TRest}`\n          ? ParsePathParams<TRest, TParam extends '' ? TAcc : TParam | TAcc>\n          : never\n    : TAcc\n\nexport type AddTrailingSlash<T> = T extends `${string}/` ? T : `${T & string}/`\n\nexport type RemoveTrailingSlashes<T> = T & `${string}/` extends never\n  ? T\n  : T extends `${infer R}/`\n    ? R\n    : T\n\nexport type AddLeadingSlash<T> = T & `/${string}` extends never\n  ? `/${T & string}`\n  : T\n\nexport type RemoveLeadingSlashes<T> = T & `/${string}` extends never\n  ? T\n  : T extends `/${infer R}`\n    ? R\n    : T\n\ntype JoinPath<TLeft extends string, TRight extends string> = TRight extends ''\n  ? TLeft\n  : TLeft extends ''\n    ? TRight\n    : `${RemoveTrailingSlashes<TLeft>}/${RemoveLeadingSlashes<TRight>}`\n\ntype RemoveLastSegment<\n  T extends string,\n  TAcc extends string = '',\n> = T extends `${infer TSegment}/${infer TRest}`\n  ? TRest & `${string}/${string}` extends never\n    ? TRest extends ''\n      ? TAcc\n      : `${TAcc}${TSegment}`\n    : RemoveLastSegment<TRest, `${TAcc}${TSegment}/`>\n  : TAcc\n\nexport type ResolveCurrentPath<\n  TFrom extends string,\n  TTo extends string,\n> = TTo extends '.'\n  ? TFrom\n  : TTo extends './'\n    ? AddTrailingSlash<TFrom>\n    : TTo & `./${string}` extends never\n      ? never\n      : TTo extends `./${infer TRest}`\n        ? AddLeadingSlash<JoinPath<TFrom, TRest>>\n        : never\n\nexport type ResolveParentPath<\n  TFrom extends string,\n  TTo extends string,\n> = TTo extends '../' | '..'\n  ? TFrom extends '' | '/'\n    ? never\n    : AddLeadingSlash<RemoveLastSegment<TFrom>>\n  : TTo & `../${string}` extends never\n    ? AddLeadingSlash<JoinPath<TFrom, TTo>>\n    : TFrom extends '' | '/'\n      ? never\n      : TTo extends `../${infer ToRest}`\n        ? ResolveParentPath<RemoveLastSegment<TFrom>, ToRest>\n        : AddLeadingSlash<JoinPath<TFrom, TTo>>\n\nexport type ResolveRelativePath<TFrom, TTo = '.'> = string extends TFrom\n  ? TTo\n  : string extends TTo\n    ? TFrom\n    : undefined extends TTo\n      ? TFrom\n      : TTo extends string\n        ? TFrom extends string\n          ? TTo extends `/${string}`\n            ? TTo\n            : TTo extends `..${string}`\n              ? ResolveParentPath<TFrom, TTo>\n              : TTo extends `.${string}`\n                ? ResolveCurrentPath<TFrom, TTo>\n                : AddLeadingSlash<JoinPath<TFrom, TTo>>\n          : never\n        : never\n\nexport type FindDescendantToPaths<\n  TRouter extends AnyRouter,\n  TPrefix extends string,\n> = `${TPrefix}/${string}` & RouteToPath<TRouter>\n\nexport type InferDescendantToPaths<\n  TRouter extends AnyRouter,\n  TPrefix extends string,\n  TPaths = FindDescendantToPaths<TRouter, TPrefix>,\n> = TPaths extends `${TPrefix}/`\n  ? never\n  : TPaths extends `${TPrefix}/${infer TRest}`\n    ? TRest\n    : never\n\nexport type RelativeToPath<\n  TRouter extends AnyRouter,\n  TTo extends string,\n  TResolvedPath extends string,\n> =\n  | (TResolvedPath & RouteToPath<TRouter> extends never\n      ? never\n      : ToPath<TRouter, TTo>)\n  | `${RemoveTrailingSlashes<TTo>}/${InferDescendantToPaths<TRouter, RemoveTrailingSlashes<TResolvedPath>>}`\n\nexport type RelativeToParentPath<\n  TRouter extends AnyRouter,\n  TFrom extends string,\n  TTo extends string,\n  TResolvedPath extends string = ResolveRelativePath<TFrom, TTo>,\n> =\n  | RelativeToPath<TRouter, TTo, TResolvedPath>\n  | (TTo extends `${string}..` | `${string}../`\n      ? TResolvedPath extends '/' | ''\n        ? never\n        : FindDescendantToPaths<\n              TRouter,\n              RemoveTrailingSlashes<TResolvedPath>\n            > extends never\n          ? never\n          : `${RemoveTrailingSlashes<TTo>}/${ParentPath<TRouter>}`\n      : never)\n\nexport type RelativeToCurrentPath<\n  TRouter extends AnyRouter,\n  TFrom extends string,\n  TTo extends string,\n  TResolvedPath extends string = ResolveRelativePath<TFrom, TTo>,\n> = RelativeToPath<TRouter, TTo, TResolvedPath> | CurrentPath<TRouter>\n\nexport type AbsoluteToPath<TRouter extends AnyRouter, TFrom extends string> =\n  | (string extends TFrom\n      ? CurrentPath<TRouter>\n      : TFrom extends `/`\n        ? never\n        : CurrentPath<TRouter>)\n  | (string extends TFrom\n      ? ParentPath<TRouter>\n      : TFrom extends `/`\n        ? never\n        : ParentPath<TRouter>)\n  | RouteToPath<TRouter>\n  | (TFrom extends '/'\n      ? never\n      : string extends TFrom\n        ? never\n        : InferDescendantToPaths<TRouter, RemoveTrailingSlashes<TFrom>>)\n\nexport type RelativeToPathAutoComplete<\n  TRouter extends AnyRouter,\n  TFrom extends string,\n  TTo extends string,\n> = string extends TTo\n  ? string\n  : string extends TFrom\n    ? AbsoluteToPath<TRouter, TFrom>\n    : TTo & `..${string}` extends never\n      ? TTo & `.${string}` extends never\n        ? AbsoluteToPath<TRouter, TFrom>\n        : RelativeToCurrentPath<TRouter, TFrom, TTo>\n      : RelativeToParentPath<TRouter, TFrom, TTo>\n\nexport type NavigateOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = ToOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo> & NavigateOptionProps\n\n/**\n * The NavigateOptions type is used to describe the options that can be used when describing a navigation action in TanStack Router.\n * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType)\n */\nexport interface NavigateOptionProps {\n  /**\n   * If set to `true`, the router will scroll the element with an id matching the hash into view with default `ScrollIntoViewOptions`.\n   * If set to `false`, the router will not scroll the element with an id matching the hash into view.\n   * If set to `ScrollIntoViewOptions`, the router will scroll the element with an id matching the hash into view with the provided options.\n   * @default true\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#hashscrollintoview)\n   * @see [MDN](https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView)\n   */\n  hashScrollIntoView?: boolean | ScrollIntoViewOptions\n  /**\n   * `replace` is a boolean that determines whether the navigation should replace the current history entry or push a new one.\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#replace)\n   */\n  replace?: boolean\n  /**\n   * Defaults to `true` so that the scroll position will be reset to 0,0 after the location is committed to the browser history.\n   * If `false`, the scroll position will not be reset to 0,0 after the location is committed to history.\n   * @default true\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#resetscroll)\n   */\n  resetScroll?: boolean\n  /** @deprecated All navigations now use startTransition under the hood */\n  startTransition?: boolean\n  /**\n   * If set to `true`, the router will wrap the resulting navigation in a `document.startViewTransition()` call.\n   * If `ViewTransitionOptions`, route navigations will be called using `document.startViewTransition({update, types})`\n   * where `types` will be the strings array passed with `ViewTransitionOptions[\"types\"]`.\n   * If the browser does not support viewTransition types, the navigation will fall back to normal `document.startTransition()`, same as if `true` was passed.\n   *\n   * If the browser does not support this api, this option will be ignored.\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#viewtransition)\n   * @see [MDN](https://developer.mozilla.org/en-US/docs/Web/API/Document/startViewTransition)\n   * @see [Google](https://developer.chrome.com/docs/web-platform/view-transitions/same-document#view-transition-types)\n   */\n  viewTransition?: boolean | ViewTransitionOptions\n  /**\n   * If `true`, navigation will ignore any blockers that might prevent it.\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#ignoreblocker)\n   */\n  ignoreBlocker?: boolean\n  /**\n   * If `true`, navigation to a route inside of router will trigger a full page load instead of the traditional SPA navigation.\n   * @default false\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#reloaddocument)\n   */\n  reloadDocument?: boolean\n  /**\n   * This can be used instead of `to` to navigate to a fully built href, e.g. pointing to an external target.\n   * @link [API Docs](https://tanstack.com/router/latest/docs/framework/react/api/router/NavigateOptionsType#href)\n   */\n  href?: string\n}\n\nexport type ToOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = ToSubOptions<TRouter, TFrom, TTo> & MaskOptions<TRouter, TMaskFrom, TMaskTo>\n\nexport interface MaskOptions<\n  in out TRouter extends AnyRouter,\n  in out TMaskFrom extends string,\n  in out TMaskTo extends string,\n> {\n  _fromLocation?: ParsedLocation\n  mask?: ToMaskOptions<TRouter, TMaskFrom, TMaskTo>\n}\n\nexport type ToMaskOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TMaskFrom extends string = string,\n  TMaskTo extends string = '.',\n> = ToSubOptions<TRouter, TMaskFrom, TMaskTo> & {\n  unmaskOnReload?: boolean\n}\n\nexport type ToSubOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n> = ToSubOptionsProps<TRouter, TFrom, TTo> &\n  SearchParamOptions<TRouter, TFrom, TTo> &\n  PathParamOptions<TRouter, TFrom, TTo>\n\nexport interface RequiredToOptions<\n  in out TRouter extends AnyRouter,\n  in out TFrom extends string,\n  in out TTo extends string | undefined,\n> {\n  to: ToPathOption<TRouter, TFrom, TTo> & {}\n}\n\nexport interface OptionalToOptions<\n  in out TRouter extends AnyRouter,\n  in out TFrom extends string,\n  in out TTo extends string | undefined,\n> {\n  to?: ToPathOption<TRouter, TFrom, TTo> & {}\n}\n\nexport type MakeToRequired<\n  TRouter extends AnyRouter,\n  TFrom extends string,\n  TTo extends string | undefined,\n> = string extends TFrom\n  ? string extends TTo\n    ? OptionalToOptions<TRouter, TFrom, TTo>\n    : TTo & CatchAllPaths<TRouter> extends never\n      ? RequiredToOptions<TRouter, TFrom, TTo>\n      : OptionalToOptions<TRouter, TFrom, TTo>\n  : OptionalToOptions<TRouter, TFrom, TTo>\n\nexport type ToSubOptionsProps<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends RoutePaths<TRouter['routeTree']> | string = string,\n  TTo extends string | undefined = '.',\n> = MakeToRequired<TRouter, TFrom, TTo> & {\n  hash?: true | Updater<string>\n  state?: true | NonNullableUpdater<ParsedHistoryState, HistoryState>\n  from?: FromPathOption<TRouter, TFrom> & {}\n}\n\nexport type ParamsReducerFn<\n  in out TRouter extends AnyRouter,\n  in out TParamVariant extends ParamVariant,\n  in out TFrom,\n  in out TTo,\n> = (\n  current: Expand<ResolveFromParams<TRouter, TParamVariant, TFrom>>,\n) => Expand<ResolveRelativeToParams<TRouter, TParamVariant, TFrom, TTo>>\n\ntype ParamsReducer<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n  TFrom,\n  TTo,\n> =\n  | Expand<ResolveRelativeToParams<TRouter, TParamVariant, TFrom, TTo>>\n  | (ParamsReducerFn<TRouter, TParamVariant, TFrom, TTo> & {})\n\ntype ParamVariant = 'PATH' | 'SEARCH'\n\nexport type ResolveRoute<\n  TRouter extends AnyRouter,\n  TFrom,\n  TTo,\n  TPath = ResolveRelativePath<TFrom, TTo>,\n> = TPath extends string\n  ? TFrom extends TPath\n    ? RouteByPath<TRouter['routeTree'], TPath>\n    : RouteByToPath<TRouter, TPath>\n  : never\n\ntype ResolveFromParamType<TParamVariant extends ParamVariant> =\n  TParamVariant extends 'PATH' ? 'allParams' : 'fullSearchSchema'\n\ntype ResolveFromAllParams<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n> = TParamVariant extends 'PATH'\n  ? AllParams<TRouter['routeTree']>\n  : FullSearchSchema<TRouter['routeTree']>\n\ntype ResolveFromParams<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n  TFrom,\n> = string extends TFrom\n  ? ResolveFromAllParams<TRouter, TParamVariant>\n  : RouteByPath<\n      TRouter['routeTree'],\n      TFrom\n    >['types'][ResolveFromParamType<TParamVariant>]\n\ntype ResolveToParamType<TParamVariant extends ParamVariant> =\n  TParamVariant extends 'PATH' ? 'allParams' : 'fullSearchSchemaInput'\n\ntype ResolveAllToParams<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n> = TParamVariant extends 'PATH'\n  ? AllParams<TRouter['routeTree']>\n  : FullSearchSchemaInput<TRouter['routeTree']>\n\nexport type ResolveToParams<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n  TFrom,\n  TTo,\n> =\n  ResolveRelativePath<TFrom, TTo> extends infer TPath\n    ? undefined extends TPath\n      ? never\n      : string extends TPath\n        ? ResolveAllToParams<TRouter, TParamVariant>\n        : TPath extends CatchAllPaths<TRouter>\n          ? ResolveAllToParams<TRouter, TParamVariant>\n          : ResolveRoute<\n              TRouter,\n              TFrom,\n              TTo\n            >['types'][ResolveToParamType<TParamVariant>]\n    : never\n\ntype ResolveRelativeToParams<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n  TFrom,\n  TTo,\n  TToParams = ResolveToParams<TRouter, TParamVariant, TFrom, TTo>,\n> = TParamVariant extends 'SEARCH'\n  ? TToParams\n  : string extends TFrom\n    ? TToParams\n    : MakeDifferenceOptional<\n        ResolveFromParams<TRouter, TParamVariant, TFrom>,\n        TToParams\n      >\n\nexport interface MakeOptionalSearchParams<\n  in out TRouter extends AnyRouter,\n  in out TFrom,\n  in out TTo,\n> {\n  search?: true | (ParamsReducer<TRouter, 'SEARCH', TFrom, TTo> & {})\n}\n\nexport interface MakeOptionalPathParams<\n  in out TRouter extends AnyRouter,\n  in out TFrom,\n  in out TTo,\n> {\n  params?: true | (ParamsReducer<TRouter, 'PATH', TFrom, TTo> & {})\n}\n\ntype MakeRequiredParamsReducer<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n  TFrom,\n  TTo,\n> =\n  | (string extends TFrom\n      ? never\n      : ResolveFromParams<\n            TRouter,\n            TParamVariant,\n            TFrom\n          > extends ResolveRelativeToParams<TRouter, TParamVariant, TFrom, TTo>\n        ? true\n        : never)\n  | (ParamsReducer<TRouter, TParamVariant, TFrom, TTo> & {})\n\nexport interface MakeRequiredPathParams<\n  in out TRouter extends AnyRouter,\n  in out TFrom,\n  in out TTo,\n> {\n  params: MakeRequiredParamsReducer<TRouter, 'PATH', TFrom, TTo> & {}\n}\n\nexport interface MakeRequiredSearchParams<\n  in out TRouter extends AnyRouter,\n  in out TFrom,\n  in out TTo,\n> {\n  search: MakeRequiredParamsReducer<TRouter, 'SEARCH', TFrom, TTo> & {}\n}\n\nexport type IsRequired<\n  TRouter extends AnyRouter,\n  TParamVariant extends ParamVariant,\n  TFrom,\n  TTo,\n> =\n  ResolveRelativePath<TFrom, TTo> extends infer TPath\n    ? undefined extends TPath\n      ? never\n      : TPath extends CatchAllPaths<TRouter>\n        ? never\n        : IsRequiredParams<\n            ResolveRelativeToParams<TRouter, TParamVariant, TFrom, TTo>\n          >\n    : never\n\nexport type SearchParamOptions<TRouter extends AnyRouter, TFrom, TTo> =\n  IsRequired<TRouter, 'SEARCH', TFrom, TTo> extends never\n    ? MakeOptionalSearchParams<TRouter, TFrom, TTo>\n    : MakeRequiredSearchParams<TRouter, TFrom, TTo>\n\nexport type PathParamOptions<TRouter extends AnyRouter, TFrom, TTo> =\n  IsRequired<TRouter, 'PATH', TFrom, TTo> extends never\n    ? MakeOptionalPathParams<TRouter, TFrom, TTo>\n    : MakeRequiredPathParams<TRouter, TFrom, TTo>\n\nexport type ToPathOption<\n  TRouter extends AnyRouter = AnyRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = string,\n> = ConstrainLiteral<\n  TTo,\n  RelativeToPathAutoComplete<\n    TRouter,\n    NoInfer<TFrom> extends string ? NoInfer<TFrom> : '',\n    NoInfer<TTo> & string\n  >\n>\n\nexport type FromPathOption<TRouter extends AnyRouter, TFrom> = ConstrainLiteral<\n  TFrom,\n  RoutePaths<TRouter['routeTree']>\n>\n\n/**\n * @link [Guide](https://tanstack.com/router/latest/docs/framework/react/guide/navigation#active-options)\n */\nexport interface ActiveOptions {\n  /**\n   * If true, the link will be active if the current route matches the `to` route path exactly (no children routes)\n   * @default false\n   */\n  exact?: boolean\n  /**\n   * If true, the link will only be active if the current URL hash matches the `hash` prop\n   * @default false\n   */\n  includeHash?: boolean\n  /**\n   * If true, the link will only be active if the current URL search params inclusively match the `search` prop\n   * @default true\n   */\n  includeSearch?: boolean\n  /**\n   * This modifies the `includeSearch` behavior.\n   * If true,  properties in `search` that are explicitly `undefined` must NOT be present in the current URL search params for the link to be active.\n   * @default false\n   */\n  explicitUndefined?: boolean\n}\n\nexport interface LinkOptionsProps {\n  /**\n   * The standard anchor tag target attribute\n   */\n  target?: HTMLAnchorElement['target']\n  /**\n   * Configurable options to determine if the link should be considered active or not\n   * @default {exact:true,includeHash:true}\n   */\n  activeOptions?: ActiveOptions\n  /**\n   * The preloading strategy for this link\n   * - `false` - No preloading\n   * - `'intent'` - Preload the linked route on hover and cache it for this many milliseconds in hopes that the user will eventually navigate there.\n   * - `'viewport'` - Preload the linked route when it enters the viewport\n   */\n  preload?: false | 'intent' | 'viewport' | 'render'\n  /**\n   * When a preload strategy is set, this delays the preload by this many milliseconds.\n   * If the user exits the link before this delay, the preload will be cancelled.\n   */\n  preloadDelay?: number\n  /**\n   * Control whether the link should be disabled or not\n   * If set to `true`, the link will be rendered without an `href` attribute\n   * @default false\n   */\n  disabled?: boolean\n}\n\nexport type LinkOptions<\n  TRouter extends AnyRouter = RegisteredRouter,\n  TFrom extends string = string,\n  TTo extends string | undefined = '.',\n  TMaskFrom extends string = TFrom,\n  TMaskTo extends string = '.',\n> = NavigateOptions<TRouter, TFrom, TTo, TMaskFrom, TMaskTo> & LinkOptionsProps\n\nexport type LinkCurrentTargetElement = {\n  preloadTimeout?: null | ReturnType<typeof setTimeout>\n}\n\nexport const preloadWarning = 'Error preloading route! ☝️'\n", "import { joinPaths, trimPathLeft } from './path'\nimport { notFound } from './not-found'\nimport { rootRouteId } from './root'\nimport type { LazyRoute } from './fileRoute'\nimport type { NotFoundError } from './not-found'\nimport type { NavigateOptions, ParsePathParams } from './link'\nimport type { ParsedLocation } from './location'\nimport type {\n  AnyRouteMatch,\n  MakeRouteMatchFromRoute,\n  MakeRouteMatchUnion,\n  RouteMatch,\n} from './Matches'\nimport type { RootRouteId } from './root'\nimport type { ParseRoute, RouteById, RoutePaths } from './routeInfo'\nimport type { AnyRouter, RegisteredRouter } from './router'\nimport type { BuildLocationFn, NavigateFn } from './RouterProvider'\nimport type {\n  Assign,\n  Constrain,\n  Expand,\n  IntersectAssign,\n  NoInfer,\n} from './utils'\nimport type {\n  AnySchema,\n  AnyStandardSchemaValidator,\n  AnyValidator,\n  AnyValidatorAdapter,\n  AnyValidatorObj,\n  DefaultValidator,\n  ResolveSearchValidatorInput,\n  ResolveValidatorOutput,\n  StandardSchemaValidator,\n  ValidatorAdapter,\n  ValidatorFn,\n  ValidatorObj,\n} from './validators'\n\nexport type AnyPathParams = {}\n\nexport type SearchSchemaInput = {\n  __TSearchSchemaInput__: 'TSearchSchemaInput'\n}\n\nexport type AnyContext = {}\n\nexport interface RouteContext {}\n\nexport type PreloadableObj = { preload?: () => Promise<void> }\n\nexport type RoutePathOptions<TCustomId, TPath> =\n  | {\n      path: TPath\n    }\n  | {\n      id: TCustomId\n    }\n\nexport interface StaticDataRouteOption {}\n\nexport type RoutePathOptionsIntersection<TCustomId, TPath> = {\n  path: TPath\n  id: TCustomId\n}\n\nexport type SearchFilter<TInput, TResult = TInput> = (prev: TInput) => TResult\n\nexport type SearchMiddlewareContext<TSearchSchema> = {\n  search: TSearchSchema\n  next: (newSearch: TSearchSchema) => TSearchSchema\n}\n\nexport type SearchMiddleware<TSearchSchema> = (\n  ctx: SearchMiddlewareContext<TSearchSchema>,\n) => TSearchSchema\n\nexport type ResolveId<\n  TParentRoute,\n  TCustomId extends string,\n  TPath extends string,\n> = TParentRoute extends { id: infer TParentId extends string }\n  ? RoutePrefix<TParentId, string extends TCustomId ? TPath : TCustomId>\n  : RootRouteId\n\nexport type InferFullSearchSchema<TRoute> = TRoute extends {\n  types: {\n    fullSearchSchema: infer TFullSearchSchema\n  }\n}\n  ? TFullSearchSchema\n  : {}\n\nexport type InferFullSearchSchemaInput<TRoute> = TRoute extends {\n  types: {\n    fullSearchSchemaInput: infer TFullSearchSchemaInput\n  }\n}\n  ? TFullSearchSchemaInput\n  : {}\n\nexport type InferAllParams<TRoute> = TRoute extends {\n  types: {\n    allParams: infer TAllParams\n  }\n}\n  ? TAllParams\n  : {}\n\nexport type InferAllContext<TRoute> = unknown extends TRoute\n  ? TRoute\n  : TRoute extends {\n        types: {\n          allContext: infer TAllContext\n        }\n      }\n    ? TAllContext\n    : {}\n\nexport type ResolveSearchSchemaFnInput<TSearchValidator> =\n  TSearchValidator extends (input: infer TSearchSchemaInput) => any\n    ? TSearchSchemaInput extends SearchSchemaInput\n      ? Omit<TSearchSchemaInput, keyof SearchSchemaInput>\n      : ResolveSearchSchemaFn<TSearchValidator>\n    : AnySchema\n\nexport type ResolveSearchSchemaInput<TSearchValidator> =\n  TSearchValidator extends AnyStandardSchemaValidator\n    ? NonNullable<TSearchValidator['~standard']['types']>['input']\n    : TSearchValidator extends AnyValidatorAdapter\n      ? TSearchValidator['types']['input']\n      : TSearchValidator extends AnyValidatorObj\n        ? ResolveSearchSchemaFnInput<TSearchValidator['parse']>\n        : ResolveSearchSchemaFnInput<TSearchValidator>\n\nexport type ResolveSearchSchemaFn<TSearchValidator> = TSearchValidator extends (\n  ...args: any\n) => infer TSearchSchema\n  ? TSearchSchema\n  : AnySchema\n\nexport type ResolveSearchSchema<TSearchValidator> =\n  unknown extends TSearchValidator\n    ? TSearchValidator\n    : TSearchValidator extends AnyStandardSchemaValidator\n      ? NonNullable<TSearchValidator['~standard']['types']>['output']\n      : TSearchValidator extends AnyValidatorAdapter\n        ? TSearchValidator['types']['output']\n        : TSearchValidator extends AnyValidatorObj\n          ? ResolveSearchSchemaFn<TSearchValidator['parse']>\n          : ResolveSearchSchemaFn<TSearchValidator>\n\nexport type ParseSplatParams<TPath extends string> = TPath &\n  `${string}$` extends never\n  ? TPath & `${string}$/${string}` extends never\n    ? never\n    : '_splat'\n  : '_splat'\n\nexport interface SplatParams {\n  _splat?: string\n}\n\nexport type ResolveParams<TPath extends string> =\n  ParseSplatParams<TPath> extends never\n    ? Record<ParsePathParams<TPath>, string>\n    : Record<ParsePathParams<TPath>, string> & SplatParams\n\nexport type ParseParamsFn<in out TPath extends string, in out TParams> = (\n  rawParams: ResolveParams<TPath>,\n) => TParams extends Record<ParsePathParams<TPath>, any>\n  ? TParams\n  : Record<ParsePathParams<TPath>, any>\n\nexport type StringifyParamsFn<in out TPath extends string, in out TParams> = (\n  params: TParams,\n) => ResolveParams<TPath>\n\nexport type ParamsOptions<in out TPath extends string, in out TParams> = {\n  params?: {\n    parse?: ParseParamsFn<TPath, TParams>\n    stringify?: StringifyParamsFn<TPath, TParams>\n  }\n\n  /** \n  @deprecated Use params.parse instead\n  */\n  parseParams?: ParseParamsFn<TPath, TParams>\n\n  /** \n  @deprecated Use params.stringify instead\n  */\n  stringifyParams?: StringifyParamsFn<TPath, TParams>\n}\n\ninterface RequiredStaticDataRouteOption {\n  staticData: StaticDataRouteOption\n}\n\ninterface OptionalStaticDataRouteOption {\n  staticData?: StaticDataRouteOption\n}\n\nexport type UpdatableStaticRouteOption = {} extends StaticDataRouteOption\n  ? OptionalStaticDataRouteOption\n  : RequiredStaticDataRouteOption\n\nexport type MetaDescriptor =\n  | { charSet: 'utf-8' }\n  | { title: string }\n  | { name: string; content: string }\n  | { property: string; content: string }\n  | { httpEquiv: string; content: string }\n  | { 'script:ld+json': LdJsonObject }\n  | { tagName: 'meta' | 'link'; [name: string]: string }\n  | Record<string, unknown>\n\ntype LdJsonObject = { [Key in string]: LdJsonValue } & {\n  [Key in string]?: LdJsonValue | undefined\n}\ntype LdJsonArray = Array<LdJsonValue> | ReadonlyArray<LdJsonValue>\ntype LdJsonPrimitive = string | number | boolean | null\ntype LdJsonValue = LdJsonPrimitive | LdJsonObject | LdJsonArray\n\nexport type RouteLinkEntry = {}\n\nexport type SearchValidator<TInput, TOutput> =\n  | ValidatorObj<TInput, TOutput>\n  | ValidatorFn<TInput, TOutput>\n  | ValidatorAdapter<TInput, TOutput>\n  | StandardSchemaValidator<TInput, TOutput>\n  | undefined\n\nexport type AnySearchValidator = SearchValidator<any, any>\n\nexport type DefaultSearchValidator = SearchValidator<\n  Record<string, unknown>,\n  AnySchema\n>\n\nexport type RoutePrefix<\n  TPrefix extends string,\n  TPath extends string,\n> = string extends TPath\n  ? RootRouteId\n  : TPath extends string\n    ? TPrefix extends RootRouteId\n      ? TPath extends '/'\n        ? '/'\n        : `/${TrimPath<TPath>}`\n      : `${TPrefix}/${TPath}` extends '/'\n        ? '/'\n        : `/${TrimPathLeft<`${TrimPathRight<TPrefix>}/${TrimPath<TPath>}`>}`\n    : never\n\nexport type TrimPath<T extends string> = '' extends T\n  ? ''\n  : TrimPathRight<TrimPathLeft<T>>\n\nexport type TrimPathLeft<T extends string> =\n  T extends `${RootRouteId}/${infer U}`\n    ? TrimPathLeft<U>\n    : T extends `/${infer U}`\n      ? TrimPathLeft<U>\n      : T\n\nexport type TrimPathRight<T extends string> = T extends '/'\n  ? '/'\n  : T extends `${infer U}/`\n    ? TrimPathRight<U>\n    : T\n\nexport type LooseReturnType<T> = T extends (\n  ...args: Array<any>\n) => infer TReturn\n  ? TReturn\n  : never\n\nexport type LooseAsyncReturnType<T> = T extends (\n  ...args: Array<any>\n) => infer TReturn\n  ? TReturn extends Promise<infer TReturn>\n    ? TReturn\n    : TReturn\n  : never\n\nexport type ContextReturnType<TContextFn> = unknown extends TContextFn\n  ? TContextFn\n  : LooseReturnType<TContextFn> extends never\n    ? AnyContext\n    : LooseReturnType<TContextFn>\n\nexport type ContextAsyncReturnType<TContextFn> = unknown extends TContextFn\n  ? TContextFn\n  : LooseAsyncReturnType<TContextFn> extends never\n    ? AnyContext\n    : LooseAsyncReturnType<TContextFn>\n\nexport type ResolveRouteContext<TRouteContextFn, TBeforeLoadFn> = Assign<\n  ContextReturnType<TRouteContextFn>,\n  ContextAsyncReturnType<TBeforeLoadFn>\n>\n\nexport type ResolveLoaderData<TLoaderFn> = unknown extends TLoaderFn\n  ? TLoaderFn\n  : LooseAsyncReturnType<TLoaderFn> extends never\n    ? undefined\n    : LooseAsyncReturnType<TLoaderFn>\n\nexport type ResolveFullSearchSchema<\n  TParentRoute extends AnyRoute,\n  TSearchValidator,\n> = unknown extends TParentRoute\n  ? ResolveValidatorOutput<TSearchValidator>\n  : IntersectAssign<\n      InferFullSearchSchema<TParentRoute>,\n      ResolveValidatorOutput<TSearchValidator>\n    >\n\nexport type ResolveFullSearchSchemaInput<\n  TParentRoute extends AnyRoute,\n  TSearchValidator,\n> = IntersectAssign<\n  InferFullSearchSchemaInput<TParentRoute>,\n  ResolveSearchValidatorInput<TSearchValidator>\n>\n\nexport type ResolveAllParamsFromParent<\n  TParentRoute extends AnyRoute,\n  TParams,\n> = Assign<InferAllParams<TParentRoute>, TParams>\n\nexport type RouteContextParameter<\n  TParentRoute extends AnyRoute,\n  TRouterContext,\n> = unknown extends TParentRoute\n  ? TRouterContext\n  : Assign<TRouterContext, InferAllContext<TParentRoute>>\n\nexport type BeforeLoadContextParameter<\n  TParentRoute extends AnyRoute,\n  TRouterContext,\n  TRouteContextFn,\n> = Assign<\n  RouteContextParameter<TParentRoute, TRouterContext>,\n  ContextReturnType<TRouteContextFn>\n>\n\nexport type ResolveAllContext<\n  TParentRoute extends AnyRoute,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n> = Assign<\n  BeforeLoadContextParameter<TParentRoute, TRouterContext, TRouteContextFn>,\n  ContextAsyncReturnType<TBeforeLoadFn>\n>\nexport interface FullSearchSchemaOption<\n  in out TParentRoute extends AnyRoute,\n  in out TSearchValidator,\n> {\n  search: Expand<ResolveFullSearchSchema<TParentRoute, TSearchValidator>>\n}\n\nexport interface RemountDepsOptions<\n  in out TRouteId,\n  in out TFullSearchSchema,\n  in out TAllParams,\n  in out TLoaderDeps,\n> {\n  routeId: TRouteId\n  search: TFullSearchSchema\n  params: TAllParams\n  loaderDeps: TLoaderDeps\n}\n\nexport type MakeRemountDepsOptionsUnion<\n  TRouteTree extends AnyRoute = RegisteredRouter['routeTree'],\n> =\n  ParseRoute<TRouteTree> extends infer TRoute extends AnyRoute\n    ? TRoute extends any\n      ? RemountDepsOptions<\n          TRoute['id'],\n          TRoute['types']['fullSearchSchema'],\n          TRoute['types']['allParams'],\n          TRoute['types']['loaderDeps']\n        >\n      : never\n    : never\n\nexport interface RouteTypes<\n  in out TParentRoute extends AnyRoute,\n  in out TPath extends string,\n  in out TFullPath extends string,\n  in out TCustomId extends string,\n  in out TId extends string,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n  in out TRouteContextFn,\n  in out TBeforeLoadFn,\n  in out TLoaderDeps,\n  in out TLoaderFn,\n  in out TChildren,\n  in out TFileRouteTypes,\n> {\n  parentRoute: TParentRoute\n  path: TPath\n  to: TrimPathRight<TFullPath>\n  fullPath: TFullPath\n  customId: TCustomId\n  id: TId\n  searchSchema: ResolveValidatorOutput<TSearchValidator>\n  searchSchemaInput: ResolveSearchValidatorInput<TSearchValidator>\n  searchValidator: TSearchValidator\n  fullSearchSchema: ResolveFullSearchSchema<TParentRoute, TSearchValidator>\n  fullSearchSchemaInput: ResolveFullSearchSchemaInput<\n    TParentRoute,\n    TSearchValidator\n  >\n  params: TParams\n  allParams: ResolveAllParamsFromParent<TParentRoute, TParams>\n  routerContext: TRouterContext\n  routeContext: ResolveRouteContext<TRouteContextFn, TBeforeLoadFn>\n  routeContextFn: TRouteContextFn\n  beforeLoadFn: TBeforeLoadFn\n  allContext: ResolveAllContext<\n    TParentRoute,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  >\n  children: TChildren\n  loaderData: ResolveLoaderData<TLoaderFn>\n  loaderDeps: TLoaderDeps\n  fileRouteTypes: TFileRouteTypes\n}\n\nexport type ResolveFullPath<\n  TParentRoute extends AnyRoute,\n  TPath extends string,\n  TPrefixed = RoutePrefix<TParentRoute['fullPath'], TPath>,\n> = TPrefixed extends RootRouteId ? '/' : TPrefixed\n\nexport interface RouteExtensions<in out TId, in out TFullPath> {\n  id: TId\n  fullPath: TFullPath\n}\n\nexport type RouteLazyFn<TRoute extends AnyRoute> = (\n  lazyFn: () => Promise<LazyRoute>,\n) => TRoute\n\nexport type RouteAddChildrenFn<\n  in out TParentRoute extends AnyRoute,\n  in out TPath extends string,\n  in out TFullPath extends string,\n  in out TCustomId extends string,\n  in out TId extends string,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n  in out TRouteContextFn,\n  in out TBeforeLoadFn,\n  in out TLoaderDeps extends Record<string, any>,\n  in out TLoaderFn,\n  in out TFileRouteTypes,\n> = <const TNewChildren>(\n  children: Constrain<\n    TNewChildren,\n    ReadonlyArray<AnyRoute> | Record<string, AnyRoute>\n  >,\n) => Route<\n  TParentRoute,\n  TPath,\n  TFullPath,\n  TCustomId,\n  TId,\n  TSearchValidator,\n  TParams,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TNewChildren,\n  TFileRouteTypes\n>\n\nexport type RouteAddFileChildrenFn<\n  in out TParentRoute extends AnyRoute,\n  in out TPath extends string,\n  in out TFullPath extends string,\n  in out TCustomId extends string,\n  in out TId extends string,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n  in out TRouteContextFn,\n  in out TBeforeLoadFn,\n  in out TLoaderDeps extends Record<string, any>,\n  in out TLoaderFn,\n  in out TFileRouteTypes,\n> = <const TNewChildren>(\n  children: TNewChildren,\n) => Route<\n  TParentRoute,\n  TPath,\n  TFullPath,\n  TCustomId,\n  TId,\n  TSearchValidator,\n  TParams,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TNewChildren,\n  TFileRouteTypes\n>\n\nexport type RouteAddFileTypesFn<\n  TParentRoute extends AnyRoute,\n  TPath extends string,\n  TFullPath extends string,\n  TCustomId extends string,\n  TId extends string,\n  TSearchValidator,\n  TParams,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps extends Record<string, any>,\n  TLoaderFn,\n  TChildren,\n> = <TNewFileRouteTypes>() => Route<\n  TParentRoute,\n  TPath,\n  TFullPath,\n  TCustomId,\n  TId,\n  TSearchValidator,\n  TParams,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TChildren,\n  TNewFileRouteTypes\n>\n\nexport interface Route<\n  in out TParentRoute extends AnyRoute,\n  in out TPath extends string,\n  in out TFullPath extends string,\n  in out TCustomId extends string,\n  in out TId extends string,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n  in out TRouteContextFn,\n  in out TBeforeLoadFn,\n  in out TLoaderDeps extends Record<string, any>,\n  in out TLoaderFn,\n  in out TChildren,\n  in out TFileRouteTypes,\n> extends RouteExtensions<TId, TFullPath> {\n  path: TPath\n  parentRoute: TParentRoute\n  children?: TChildren\n  types: RouteTypes<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren,\n    TFileRouteTypes\n  >\n  options: RouteOptions<\n    TParentRoute,\n    TId,\n    TCustomId,\n    TFullPath,\n    TPath,\n    TSearchValidator,\n    TParams,\n    TLoaderDeps,\n    TLoaderFn,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  >\n  isRoot: TParentRoute extends AnyRoute ? true : false\n  _componentsPromise?: Promise<Array<void>>\n  lazyFn?: () => Promise<LazyRoute>\n  _lazyPromise?: Promise<void>\n  rank: number\n  to: TrimPathRight<TFullPath>\n  init: (opts: { originalIndex: number; defaultSsr?: boolean }) => void\n  update: (\n    options: UpdatableRouteOptions<\n      TParentRoute,\n      TCustomId,\n      TFullPath,\n      TParams,\n      TSearchValidator,\n      TLoaderFn,\n      TLoaderDeps,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    >,\n  ) => this\n  lazy: RouteLazyFn<\n    Route<\n      TParentRoute,\n      TPath,\n      TFullPath,\n      TCustomId,\n      TId,\n      TSearchValidator,\n      TParams,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn,\n      TChildren,\n      TFileRouteTypes\n    >\n  >\n  addChildren: RouteAddChildrenFn<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TFileRouteTypes\n  >\n  _addFileChildren: RouteAddFileChildrenFn<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TFileRouteTypes\n  >\n  _addFileTypes: RouteAddFileTypesFn<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren\n  >\n}\n\nexport type AnyRoute = Route<\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any,\n  any\n>\n\nexport type AnyRouteWithContext<TContext> = AnyRoute & {\n  types: { allContext: TContext }\n}\n\nexport type RouteOptions<\n  TParentRoute extends AnyRoute = AnyRoute,\n  TId extends string = string,\n  TCustomId extends string = string,\n  TFullPath extends string = string,\n  TPath extends string = string,\n  TSearchValidator = undefined,\n  TParams = AnyPathParams,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n  TRouterContext = {},\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n> = BaseRouteOptions<\n  TParentRoute,\n  TId,\n  TCustomId,\n  TPath,\n  TSearchValidator,\n  TParams,\n  TLoaderDeps,\n  TLoaderFn,\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn\n> &\n  UpdatableRouteOptions<\n    NoInfer<TParentRoute>,\n    NoInfer<TCustomId>,\n    NoInfer<TFullPath>,\n    NoInfer<TParams>,\n    NoInfer<TSearchValidator>,\n    NoInfer<TLoaderFn>,\n    NoInfer<TLoaderDeps>,\n    NoInfer<TRouterContext>,\n    NoInfer<TRouteContextFn>,\n    NoInfer<TBeforeLoadFn>\n  >\n\nexport type RouteContextFn<\n  in out TParentRoute extends AnyRoute,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n> = (\n  ctx: RouteContextOptions<\n    TParentRoute,\n    TSearchValidator,\n    TParams,\n    TRouterContext\n  >,\n) => any\n\nexport type BeforeLoadFn<\n  in out TParentRoute extends AnyRoute,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n  in out TRouteContextFn,\n> = (\n  ctx: BeforeLoadContextOptions<\n    TParentRoute,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn\n  >,\n) => any\n\nexport type FileBaseRouteOptions<\n  TParentRoute extends AnyRoute = AnyRoute,\n  TId extends string = string,\n  TPath extends string = string,\n  TSearchValidator = undefined,\n  TParams = {},\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n  TRouterContext = {},\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n  TRemountDepsFn = AnyContext,\n> = ParamsOptions<TPath, TParams> & {\n  validateSearch?: Constrain<TSearchValidator, AnyValidator, DefaultValidator>\n\n  shouldReload?:\n    | boolean\n    | ((\n        match: LoaderFnContext<\n          TParentRoute,\n          TId,\n          TParams,\n          TLoaderDeps,\n          TRouterContext,\n          TRouteContextFn,\n          TBeforeLoadFn\n        >,\n      ) => any)\n\n  context?: Constrain<\n    TRouteContextFn,\n    (\n      ctx: RouteContextOptions<\n        TParentRoute,\n        TParams,\n        TRouterContext,\n        TLoaderDeps\n      >,\n    ) => any\n  >\n\n  // This async function is called before a route is loaded.\n  // If an error is thrown here, the route's loader will not be called.\n  // If thrown during a navigation, the navigation will be cancelled and the error will be passed to the `onError` function.\n  // If thrown during a preload event, the error will be logged to the console.\n  beforeLoad?: Constrain<\n    TBeforeLoadFn,\n    (\n      ctx: BeforeLoadContextOptions<\n        TParentRoute,\n        TSearchValidator,\n        TParams,\n        TRouterContext,\n        TRouteContextFn\n      >,\n    ) => any\n  >\n\n  loaderDeps?: (\n    opts: FullSearchSchemaOption<TParentRoute, TSearchValidator>,\n  ) => TLoaderDeps\n\n  remountDeps?: Constrain<\n    TRemountDepsFn,\n    (\n      opt: RemountDepsOptions<\n        TId,\n        FullSearchSchemaOption<TParentRoute, TSearchValidator>,\n        Expand<ResolveAllParamsFromParent<TParentRoute, TParams>>,\n        TLoaderDeps\n      >,\n    ) => any\n  >\n\n  loader?: Constrain<\n    TLoaderFn,\n    (\n      ctx: LoaderFnContext<\n        TParentRoute,\n        TId,\n        TParams,\n        TLoaderDeps,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n    ) => any\n  >\n}\n\nexport type BaseRouteOptions<\n  TParentRoute extends AnyRoute = AnyRoute,\n  TId extends string = string,\n  TCustomId extends string = string,\n  TPath extends string = string,\n  TSearchValidator = undefined,\n  TParams = {},\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n  TRouterContext = {},\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n> = RoutePathOptions<TCustomId, TPath> &\n  FileBaseRouteOptions<\n    TParentRoute,\n    TId,\n    TPath,\n    TSearchValidator,\n    TParams,\n    TLoaderDeps,\n    TLoaderFn,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  > & {\n    getParentRoute: () => TParentRoute\n  }\n\nexport interface ContextOptions<\n  in out TParentRoute extends AnyRoute,\n  in out TParams,\n> {\n  abortController: AbortController\n  preload: boolean\n  params: Expand<ResolveAllParamsFromParent<TParentRoute, TParams>>\n  location: ParsedLocation\n  /**\n   * @deprecated Use `throw redirect({ to: '/somewhere' })` instead\n   **/\n  navigate: NavigateFn\n  buildLocation: BuildLocationFn\n  cause: 'preload' | 'enter' | 'stay'\n  matches: Array<MakeRouteMatchUnion>\n}\n\nexport interface RouteContextOptions<\n  in out TParentRoute extends AnyRoute,\n  in out TParams,\n  in out TRouterContext,\n  in out TLoaderDeps,\n> extends ContextOptions<TParentRoute, TParams> {\n  deps: TLoaderDeps\n  context: Expand<RouteContextParameter<TParentRoute, TRouterContext>>\n}\n\nexport interface BeforeLoadContextOptions<\n  in out TParentRoute extends AnyRoute,\n  in out TSearchValidator,\n  in out TParams,\n  in out TRouterContext,\n  in out TRouteContextFn,\n> extends ContextOptions<TParentRoute, TParams>,\n    FullSearchSchemaOption<TParentRoute, TSearchValidator> {\n  context: Expand<\n    BeforeLoadContextParameter<TParentRoute, TRouterContext, TRouteContextFn>\n  >\n}\n\ntype AssetFnContextOptions<\n  in out TRouteId,\n  in out TFullPath,\n  in out TParentRoute extends AnyRoute,\n  in out TParams,\n  in out TSearchValidator,\n  in out TLoaderFn,\n  in out TRouterContext,\n  in out TRouteContextFn,\n  in out TBeforeLoadFn,\n  in out TLoaderDeps,\n> = {\n  matches: Array<\n    RouteMatch<\n      TRouteId,\n      TFullPath,\n      ResolveAllParamsFromParent<TParentRoute, TParams>,\n      ResolveFullSearchSchema<TParentRoute, TSearchValidator>,\n      ResolveLoaderData<TLoaderFn>,\n      ResolveAllContext<\n        TParentRoute,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n      TLoaderDeps\n    >\n  >\n  match: RouteMatch<\n    TRouteId,\n    TFullPath,\n    ResolveAllParamsFromParent<TParentRoute, TParams>,\n    ResolveFullSearchSchema<TParentRoute, TSearchValidator>,\n    ResolveLoaderData<TLoaderFn>,\n    ResolveAllContext<\n      TParentRoute,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    >,\n    TLoaderDeps\n  >\n  params: ResolveAllParamsFromParent<TParentRoute, TParams>\n  loaderData?: ResolveLoaderData<TLoaderFn>\n}\n\nexport interface DefaultUpdatableRouteOptionsExtensions {\n  component?: unknown\n  errorComponent?: unknown\n  notFoundComponent?: unknown\n  pendingComponent?: unknown\n}\n\nexport interface UpdatableRouteOptionsExtensions\n  extends DefaultUpdatableRouteOptionsExtensions {}\n\nexport interface UpdatableRouteOptions<\n  in out TParentRoute extends AnyRoute,\n  in out TRouteId,\n  in out TFullPath,\n  in out TParams,\n  in out TSearchValidator,\n  in out TLoaderFn,\n  in out TLoaderDeps,\n  in out TRouterContext,\n  in out TRouteContextFn,\n  in out TBeforeLoadFn,\n> extends UpdatableStaticRouteOption,\n    UpdatableRouteOptionsExtensions {\n  // If true, this route will be matched as case-sensitive\n  caseSensitive?: boolean\n  // If true, this route will be forcefully wrapped in a suspense boundary\n  wrapInSuspense?: boolean\n  // The content to be rendered when the route is matched. If no component is provided, defaults to `<Outlet />`\n\n  pendingMs?: number\n  pendingMinMs?: number\n  staleTime?: number\n  gcTime?: number\n  preload?: boolean\n  preloadStaleTime?: number\n  preloadGcTime?: number\n  search?: {\n    middlewares?: Array<\n      SearchMiddleware<\n        ResolveFullSearchSchemaInput<TParentRoute, TSearchValidator>\n      >\n    >\n  }\n  /** \n  @deprecated Use search.middlewares instead\n  */\n  preSearchFilters?: Array<\n    SearchFilter<ResolveFullSearchSchema<TParentRoute, TSearchValidator>>\n  >\n  /** \n  @deprecated Use search.middlewares instead\n  */\n  postSearchFilters?: Array<\n    SearchFilter<ResolveFullSearchSchema<TParentRoute, TSearchValidator>>\n  >\n  onCatch?: (error: Error) => void\n  onError?: (err: any) => void\n  // These functions are called as route matches are loaded, stick around and leave the active\n  // matches\n  onEnter?: (\n    match: RouteMatch<\n      TRouteId,\n      TFullPath,\n      ResolveAllParamsFromParent<TParentRoute, TParams>,\n      ResolveFullSearchSchema<TParentRoute, TSearchValidator>,\n      ResolveLoaderData<TLoaderFn>,\n      ResolveAllContext<\n        TParentRoute,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n      TLoaderDeps\n    >,\n  ) => void\n  onStay?: (\n    match: RouteMatch<\n      TRouteId,\n      TFullPath,\n      ResolveAllParamsFromParent<TParentRoute, TParams>,\n      ResolveFullSearchSchema<TParentRoute, TSearchValidator>,\n      ResolveLoaderData<TLoaderFn>,\n      ResolveAllContext<\n        TParentRoute,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n      TLoaderDeps\n    >,\n  ) => void\n  onLeave?: (\n    match: RouteMatch<\n      TRouteId,\n      TFullPath,\n      ResolveAllParamsFromParent<TParentRoute, TParams>,\n      ResolveFullSearchSchema<TParentRoute, TSearchValidator>,\n      ResolveLoaderData<TLoaderFn>,\n      ResolveAllContext<\n        TParentRoute,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >,\n      TLoaderDeps\n    >,\n  ) => void\n  headers?: (\n    ctx: AssetFnContextOptions<\n      TRouteId,\n      TFullPath,\n      TParentRoute,\n      TParams,\n      TSearchValidator,\n      TLoaderFn,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps\n    >,\n  ) => Record<string, string>\n  head?: (\n    ctx: AssetFnContextOptions<\n      TRouteId,\n      TFullPath,\n      TParentRoute,\n      TParams,\n      TSearchValidator,\n      TLoaderFn,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps\n    >,\n  ) => {\n    links?: AnyRouteMatch['links']\n    scripts?: AnyRouteMatch['headScripts']\n    meta?: AnyRouteMatch['meta']\n  }\n  scripts?: (\n    ctx: AssetFnContextOptions<\n      TRouteId,\n      TFullPath,\n      TParentRoute,\n      TParams,\n      TSearchValidator,\n      TLoaderFn,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps\n    >,\n  ) => AnyRouteMatch['scripts']\n  ssr?: boolean\n  codeSplitGroupings?: Array<\n    Array<\n      | 'loader'\n      | 'component'\n      | 'pendingComponent'\n      | 'notFoundComponent'\n      | 'errorComponent'\n    >\n  >\n}\n\nexport type RouteLoaderFn<\n  in out TParentRoute extends AnyRoute = AnyRoute,\n  in out TId extends string = string,\n  in out TParams = {},\n  in out TLoaderDeps = {},\n  in out TRouterContext = {},\n  in out TRouteContextFn = AnyContext,\n  in out TBeforeLoadFn = AnyContext,\n> = (\n  match: LoaderFnContext<\n    TParentRoute,\n    TId,\n    TParams,\n    TLoaderDeps,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  >,\n) => any\n\nexport interface LoaderFnContext<\n  in out TParentRoute extends AnyRoute = AnyRoute,\n  in out TId extends string = string,\n  in out TParams = {},\n  in out TLoaderDeps = {},\n  in out TRouterContext = {},\n  in out TRouteContextFn = AnyContext,\n  in out TBeforeLoadFn = AnyContext,\n> {\n  abortController: AbortController\n  preload: boolean\n  params: Expand<ResolveAllParamsFromParent<TParentRoute, TParams>>\n  deps: TLoaderDeps\n  context: Expand<\n    ResolveAllContext<\n      TParentRoute,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    >\n  >\n  location: ParsedLocation // Do not supply search schema here so as to demotivate people from trying to shortcut loaderDeps\n  /**\n   * @deprecated Use `throw redirect({ to: '/somewhere' })` instead\n   **/\n  navigate: (opts: NavigateOptions<AnyRouter>) => Promise<void> | void\n  // root route does not have a parent match\n  parentMatchPromise: TId extends RootRouteId\n    ? never\n    : Promise<MakeRouteMatchFromRoute<TParentRoute>>\n  cause: 'preload' | 'enter' | 'stay'\n  route: AnyRoute\n}\n\nexport type RootRouteOptions<\n  TSearchValidator = undefined,\n  TRouterContext = {},\n  TRouteContextFn = AnyContext,\n  TBeforeLoadFn = AnyContext,\n  TLoaderDeps extends Record<string, any> = {},\n  TLoaderFn = undefined,\n> = Omit<\n  RouteOptions<\n    any, // TParentRoute\n    RootRouteId, // TId\n    RootRouteId, // TCustomId\n    '', // TFullPath\n    '', // TPath\n    TSearchValidator,\n    {}, // TParams\n    TLoaderDeps,\n    TLoaderFn,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  >,\n  | 'path'\n  | 'id'\n  | 'getParentRoute'\n  | 'caseSensitive'\n  | 'parseParams'\n  | 'stringifyParams'\n  | 'params'\n>\n\nexport type RouteConstraints = {\n  TParentRoute: AnyRoute\n  TPath: string\n  TFullPath: string\n  TCustomId: string\n  TId: string\n  TSearchSchema: AnySchema\n  TFullSearchSchema: AnySchema\n  TParams: Record<string, any>\n  TAllParams: Record<string, any>\n  TParentContext: AnyContext\n  TRouteContext: RouteContext\n  TAllContext: AnyContext\n  TRouterContext: AnyContext\n  TChildren: unknown\n  TRouteTree: AnyRoute\n}\n\nexport type RouteTypesById<TRouter extends AnyRouter, TId> = RouteById<\n  TRouter['routeTree'],\n  TId\n>['types']\n\nexport type RouteMask<TRouteTree extends AnyRoute> = {\n  routeTree: TRouteTree\n  from: RoutePaths<TRouteTree>\n  to?: any\n  params?: any\n  search?: any\n  hash?: any\n  state?: any\n  unmaskOnReload?: boolean\n}\n\n/**\n * @deprecated Use `ErrorComponentProps` instead.\n */\nexport type ErrorRouteProps = {\n  error: unknown\n  info?: { componentStack: string }\n  reset: () => void\n}\n\nexport type ErrorComponentProps = {\n  error: Error\n  info?: { componentStack: string }\n  reset: () => void\n}\nexport type NotFoundRouteProps = {\n  // TODO: Make sure this is `| null | undefined` (this is for global not-founds)\n  data: unknown\n}\n\nexport class BaseRoute<\n  in out TParentRoute extends AnyRoute = AnyRoute,\n  in out TPath extends string = '/',\n  in out TFullPath extends string = ResolveFullPath<TParentRoute, TPath>,\n  in out TCustomId extends string = string,\n  in out TId extends string = ResolveId<TParentRoute, TCustomId, TPath>,\n  in out TSearchValidator = undefined,\n  in out TParams = ResolveParams<TPath>,\n  in out TRouterContext = AnyContext,\n  in out TRouteContextFn = AnyContext,\n  in out TBeforeLoadFn = AnyContext,\n  in out TLoaderDeps extends Record<string, any> = {},\n  in out TLoaderFn = undefined,\n  in out TChildren = unknown,\n  in out TFileRouteTypes = unknown,\n> {\n  isRoot: TParentRoute extends AnyRoute ? true : false\n  options: RouteOptions<\n    TParentRoute,\n    TId,\n    TCustomId,\n    TFullPath,\n    TPath,\n    TSearchValidator,\n    TParams,\n    TLoaderDeps,\n    TLoaderFn,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn\n  >\n\n  // The following properties are set up in this.init()\n  parentRoute!: TParentRoute\n  private _id!: TId\n  private _path!: TPath\n  private _fullPath!: TFullPath\n  private _to!: TrimPathRight<TFullPath>\n  private _ssr!: boolean\n\n  public get to() {\n    return this._to\n  }\n\n  public get id() {\n    return this._id\n  }\n\n  public get path() {\n    return this._path\n  }\n\n  public get fullPath() {\n    return this._fullPath\n  }\n\n  public get ssr() {\n    return this._ssr\n  }\n\n  // Optional\n  children?: TChildren\n  originalIndex?: number\n  rank!: number\n  lazyFn?: () => Promise<LazyRoute>\n  _lazyPromise?: Promise<void>\n  _componentsPromise?: Promise<Array<void>>\n\n  constructor(\n    options?: RouteOptions<\n      TParentRoute,\n      TId,\n      TCustomId,\n      TFullPath,\n      TPath,\n      TSearchValidator,\n      TParams,\n      TLoaderDeps,\n      TLoaderFn,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    >,\n  ) {\n    this.options = (options as any) || {}\n    this.isRoot = !options?.getParentRoute as any\n\n    if ((options as any)?.id && (options as any)?.path) {\n      throw new Error(`Route cannot have both an 'id' and a 'path' option.`)\n    }\n  }\n\n  types!: RouteTypes<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren,\n    TFileRouteTypes\n  >\n\n  init = (opts: { originalIndex: number; defaultSsr?: boolean }): void => {\n    this.originalIndex = opts.originalIndex\n\n    const options = this.options as\n      | (RouteOptions<\n          TParentRoute,\n          TId,\n          TCustomId,\n          TFullPath,\n          TPath,\n          TSearchValidator,\n          TParams,\n          TLoaderDeps,\n          TLoaderFn,\n          TRouterContext,\n          TRouteContextFn,\n          TBeforeLoadFn\n        > &\n          RoutePathOptionsIntersection<TCustomId, TPath>)\n      | undefined\n\n    const isRoot = !options?.path && !options?.id\n\n    this.parentRoute = this.options.getParentRoute?.()\n\n    if (isRoot) {\n      this._path = rootRouteId as TPath\n    } else if (!this.parentRoute) {\n      throw new Error(\n        `Child Route instances must pass a 'getParentRoute: () => ParentRoute' option that returns a Route instance.`,\n      )\n    }\n\n    let path: undefined | string = isRoot ? rootRouteId : options?.path\n\n    // If the path is anything other than an index path, trim it up\n    if (path && path !== '/') {\n      path = trimPathLeft(path)\n    }\n\n    const customId = options?.id || path\n\n    // Strip the parentId prefix from the first level of children\n    let id = isRoot\n      ? rootRouteId\n      : joinPaths([\n          this.parentRoute.id === rootRouteId ? '' : this.parentRoute.id,\n          customId,\n        ])\n\n    if (path === rootRouteId) {\n      path = '/'\n    }\n\n    if (id !== rootRouteId) {\n      id = joinPaths(['/', id])\n    }\n\n    const fullPath =\n      id === rootRouteId ? '/' : joinPaths([this.parentRoute.fullPath, path])\n\n    this._path = path as TPath\n    this._id = id as TId\n    this._fullPath = fullPath as TFullPath\n    this._to = fullPath as TrimPathRight<TFullPath>\n    this._ssr = options?.ssr ?? opts.defaultSsr ?? true\n  }\n\n  clone = (other: typeof this) => {\n    this._path = other._path\n    this._id = other._id\n    this._fullPath = other._fullPath\n    this._to = other._to\n    this._ssr = other._ssr\n    this.options.getParentRoute = other.options.getParentRoute\n    this.children = other.children\n  }\n\n  addChildren: RouteAddChildrenFn<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TFileRouteTypes\n  > = (children) => {\n    return this._addFileChildren(children) as any\n  }\n\n  _addFileChildren: RouteAddFileChildrenFn<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TFileRouteTypes\n  > = (children) => {\n    if (Array.isArray(children)) {\n      this.children = children as TChildren\n    }\n\n    if (typeof children === 'object' && children !== null) {\n      this.children = Object.values(children) as TChildren\n    }\n\n    return this as any\n  }\n\n  _addFileTypes: RouteAddFileTypesFn<\n    TParentRoute,\n    TPath,\n    TFullPath,\n    TCustomId,\n    TId,\n    TSearchValidator,\n    TParams,\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren\n  > = () => {\n    return this as any\n  }\n\n  updateLoader = <TNewLoaderFn>(options: {\n    loader: Constrain<\n      TNewLoaderFn,\n      RouteLoaderFn<\n        TParentRoute,\n        TCustomId,\n        TParams,\n        TLoaderDeps,\n        TRouterContext,\n        TRouteContextFn,\n        TBeforeLoadFn\n      >\n    >\n  }) => {\n    Object.assign(this.options, options)\n    return this as unknown as BaseRoute<\n      TParentRoute,\n      TPath,\n      TFullPath,\n      TCustomId,\n      TId,\n      TSearchValidator,\n      TParams,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TNewLoaderFn,\n      TChildren,\n      TFileRouteTypes\n    >\n  }\n\n  update = (\n    options: UpdatableRouteOptions<\n      TParentRoute,\n      TCustomId,\n      TFullPath,\n      TParams,\n      TSearchValidator,\n      TLoaderFn,\n      TLoaderDeps,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn\n    >,\n  ): this => {\n    Object.assign(this.options, options)\n    return this\n  }\n\n  lazy: RouteLazyFn<\n    Route<\n      TParentRoute,\n      TPath,\n      TFullPath,\n      TCustomId,\n      TId,\n      TSearchValidator,\n      TParams,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn,\n      TChildren,\n      TFileRouteTypes\n    >\n  > = (lazyFn) => {\n    this.lazyFn = lazyFn\n    return this\n  }\n}\n\nexport class BaseRouteApi<TId, TRouter extends AnyRouter = RegisteredRouter> {\n  id: TId\n\n  constructor({ id }: { id: TId }) {\n    this.id = id as any\n  }\n\n  notFound = (opts?: NotFoundError) => {\n    return notFound({ routeId: this.id as string, ...opts })\n  }\n}\n\nexport interface RootRoute<\n  in out TSearchValidator = undefined,\n  in out TRouterContext = {},\n  in out TRouteContextFn = AnyContext,\n  in out TBeforeLoadFn = AnyContext,\n  in out TLoaderDeps extends Record<string, any> = {},\n  in out TLoaderFn = undefined,\n  in out TChildren = unknown,\n  in out TFileRouteTypes = unknown,\n> extends Route<\n    any, // TParentRoute\n    '/', // TPath\n    '/', // TFullPath\n    string, // TCustomId\n    RootRouteId, // TId\n    TSearchValidator, // TSearchValidator\n    {}, // TParams\n    TRouterContext,\n    TRouteContextFn,\n    TBeforeLoadFn,\n    TLoaderDeps,\n    TLoaderFn,\n    TChildren, // TChildren\n    TFileRouteTypes\n  > {}\n\nexport class BaseRootRoute<\n  in out TSearchValidator = undefined,\n  in out TRouterContext = {},\n  in out TRouteContextFn = AnyContext,\n  in out TBeforeLoadFn = AnyContext,\n  in out TLoaderDeps extends Record<string, any> = {},\n  in out TLoaderFn = undefined,\n  in out TChildren = unknown,\n  in out TFileRouteTypes = unknown,\n> extends BaseRoute<\n  any, // TParentRoute\n  '/', // TPath\n  '/', // TFullPath\n  string, // TCustomId\n  RootRouteId, // TId\n  TSearchValidator, // TSearchValidator\n  {}, // TParams\n  TRouterContext,\n  TRouteContextFn,\n  TBeforeLoadFn,\n  TLoaderDeps,\n  TLoaderFn,\n  TChildren, // TChildren\n  TFileRouteTypes\n> {\n  constructor(\n    options?: RootRouteOptions<\n      TSearchValidator,\n      TRouterContext,\n      TRouteContextFn,\n      TBeforeLoadFn,\n      TLoaderDeps,\n      TLoaderFn\n    >,\n  ) {\n    super(options as any)\n  }\n}\n\n//\n"], "mappings": ";AA6FA,IAAM,gBAAgB;AACtB,IAAM,gBAAgB;AACtB,IAAM,oBAAoB;AAEnB,SAAS,cAAc,MAgBZ;AACZ,MAAA,WAAW,KAAK,YAAY;AAC1B,QAAA,cAAA,oBAAkB,IAAoC;AAEtD,QAAA,SAAS,CAAC,WAAoC;AAClD,eAAW,KAAK,YAAY;AAChB,gBAAA,QAAQ,CAAC,eAAe,WAAW,EAAE,UAAU,OAAA,CAAQ,CAAC;EACtE;AAEM,QAAA,oBAAoB,CAAC,WAAoC;AAC7D,QAAI,KAAK,uBAAuB,KAAM,QAAO,MAAM;QAC9C,YAAW,KAAK,YAAY;EACnC;AAEA,QAAM,gBAAgB,OAAO;IAC3B;IACA;IACA,GAAG;EAAA,MACkB;AAtCzB,QAAA,IAAA;AAuCU,UAAA,iBAAgB,gBAAA,OAAA,SAAA,aAAc,kBAAiB;AACrD,QAAI,eAAe;AACZ,WAAA;AACL;IAAA;AAGF,UAAM,aAAW,KAAA,KAAK,gBAAL,OAAA,SAAA,GAAA,KAAA,IAAA,MAAwB,CAAC;AAC1C,UAAM,kBACJ,WAAW,SAAS,UAAU,WAAW,SAAS;AACpD,QAAI,OAAO,aAAa,eAAe,SAAS,UAAU,iBAAiB;AACzE,iBAAW,WAAW,UAAU;AAC9B,cAAM,eAAe,UAAU,WAAW,MAAM,WAAW,KAAK;AAC1D,cAAA,YAAY,MAAM,QAAQ,UAAU;UACxC,iBAAiB;UACjB;UACA,QAAQ,WAAW;QAAA,CACpB;AACD,YAAI,WAAW;AACb,WAAA,KAAA,KAAK,cAAL,OAAA,SAAA,GAAA,KAAA,IAAA;AACA;QAAA;MACF;IACF;AAGG,SAAA;EACP;AAEO,SAAA;IACL,IAAI,WAAW;AACN,aAAA;IACT;IACA,IAAI,SAAS;AACX,aAAO,KAAK,UAAU;IACxB;IACA;IACA,WAAW,CAAC,OAAuC;AACjD,kBAAY,IAAI,EAAE;AAElB,aAAO,MAAM;AACX,oBAAY,OAAO,EAAE;MACvB;IACF;IACA,MAAM,CAAC,MAAM,OAAO,iBAAiB;AAC7B,YAAA,eAAe,SAAS,MAAM,aAAa;AACzC,cAAA,kBAAkB,eAAe,GAAG,KAAK;AACnC,oBAAA;QACZ,MAAM,MAAM;AACL,eAAA,UAAU,MAAM,KAAK;AACnB,iBAAA,EAAE,MAAM,OAAA,CAAQ;QACzB;QACA;QACA,MAAM;QACN;QACA;MAAA,CACD;IACH;IACA,SAAS,CAAC,MAAM,OAAO,iBAAiB;AAChC,YAAA,eAAe,SAAS,MAAM,aAAa;AACzC,cAAA,kBAAkB,cAAc,KAAK;AAC/B,oBAAA;QACZ,MAAM,MAAM;AACL,eAAA,aAAa,MAAM,KAAK;AACtB,iBAAA,EAAE,MAAM,UAAA,CAAW;QAC5B;QACA;QACA,MAAM;QACN;QACA;MAAA,CACD;IACH;IACA,IAAI,CAAC,OAAO,iBAAiB;AACb,oBAAA;QACZ,MAAM,MAAM;AACV,eAAK,GAAG,KAAK;AACb,4BAAkB,EAAE,MAAM,MAAM,MAAA,CAAO;QACzC;QACA;QACA,MAAM;MAAA,CACP;IACH;IACA,MAAM,CAAC,iBAAiB;AACR,oBAAA;QACZ,MAAM,MAAM;AACL,eAAA,MAAK,gBAAA,OAAA,SAAA,aAAc,kBAAiB,KAAK;AAC5B,4BAAA,EAAE,MAAM,OAAA,CAAQ;QACpC;QACA;QACA,MAAM;MAAA,CACP;IACH;IACA,SAAS,CAAC,iBAAiB;AACX,oBAAA;QACZ,MAAM,MAAM;AACL,eAAA,SAAQ,gBAAA,OAAA,SAAA,aAAc,kBAAiB,KAAK;AAC/B,4BAAA,EAAE,MAAM,UAAA,CAAW;QACvC;QACA;QACA,MAAM;MAAA,CACP;IACH;IACA,WAAW,MAAM,SAAS,MAAM,aAAa,MAAM;IACnD,YAAY,CAAC,QAAQ,KAAK,WAAW,GAAG;IACxC,OAAO,CAAC,YAAY;AA7IxB,UAAA;AA8IM,UAAI,CAAC,KAAK,YAAa,QAAO,MAAM;MAAC;AACrC,YAAM,aAAW,KAAA,KAAK,gBAAL,OAAA,SAAA,GAAA,KAAA,IAAA,MAAwB,CAAC;AAC1C,WAAK,YAAY,CAAC,GAAG,UAAU,OAAO,CAAC;AAEvC,aAAO,MAAM;AAlJnB,YAAAA,KAAA;AAmJQ,cAAMC,cAAWD,MAAA,KAAK,gBAAL,OAAA,SAAAA,IAAA,KAAA,IAAA,MAAwB,CAAC;AAC1C,SAAA,KAAA,KAAK,gBAAL,OAAA,SAAA,GAAA,KAAA,MAAmBC,UAAS,OAAO,CAAC,MAAM,MAAM,OAAO,CAAA;MACzD;IACF;IACA,OAAO,MAAA;AAvJX,UAAA;AAuJiB,cAAA,KAAA,KAAK,UAAL,OAAA,SAAA,GAAA,KAAA,IAAA;IAAA;IACb,SAAS,MAAA;AAxJb,UAAA;AAwJmB,cAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAA,KAAA,IAAA;IAAA;IACf;EACF;AACF;AAEA,SAAS,kBAAkB,OAAe,OAAiC;AACzE,MAAI,CAAC,OAAO;AACV,YAAQ,CAAC;EAAA;AAEJ,SAAA;IACL,GAAG;IACH,KAAK,gBAAgB;IACrB,CAAC,aAAa,GAAG;EACnB;AACF;AAkBO,SAAS,qBAAqB,MAInB;AA5LlB,MAAA;AA6LE,QAAM,OACJ,QAAA,OAAA,SAAA,KAAM,YACL,OAAO,aAAa,cAAc,SAAU;AAEzC,QAAA,oBAAoB,IAAI,QAAQ;AAChC,QAAA,uBAAuB,IAAI,QAAQ;AAEzC,MAAI,WAAqC,CAAC;AAC1C,QAAM,eAAe,MAAM;AACrB,QAAA,eAAe,CAAC,gBACnB,WAAW;AAEd,QAAM,cAAa,QAAA,OAAA,SAAA,KAAM,gBAAe,CAAC,SAAS;AAC5C,QAAA,iBACJ,QAAA,OAAA,SAAA,KAAM,mBACL,MACC;IACE,GAAG,IAAI,SAAS,QAAQ,GAAG,IAAI,SAAS,MAAM,GAAG,IAAI,SAAS,IAAI;IAClE,IAAI,QAAQ;EAAA;AAIlB,MAAI,GAAC,KAAA,IAAI,QAAQ,UAAZ,OAAA,SAAA,GAAmB,MAAK;AAC3B,QAAI,QAAQ;MACV;QACE,CAAC,aAAa,GAAG;QACjB,KAAK,gBAAgB;MACvB;MACA;IACF;EAAA;AAGF,MAAI,kBAAkB,cAAc;AAChC,MAAA;AAEJ,MAAI,cAAc;AAClB,MAAI,gBAAgB;AACpB,MAAI,qBAAqB;AACzB,MAAI,yBAAyB;AAE7B,QAAM,cAAc,MAAM;AAEtB,MAAA;AAaA,MAAA;AAGJ,QAAM,QAAQ,MAAM;AAClB,QAAI,CAAC,MAAM;AACT;IAAA;AAIF,YAAQ,qBAAqB;AAG5B,KAAC,KAAK,SAAS,IAAI,QAAQ,YAAY,IAAI,QAAQ;MAClD,KAAK;MACL;MACA,KAAK;IACP;AAGA,YAAQ,qBAAqB;AAGtB,WAAA;AACK,gBAAA;AACO,uBAAA;EACrB;AAGA,QAAM,qBAAqB,CACzB,MACA,UACA,UACG;AACG,UAAA,OAAO,WAAW,QAAQ;AAEhC,QAAI,CAAC,WAAW;AACK,yBAAA;IAAA;AAIH,sBAAA,UAAU,UAAU,KAAK;AAGpC,WAAA;MACL;MACA;MACA,SAAQ,QAAA,OAAA,SAAA,KAAM,WAAU,SAAS;IACnC;AAEA,QAAI,CAAC,WAAW;AAEd,kBAAY,QAAQ,QAAQ,EAAE,KAAK,MAAM,MAAA,CAAO;IAAA;EAEpD;AAGM,QAAA,YAAY,CAAC,SAA6B;AAC9C,sBAAkB,cAAc;AACxB,YAAA,OAAO,EAAE,KAAA,CAAM;EACzB;AAEA,QAAM,iBAAiB,YAAY;AACjC,QAAI,eAAe;AACD,sBAAA;AAChB;IAAA;AAGF,UAAM,eAAe,cAAc;AACnC,UAAM,QACJ,aAAa,MAAM,aAAa,IAAI,gBAAgB,MAAM,aAAa;AACzE,UAAM,YAAY,UAAU;AAC5B,UAAM,SAAS,UAAU;AACzB,UAAM,OAAQ,CAAC,aAAa,CAAC,UAAW;AAC1B,kBAAA;AAEd,UAAM,SAAS,OAAO,OAAO,SAAS,SAAS;AAC/C,UAAM,SAAkC,OACpC;MACE,MAAM;MACN,OAAO;IAAA,IAET;MACE,MAAM,SAAS,SAAS;IAC1B;AAEJ,QAAI,oBAAoB;AACD,2BAAA;IAAA,OAChB;AACL,YAAMA,YAAW,aAAa;AAC9B,UAAI,OAAO,aAAa,eAAeA,UAAS,QAAQ;AACtD,mBAAW,WAAWA,WAAU;AACxB,gBAAA,YAAY,MAAM,QAAQ,UAAU;YACxC;YACA;YACA;UAAA,CACD;AACD,cAAI,WAAW;AACG,4BAAA;AACZ,gBAAA,QAAQ,GAAG,CAAC;AAChB,oBAAQ,OAAO,MAAM;AACrB;UAAA;QACF;MACF;IACF;AAGF,sBAAkB,cAAc;AAChC,YAAQ,OAAO,MAAM;EACvB;AAEM,QAAA,iBAAiB,CAAC,MAAyB;AAC/C,QAAI,wBAAwB;AACD,+BAAA;AACzB;IAAA;AAGF,QAAI,cAAc;AAGlB,UAAMA,YAAW,aAAa;AAC9B,QAAI,OAAO,aAAa,eAAeA,UAAS,QAAQ;AACtD,iBAAW,WAAWA,WAAU;AACxB,cAAA,yBAAyB,QAAQ,sBAAsB;AAC7D,YAAI,2BAA2B,MAAM;AACrB,wBAAA;AACd;QAAA;AAGF,YACE,OAAO,2BAA2B,cAClC,uBAAA,MAA6B,MAC7B;AACc,wBAAA;AACd;QAAA;MACF;IACF;AAGF,QAAI,aAAa;AACf,QAAE,eAAe;AACjB,aAAQ,EAAE,cAAc;IAAA;AAE1B;EACF;AAEA,QAAM,UAAU,cAAc;IAC5B;IACA,WAAW,MAAM,IAAI,QAAQ;IAC7B,WAAW,CAAC,MAAM,UAAU,mBAAmB,QAAQ,MAAM,KAAK;IAClE,cAAc,CAAC,MAAM,UAAU,mBAAmB,WAAW,MAAM,KAAK;IACxE,MAAM,CAAC,kBAAkB;AACvB,UAAI,cAAoC,sBAAA;AACf,+BAAA;AAClB,aAAA,IAAI,QAAQ,KAAK;IAC1B;IACA,SAAS,CAAC,kBAAkB;AAC1B,UAAI,cAAoC,sBAAA;AACf,+BAAA;AACzB,UAAI,QAAQ,QAAQ;IACtB;IACA,IAAI,CAAC,MAAM;AACK,oBAAA;AACV,UAAA,QAAQ,GAAG,CAAC;IAClB;IACA,YAAY,CAAC,SAAS,WAAW,IAAI;IACrC;IACA,SAAS,MAAM;AACb,UAAI,QAAQ,YAAY;AACxB,UAAI,QAAQ,eAAe;AACvB,UAAA,oBAAoB,mBAAmB,gBAAgB;QACzD,SAAS;MAAA,CACV;AACG,UAAA,oBAAoB,eAAe,cAAc;IACvD;IACA,WAAW,MAAM;AAGX,UAAA,oBAAoB,oBAAoB,kBAAkB;AAC1C,0BAAA;MAAA;IAEtB;IACA,aAAa;IACb,aAAa;IACb,qBAAqB;EAAA,CACtB;AAED,MAAI,iBAAiB,mBAAmB,gBAAgB,EAAE,SAAS,KAAA,CAAM;AACrE,MAAA,iBAAiB,eAAe,cAAc;AAE9C,MAAA,QAAQ,YAAY,YAAa,MAAkB;AACrD,UAAM,MAAM,kBAAkB,MAAM,IAAI,SAAS,IAAW;AAC5D,QAAI,CAAC,QAAQ,mBAAoB,WAAU,MAAM;AAC1C,WAAA;EACT;AAEI,MAAA,QAAQ,eAAe,YAAa,MAAkB;AACxD,UAAM,MAAM,qBAAqB,MAAM,IAAI,SAAS,IAAW;AAC/D,QAAI,CAAC,QAAQ,mBAAoB,WAAU,SAAS;AAC7C,WAAA;EACT;AAEO,SAAA;AACT;AAEO,SAAS,kBAAkB,MAAwC;AACxE,QAAM,OACJ,QAAA,OAAA,SAAA,KAAM,YACL,OAAO,aAAa,cAAc,SAAU;AAC/C,SAAO,qBAAqB;IAC1B,QAAQ;IACR,eAAe,MAAM;AACb,YAAA,YAAY,IAAI,SAAS,KAAK,MAAM,GAAG,EAAE,MAAM,CAAC;AAChD,YAAA,WAAW,UAAU,CAAC,KAAK;AAC3B,YAAA,aAAa,IAAI,SAAS;AAC1B,YAAA,cAAc,UAAU,MAAM,CAAC;AAC/B,YAAA,WACJ,YAAY,WAAW,IAAI,KAAK,IAAI,YAAY,KAAK,GAAG,CAAC;AAC3D,YAAM,WAAW,GAAG,QAAQ,GAAG,UAAU,GAAG,QAAQ;AACpD,aAAO,UAAU,UAAU,IAAI,QAAQ,KAAK;IAC9C;IACA,YAAY,CAAC,SACX,GAAG,IAAI,SAAS,QAAQ,GAAG,IAAI,SAAS,MAAM,IAAI,IAAI;EAAA,CACzD;AACH;AAEO,SAAS,oBACd,OAGI;EACF,gBAAgB,CAAC,GAAG;AACtB,GACe;AACf,QAAM,UAAU,KAAK;AACrB,MAAI,QAAQ,KAAK,eACb,KAAK,IAAI,KAAK,IAAI,KAAK,cAAc,CAAC,GAAG,QAAQ,SAAS,CAAC,IAC3D,QAAQ,SAAS;AACrB,QAAM,SAAS,QAAQ;IAAI,CAAC,QAAQC,WAClC,kBAAkBA,QAAO,MAAS;EACpC;AAEM,QAAA,cAAc,MAAM,UAAU,QAAQ,KAAK,GAAI,OAAO,KAAK,CAAC;AAElE,SAAO,cAAc;IACnB;IACA,WAAW,MAAM,QAAQ;IACzB,WAAW,CAAC,MAAM,UAAU;AAEtB,UAAA,QAAQ,QAAQ,SAAS,GAAG;AACtB,gBAAA,OAAO,QAAQ,CAAC;AACjB,eAAA,OAAO,QAAQ,CAAC;MAAA;AAEzB,aAAO,KAAK,KAAK;AACjB,cAAQ,KAAK,IAAI;AACjB,cAAQ,KAAK,IAAI,QAAQ,SAAS,GAAG,CAAC;IACxC;IACA,cAAc,CAAC,MAAM,UAAU;AAC7B,aAAO,KAAK,IAAI;AAChB,cAAQ,KAAK,IAAI;IACnB;IACA,MAAM,MAAM;AACV,cAAQ,KAAK,IAAI,QAAQ,GAAG,CAAC;IAC/B;IACA,SAAS,MAAM;AACb,cAAQ,KAAK,IAAI,QAAQ,GAAG,QAAQ,SAAS,CAAC;IAChD;IACA,IAAI,CAAC,MAAM;AACD,cAAA,KAAK,IAAI,KAAK,IAAI,QAAQ,GAAG,CAAC,GAAG,QAAQ,SAAS,CAAC;IAC7D;IACA,YAAY,CAAC,SAAS;EAAA,CACvB;AACH;AAEgB,SAAA,UACd,MACA,OACiB;AACX,QAAA,YAAY,KAAK,QAAQ,GAAG;AAC5B,QAAA,cAAc,KAAK,QAAQ,GAAG;AAE7B,SAAA;IACL;IACA,UAAU,KAAK;MACb;MACA,YAAY,IACR,cAAc,IACZ,KAAK,IAAI,WAAW,WAAW,IAC/B,YACF,cAAc,IACZ,cACA,KAAK;IACb;IACA,MAAM,YAAY,KAAK,KAAK,UAAU,SAAS,IAAI;IACnD,QACE,cAAc,KACV,KAAK,MAAM,aAAa,cAAc,KAAK,SAAY,SAAS,IAChE;IACN,OAAO,SAAS,EAAE,CAAC,aAAa,GAAG,GAAG,KAAK,gBAAkB,EAAA;EAC/D;AACF;AAGA,SAAS,kBAAkB;AACjB,UAAA,KAAK,OAAA,IAAW,GAAG,SAAS,EAAE,EAAE,UAAU,CAAC;AACrD;;;ACjoBA,IAAI,eAAe;AACnB,IAAI,SAAS;AACb,SAAS,UAAU,WAAW,SAAS;AACnC,MAAI,WAAW;AACX;AAAA,EACJ;AACA,MAAI,cAAc;AACd,UAAM,IAAI,MAAM,MAAM;AAAA,EAC1B;AACA,MAAI,WAAW,OAAO,YAAY,aAAa,QAAQ,IAAI;AAC3D,MAAI,QAAQ,WAAW,GAAG,OAAO,QAAQ,IAAI,EAAE,OAAO,QAAQ,IAAI;AAClE,QAAM,IAAI,MAAM,KAAK;AACzB;;;AC+JO,SAAS,KAAQ,KAAe;AAC9B,SAAA,IAAI,IAAI,SAAS,CAAC;AAC3B;AAEA,SAAS,WAAW,GAAuB;AACzC,SAAO,OAAO,MAAM;AACtB;AAEgB,SAAA,iBACd,SACA,UACS;AACL,MAAA,WAAW,OAAO,GAAG;AACvB,WAAO,QAAQ,QAAQ;EAAA;AAGlB,SAAA;AACT;AAEgB,SAAA,KACd,QACA,MACoB;AACpB,SAAO,KAAK,OAAO,CAAC,KAAU,QAAc;AACtC,QAAA,GAAG,IAAI,OAAO,GAAG;AACd,WAAA;EACT,GAAG,CAAA,CAAS;AACd;AAQgB,SAAA,iBAAoB,MAAW,OAAa;AAC1D,MAAI,SAAS,OAAO;AACX,WAAA;EAAA;AAGT,QAAM,OAAO;AAEb,QAAM,QAAQ,aAAa,IAAI,KAAK,aAAa,IAAI;AAErD,MAAI,SAAU,oBAAoB,IAAI,KAAK,oBAAoB,IAAI,GAAI;AACrE,UAAM,YAAY,QACd,OACC,OAAO,KAAK,IAAI,EAAqB;MACpC,OAAO,sBAAsB,IAAI;IACnC;AACJ,UAAM,WAAW,UAAU;AAC3B,UAAM,YAAY,QACd,OACC,OAAO,KAAK,IAAI,EAAqB;MACpC,OAAO,sBAAsB,IAAI;IACnC;AACJ,UAAM,WAAW,UAAU;AAC3B,UAAM,OAAY,QAAQ,CAAA,IAAK,CAAC;AAEhC,QAAI,aAAa;AAEjB,aAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,YAAM,MAAM,QAAQ,IAAK,UAAU,CAAC;AACpC,WACI,CAAC,SAAS,UAAU,SAAS,GAAG,KAAM,UACxC,KAAK,GAAG,MAAM,UACd,KAAK,GAAG,MAAM,QACd;AACA,aAAK,GAAG,IAAI;AACZ;MAAA,OACK;AACA,aAAA,GAAG,IAAI,iBAAiB,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC;AAC7C,YAAA,KAAK,GAAG,MAAM,KAAK,GAAG,KAAK,KAAK,GAAG,MAAM,QAAW;AACtD;QAAA;MACF;IACF;AAGF,WAAO,aAAa,YAAY,eAAe,WAAW,OAAO;EAAA;AAG5D,SAAA;AACT;AAOA,SAAS,oBAAoB,GAAQ;AACnC;;IAEE,cAAc,CAAC,KACf,OAAO,oBAAoB,CAAC,EAAE,WAAW,OAAO,KAAK,CAAC,EAAE;;AAE5D;AAGO,SAAS,cAAc,GAAQ;AAChC,MAAA,CAAC,mBAAmB,CAAC,GAAG;AACnB,WAAA;EAAA;AAIT,QAAM,OAAO,EAAE;AACX,MAAA,OAAO,SAAS,aAAa;AACxB,WAAA;EAAA;AAIT,QAAM,OAAO,KAAK;AACd,MAAA,CAAC,mBAAmB,IAAI,GAAG;AACtB,WAAA;EAAA;AAIT,MAAI,CAAC,KAAK,eAAe,eAAe,GAAG;AAClC,WAAA;EAAA;AAIF,SAAA;AACT;AAEA,SAAS,mBAAmB,GAAQ;AAClC,SAAO,OAAO,UAAU,SAAS,KAAK,CAAC,MAAM;AAC/C;AAEO,SAAS,aAAa,OAAyC;AAC7D,SAAA,MAAM,QAAQ,KAAK,KAAK,MAAM,WAAW,OAAO,KAAK,KAAK,EAAE;AACrE;AAEA,SAAS,cAAc,KAAU,iBAA0B;AACrD,MAAA,OAAO,OAAO,KAAK,GAAG;AAC1B,MAAI,iBAAiB;AACnB,WAAO,KAAK,OAAO,CAAC,QAAQ,IAAI,GAAG,MAAM,MAAS;EAAA;AAE7C,SAAA;AACT;AAEgB,SAAA,UACd,GACA,GACA,MACS;AACT,MAAI,MAAM,GAAG;AACJ,WAAA;EAAA;AAGL,MAAA,OAAO,MAAM,OAAO,GAAG;AAClB,WAAA;EAAA;AAGT,MAAI,cAAc,CAAC,KAAK,cAAc,CAAC,GAAG;AAClC,UAAA,mBAAkB,QAAA,OAAA,SAAA,KAAM,oBAAmB;AAC3C,UAAA,QAAQ,cAAc,GAAG,eAAe;AACxC,UAAA,QAAQ,cAAc,GAAG,eAAe;AAE9C,QAAI,EAAC,QAAA,OAAA,SAAA,KAAM,YAAW,MAAM,WAAW,MAAM,QAAQ;AAC5C,aAAA;IAAA;AAGT,WAAO,MAAM,MAAM,CAAC,QAAQ,UAAU,EAAE,GAAG,GAAG,EAAE,GAAG,GAAG,IAAI,CAAC;EAAA;AAG7D,MAAI,MAAM,QAAQ,CAAC,KAAK,MAAM,QAAQ,CAAC,GAAG;AACpC,QAAA,EAAE,WAAW,EAAE,QAAQ;AAClB,aAAA;IAAA;AAET,WAAO,CAAC,EAAE,KAAK,CAAC,MAAM,UAAU,CAAC,UAAU,MAAM,EAAE,KAAK,GAAG,IAAI,CAAC;EAAA;AAG3D,SAAA;AACT;AAsCO,SAAS,wBAA2B,WAAgC;AACrE,MAAA;AACA,MAAA;AAEJ,QAAM,oBAAoB,IAAI,QAAW,CAAC,SAAS,WAAW;AACvC,yBAAA;AACD,wBAAA;EAAA,CACrB;AAED,oBAAkB,SAAS;AAET,oBAAA,UAAU,CAAC,UAAa;AACxC,sBAAkB,SAAS;AAC3B,sBAAkB,QAAQ;AAC1B,uBAAmB,KAAK;AACxB,iBAAA,OAAA,SAAA,UAAY,KAAA;EACd;AAEkB,oBAAA,SAAS,CAAC,MAAM;AAChC,sBAAkB,SAAS;AAC3B,sBAAkB,CAAC;EACrB;AAEO,SAAA;AACT;AAMO,SAAS,WAAW,YAAoB;AACtC,SAAA,WACJ,QAAQ,OAAO,MAAM,EACrB,QAAQ,MAAM,KAAK,EACnB,QAAQ,MAAM,KAAK;AACxB;AAEgB,SAAA,QAAW,MAAS,MAAS;AAC3C,MAAI,OAAO,GAAG,MAAM,IAAI,GAAG;AAClB,WAAA;EAAA;AAIP,MAAA,OAAO,SAAS,YAChB,SAAS,QACT,OAAO,SAAS,YAChB,SAAS,MACT;AACO,WAAA;EAAA;AAGH,QAAA,QAAQ,OAAO,KAAK,IAAI;AAC9B,MAAI,MAAM,WAAW,OAAO,KAAK,IAAI,EAAE,QAAQ;AACtC,WAAA;EAAA;AAGT,aAAW,QAAQ,OAAO;AACxB,QACE,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,KAChD,CAAC,OAAO,GAAG,KAAK,IAAe,GAAG,KAAK,IAAe,CAAC,GACvD;AACO,aAAA;IAAA;EACT;AAEK,SAAA;AACT;AAaO,SAAS,mBAAmB,aAA8B;AAE/D,QAAM,UAAU;AACT,SAAA,QAAQ,KAAK,WAAW;AACjC;;;ACvcO,SAAS,UAAU,OAAkC;AACnD,SAAA;IACL,MACG,OAAO,CAAC,QAAQ;AACf,aAAO,QAAQ;IAAA,CAChB,EACA,KAAK,GAAG;EACb;AACF;AAEO,SAAS,UAAU,MAAc;AAE/B,SAAA,KAAK,QAAQ,WAAW,GAAG;AACpC;AAEO,SAAS,aAAa,MAAc;AACzC,SAAO,SAAS,MAAM,OAAO,KAAK,QAAQ,WAAW,EAAE;AACzD;AAEO,SAAS,cAAc,MAAc;AAC1C,SAAO,SAAS,MAAM,OAAO,KAAK,QAAQ,WAAW,EAAE;AACzD;AAEO,SAAS,SAAS,MAAc;AAC9B,SAAA,cAAc,aAAa,IAAI,CAAC;AACzC;AAEgB,SAAA,oBAAoB,OAAe,UAA0B;AACvE,OAAA,SAAA,OAAA,SAAA,MAAO,SAAS,GAAA,MAAQ,UAAU,OAAO,UAAU,GAAG,QAAQ,KAAK;AAC9D,WAAA,MAAM,MAAM,GAAG,EAAE;EAAA;AAEnB,SAAA;AACT;AAMgB,SAAA,cACd,WACA,WACA,UACS;AACT,SACE,oBAAoB,WAAW,QAAQ,MACvC,oBAAoB,WAAW,QAAQ;AAE3C;AAoCO,SAAS,YAAY;EAC1B;EACA;EACA;EACA,gBAAgB;EAChB;AACF,GAAuB;;AACd,SAAA,eAAe,UAAU,MAAM,aAAa;AAC9C,OAAA,eAAe,UAAU,IAAI,aAAa;AAE3C,MAAA,eAAe,cAAc,IAAI;AAC/B,QAAA,aAAa,cAAc,EAAE;AAEnC,MAAI,aAAa,SAAS,OAAK,KAAA,KAAK,YAAY,MAAjB,OAAA,SAAA,GAAoB,WAAU,KAAK;AAChE,iBAAa,IAAI;EAAA;AAGR,aAAA,QAAQ,CAAC,WAAW,UAAU;AACnC,QAAA,UAAU,UAAU,KAAK;AAC3B,UAAI,CAAC,OAAO;AAEV,uBAAe,CAAC,SAAS;MAChB,WAAA,UAAU,WAAW,SAAS,GAAG;AAE1C,qBAAa,KAAK,SAAS;MAAA,MACtB;IAEP,WACS,UAAU,UAAU,MAAM;AACnC,mBAAa,IAAI;IACnB,WAAW,UAAU,UAAU,IAAK;SAE7B;AACL,mBAAa,KAAK,SAAS;IAAA;EAC7B,CACD;AAEG,MAAA,aAAa,SAAS,GAAG;AAC3B,UAAI,KAAA,KAAK,YAAY,MAAjB,OAAA,SAAA,GAAoB,WAAU,KAAK;AACrC,UAAI,kBAAkB,SAAS;AAC7B,qBAAa,IAAI;MAAA;IACnB,WACS,kBAAkB,UAAU;AACrC,mBAAa,KAAK,EAAE,MAAM,YAAY,OAAO,IAAA,CAAK;IAAA;EACpD;AAGF,QAAM,SAAS,UAAU,CAAC,UAAU,GAAG,aAAa,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACxE,SAAO,UAAU,MAAM;AACzB;AAEO,SAAS,cAAc,UAAmC;AAC/D,MAAI,CAAC,UAAU;AACb,WAAO,CAAC;EAAA;AAGV,aAAW,UAAU,QAAQ;AAE7B,QAAM,WAA2B,CAAC;AAElC,MAAI,SAAS,MAAM,GAAG,CAAC,MAAM,KAAK;AACrB,eAAA,SAAS,UAAU,CAAC;AAC/B,aAAS,KAAK;MACZ,MAAM;MACN,OAAO;IAAA,CACR;EAAA;AAGH,MAAI,CAAC,UAAU;AACN,WAAA;EAAA;AAIT,QAAM,QAAQ,SAAS,MAAM,GAAG,EAAE,OAAO,OAAO;AAEvC,WAAA;IACP,GAAG,MAAM,IAAI,CAAC,SAAkB;AAC1B,UAAA,SAAS,OAAO,SAAS,KAAK;AACzB,eAAA;UACL,MAAM;UACN,OAAO;QACT;MAAA;AAGF,UAAI,KAAK,OAAO,CAAC,MAAM,KAAK;AACnB,eAAA;UACL,MAAM;UACN,OAAO;QACT;MAAA;AAGK,aAAA;QACL,MAAM;QACN,OAAO,KAAK,SAAS,KAAK,IACtB,KACG,MAAM,KAAK,EACX,IAAI,CAAC,YAAY,UAAU,OAAO,CAAC,EACnC,KAAK,KAAK,IACb,UAAU,IAAI;MACpB;IACD,CAAA;EACH;AAEA,MAAI,SAAS,MAAM,EAAE,MAAM,KAAK;AACnB,eAAA,SAAS,UAAU,CAAC;AAC/B,aAAS,KAAK;MACZ,MAAM;MACN,OAAO;IAAA,CACR;EAAA;AAGI,SAAA;AACT;AAgBO,SAAS,gBAAgB;EAC9B;EACA;EACA;EACA;EACA;AACF,GAAkD;AAC1C,QAAA,2BAA2B,cAAc,IAAI;AAEnD,WAAS,YAAY,KAAkB;AAC/B,UAAA,QAAQ,OAAO,GAAG;AAClB,UAAA,gBAAgB,OAAO,UAAU;AAEvC,QAAI,CAAC,KAAK,QAAQ,EAAE,SAAS,GAAG,GAAG;AAE1B,aAAA,gBAAgB,UAAU,KAAK,IAAI;IAAA,OACrC;AACL,aAAO,gBAAgB,gBAAgB,OAAO,aAAa,IAAI;IAAA;EACjE;AAKF,MAAI,kBAAkB;AAEtB,QAAM,aAAsC,CAAC;AAC7C,QAAM,mBAAmB;IACvB,yBAAyB,IAAI,CAAC,YAAY;AACpC,UAAA,QAAQ,SAAS,YAAY;AAC/B,mBAAW,SAAS,OAAO;AACrB,cAAA,QAAQ,YAAY,QAAQ;AAClC,YAAI,eAAuB,QAAA,GAAG,QAAQ,KAAK,GAAG,SAAS,EAAE;AAClD,eAAA;MAAA;AAGL,UAAA,QAAQ,SAAS,SAAS;AAC5B,cAAM,MAAM,QAAQ,MAAM,UAAU,CAAC;AACrC,YAAI,CAAC,mBAAmB,EAAE,OAAO,SAAS;AACtB,4BAAA;QAAA;AAET,mBAAA,GAAG,IAAI,OAAO,GAAG;AAC5B,YAAI,aAAa;AACT,gBAAA,QAAQ,YAAY,QAAQ,KAAK;AACvC,iBAAO,GAAG,QAAQ,KAAK,GAAG,SAAS,EAAE;QAAA;AAEhC,eAAA,YAAY,GAAG,KAAK;MAAA;AAG7B,aAAO,QAAQ;IAChB,CAAA;EACH;AACO,SAAA,EAAE,YAAY,kBAAkB,gBAAgB;AACzD;AAEA,SAAS,gBAAgB,OAAe,eAAqC;AACvE,MAAA,UAAU,mBAAmB,KAAK;AACtC,MAAI,eAAe;AACjB,eAAW,CAAC,aAAa,IAAI,KAAK,eAAe;AACrC,gBAAA,QAAQ,WAAW,aAAa,IAAI;IAAA;EAChD;AAEK,SAAA;AACT;AAEgB,SAAA,cACd,UACA,iBACA,eAC2B;AAC3B,QAAM,aAAa,YAAY,UAAU,iBAAiB,aAAa;AAGnE,MAAA,cAAc,MAAM,CAAC,YAAY;AACnC;EAAA;AAGF,SAAO,cAAc,CAAC;AACxB;AAEO,SAAS,eACd,UACA,UACA,gBAAyB,OACzB;AAEA,QAAM,qBAAqB,gBAAgB,WAAW,SAAS,YAAY;AAC3E,QAAM,qBAAqB,gBAAgB,WAAW,SAAS,YAAY;AAE3E,UAAQ,MAAM;;;IAGZ,KAAK,uBAAuB;AACnB,aAAA;;IAGT,KAAK,uBAAuB;AACnB,aAAA;;;IAIT,KAAK,SAAS,SAAS,SAAS;AACvB,aAAA;;;;;IAMT,KAAK,mBAAmB,mBAAmB,MAAM,MAAM;AAC9C,aAAA;;IAGT,KAAK,mBAAmB,WAAW,kBAAkB;AAC5C,aAAA,SAAS,MAAM,SAAS,MAAM;;IAGvC;AACS,aAAA;EAAA;AAEb;AAEgB,SAAA,YACd,UACA,MACA,eACoC;AAEpC,MAAI,aAAa,OAAO,CAAC,KAAK,WAAW,QAAQ,GAAG;AAC3C,WAAA;EAAA;AAGT,SAAO,eAAe,UAAU,MAAM,cAAc,aAAa;AAEjE,QAAM,KAAK;IACT;IACA,GAAG,cAAc,MAAM,GAAG;IAC1B,cAAc;EAChB;AAGM,QAAA,eAAe,cAAc,IAAI;AACjC,QAAA,gBAAgB,cAAc,EAAE;AAEtC,MAAI,CAAC,KAAK,WAAW,GAAG,GAAG;AACzB,iBAAa,QAAQ;MACnB,MAAM;MACN,OAAO;IAAA,CACR;EAAA;AAGH,MAAI,CAAC,GAAG,WAAW,GAAG,GAAG;AACvB,kBAAc,QAAQ;MACpB,MAAM;MACN,OAAO;IAAA,CACR;EAAA;AAGH,QAAM,SAAiC,CAAC;AAExC,QAAMC,YAAW,MAAM;AAEf,aAAA,IAAI,GACR,IAAI,KAAK,IAAI,aAAa,QAAQ,cAAc,MAAM,GACtD,KACA;AACM,YAAA,cAAc,aAAa,CAAC;AAC5B,YAAA,eAAe,cAAc,CAAC;AAE9B,YAAA,oBAAoB,KAAK,aAAa,SAAS;AAC/C,YAAA,qBAAqB,KAAK,cAAc,SAAS;AAEvD,UAAI,cAAc;AACZ,YAAA,aAAa,SAAS,YAAY;AACpC,gBAAM,SAAS;YACb,UAAU,aAAa,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;UACrD;AAEA,iBAAO,GAAG,IAAI;AACd,iBAAO,QAAQ,IAAI;AACZ,iBAAA;QAAA;AAGL,YAAA,aAAa,SAAS,YAAY;AACpC,cAAI,aAAa,UAAU,OAAO,EAAC,eAAA,OAAA,SAAA,YAAa,QAAO;AAC9C,mBAAA;UAAA;AAGT,cAAI,aAAa;AACf,gBAAI,cAAc,eAAe;AAC3B,kBAAA,aAAa,UAAU,YAAY,OAAO;AACrC,uBAAA;cAAA;YACT,WAEA,aAAa,MAAM,YAAA,MACnB,YAAY,MAAM,YAAA,GAClB;AACO,qBAAA;YAAA;UACT;QACF;AAGF,YAAI,CAAC,aAAa;AACT,iBAAA;QAAA;AAGL,YAAA,aAAa,SAAS,SAAS;AAC7B,cAAA,YAAY,UAAU,KAAK;AACtB,mBAAA;UAAA;AAET,cAAI,YAAY,MAAM,OAAO,CAAC,MAAM,KAAK;AACvC,mBAAO,aAAa,MAAM,UAAU,CAAC,CAAC,IAAI;cACxC,YAAY;YACd;UAAA;QACF;MACF;AAGE,UAAA,CAAC,qBAAqB,oBAAoB;AAC5C,eAAO,IAAI,IAAI,UAAU,aAAa,MAAM,IAAI,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC;AACtE,eAAO,CAAC,CAAC,cAAc,UAAS,gBAAA,OAAA,SAAA,aAAc,WAAU;MAAA;IAC1D;AAGK,WAAA;EAAA,GACN;AAEH,SAAOA,WAAU,SAAS;AAC5B;;;AC1agB,SAAA,SAAS,UAAyB,CAAA,GAAI;AAClD,UAAgB,aAAa;AAC3B,MAAA,QAAQ,MAAa,OAAA;AAClB,SAAA;AACT;AAEO,SAAS,WAAW,KAAgC;AAClD,SAAA,CAAC,EAAC,OAAA,OAAA,SAAA,IAAK;AAChB;;;ACNgB,SAAA,OAAO,KAAU,KAAc;AACvC,QAAA,mBAAmB,OAAO,QAAQ,GAAG,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAM;AACjE,QAAA,MAAM,QAAQ,KAAK,GAAG;AACjB,aAAA,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC;IAAA,OACnC;AACL,aAAO,CAAC,CAAC,KAAK,OAAO,KAAK,CAAC,CAAC;IAAA;EAC9B,CACD;AAEK,QAAA,eAAe,IAAI,gBAAgB,gBAAgB;AAEjD,UAAA,OAAO,MAAM,aAAa,SAAS;AAC7C;AAUA,SAAS,QAAQ,KAAU;AACrB,MAAA,CAAC,IAAY,QAAA;AACX,QAAA,MAAM,mBAAmB,GAAG,IAC9B,mBAAmB,GAAG,IACtB,mBAAmB,mBAAmB,GAAG,CAAC;AAE1C,MAAA,QAAQ,QAAgB,QAAA;AACxB,MAAA,QAAQ,OAAe,QAAA;AACpB,SAAA,CAAC,MAAM,MAAM,KAAK,CAAC,MAAM,OAAO,MAAM,CAAC,MAAM;AACtD;AAWgB,SAAA,OAAO,KAAU,KAAmB;AAClD,QAAM,mBAAmB,MAAM,IAAI,MAAM,IAAI,MAAM,IAAI;AACjD,QAAA,eAAe,IAAI,gBAAgB,gBAAgB;AAEzD,QAAM,UAAU,CAAC,GAAG,aAAa,QAAA,CAAS;AAE1C,SAAO,QAAQ,OAAgC,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM;AAC9D,UAAA,gBAAgB,IAAI,GAAG;AAC7B,QAAI,iBAAiB,MAAM;AACrB,UAAA,GAAG,IAAI,QAAQ,KAAK;IAAA,OACnB;AACL,UAAI,GAAG,IAAI,MAAM,QAAQ,aAAa,IAClC,CAAC,GAAG,eAAe,QAAQ,KAAK,CAAC,IACjC,CAAC,eAAe,QAAQ,KAAK,CAAC;IAAA;AAG7B,WAAA;EACT,GAAG,CAAA,CAAE;AACP;;;AC/Ea,IAAA,qBAAqB,gBAAgB,KAAK,KAAK;AACrD,IAAM,yBAAyB;EACpC,KAAK;EACL,KAAK;AACP;AAEO,SAAS,gBAAgB,QAA8B;AAC5D,SAAO,CAAC,cAAiC;AACvC,QAAI,UAAU,UAAU,GAAG,CAAC,MAAM,KAAK;AACzB,kBAAA,UAAU,UAAU,CAAC;IAAA;AAG7B,UAAA,QAAiC,OAAO,SAAS;AAGvD,eAAW,OAAO,OAAO;AACjB,YAAA,QAAQ,MAAM,GAAG;AACnB,UAAA,OAAO,UAAU,UAAU;AACzB,YAAA;AACI,gBAAA,GAAG,IAAI,OAAO,KAAK;QAAA,SAClB,KAAK;QAAA;MAEd;IACF;AAGK,WAAA;EACT;AACF;AAEgB,SAAA,oBACd,WACA,QACA;AACA,WAAS,eAAe,KAAU;AAChC,QAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AACvC,UAAA;AACF,eAAO,UAAU,GAAG;MAAA,SACb,KAAK;MAAA;IAAA,WAGL,OAAO,QAAQ,YAAY,OAAO,WAAW,YAAY;AAC9D,UAAA;AAGF,eAAO,GAAG;AACV,eAAO,UAAU,GAAG;MAAA,SACb,KAAK;MAAA;IAEd;AAEK,WAAA;EAAA;AAGT,SAAO,CAAC,WAAgC;AAC7B,aAAA,EAAE,GAAG,OAAO;AAErB,WAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,QAAQ;AAC7B,YAAA,MAAM,OAAO,GAAG;AACtB,UAAI,OAAO,QAAQ,eAAe,QAAQ,QAAW;AACnD,eAAO,OAAO,GAAG;MAAA,OACZ;AACE,eAAA,GAAG,IAAI,eAAe,GAAG;MAAA;IAClC,CACD;AAED,UAAM,YAAY,OAAO,MAAgC,EAAE,SAAS;AAE7D,WAAA,YAAY,IAAI,SAAS,KAAK;EACvC;AACF;;;ACzEO,IAAM,cAAc;;;ACmDpB,SAAS,SAOd,MACmD;AACjD,OAAa,aAAa;AAC5B,OAAK,aAAa,KAAK,cAAc,KAAK,QAAQ;AAC7C,OAAA,UAAU,KAAK,WAAW,CAAC;AAC5B,MAAA,CAAC,KAAK,gBAAgB;AACxB,SAAK,iBAAiB;AAClB,QAAA;AACF,UAAI,IAAI,GAAG,KAAK,IAAI,EAAE;AACtB,WAAK,iBAAiB;IAAA,QAChB;IAAA;EAAC;AAGX,MAAI,KAAK,OAAO;AACR,UAAA;EAAA;AAGD,SAAA;AACT;AAEO,SAAS,WAAW,KAA8B;AAChD,SAAA,CAAC,EAAC,OAAA,OAAA,SAAA,IAAK;AAChB;AAEO,SAAS,mBAAmB,KAAmC;AACpE,SAAO,CAAC,EAAC,OAAA,OAAA,SAAA,IAAK,eAAc,IAAI;AAClC;;;AClEa,IAAA,mBAAA,oBAAuB,QAGlC;AACW,IAAA,mBAAA,oBAAuB,QAGlC;AAEK,IAAM,gCAAgC;EAC3C,SAAS,CAAA;AACX;AAEA,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAM,mBAAA,oBAAuB,IAAoB;AAEjD,IAAM,uBAAA,oBAA2B,IAA6B;AAE9D,SAAS,kBAAkB,aAAoC;AAEvD,QAAA,SAAS,MAAM,KAAK,WAAW,EAAE,KAAK,CAAC,GAAG,MAAM;AAEhD,QAAA,aAAa,WAAW,EAAE,QAAQ,KAAK,SAAS,CAAC,EAAU,QAAA;AAE3D,QAAA,aAAa,WAAW,EAAE,QAAQ,KAAK,SAAS,CAAC,EAAU,QAAA;AACxD,WAAA;EAAA,CACR;AAED,aAAW,WAAW,QAAQ;AAC5B,QAAI,8BAA8B,QAAQ,SAAS,OAAO,GAAG;AAC3D;IAAA;AAG4B,kCAAA,QAAQ,KAAK,OAAO;AAClD,YAAQ,UAAU;AAEZ,UAAA,SAAS,iBAAiB,IAAI,OAAO;AAC3C,QAAI,QAAQ;AACV,iBAAW,SAAS,QAAQ;AACpB,cAAA,2BAA2B,iBAAiB,IAAI,KAAK;AAC3D,YAAI,CAAC,yBAA0B;AAC/B,0BAAkB,wBAAwB;MAAA;IAC5C;EACF;AAEJ;AAEA,SAAS,kBAAkB,OAAuB;AAChD,QAAM,UAAU;IAAQ,CAAC,aACvB,SAAS;MACP,SAAS,MAAM;MACf,YAAY,MAAM;IACnB,CAAA;EACH;AACF;AAEA,SAAS,yBAAyB,SAA2B;AAC3D,UAAQ,UAAU;IAAQ,CAAC,aACzB,SAAS;MACP,SAAS,QAAQ;MACjB,YAAY,QAAQ;IACrB,CAAA;EACH;AACF;AAKO,SAAS,QAAQ,OAAuB;AAE7C,MAAI,eAAe,KAAK,CAAC,qBAAqB,IAAI,KAAK,GAAG;AACnC,yBAAA,IAAI,OAAO,MAAM,SAAS;EAAA;AAGjD,mBAAiB,IAAI,KAAK;AAE1B,MAAI,eAAe,EAAG;AACtB,MAAI,aAAc;AAEd,MAAA;AACa,mBAAA;AAER,WAAA,iBAAiB,OAAO,GAAG;AAC1B,YAAA,SAAS,MAAM,KAAK,gBAAgB;AAC1C,uBAAiB,MAAM;AAGvB,iBAAWC,UAAS,QAAQ;AAE1B,cAAM,YAAY,qBAAqB,IAAIA,MAAK,KAAKA,OAAM;AAC3DA,eAAM,YAAY;AAClB,0BAAkBA,MAAK;MAAA;AAIzB,iBAAWA,UAAS,QAAQ;AACpB,cAAA,cAAc,iBAAiB,IAAIA,MAAK;AAC9C,YAAI,CAAC,YAAa;AAEY,sCAAA,QAAQ,KAAKA,MAAK;AAChD,0BAAkB,WAAW;MAAA;AAI/B,iBAAWA,UAAS,QAAQ;AACpB,cAAA,cAAc,iBAAiB,IAAIA,MAAK;AAC9C,YAAI,CAAC,YAAa;AAElB,mBAAW,WAAW,aAAa;AACjC,mCAAyB,OAAO;QAAA;MAClC;IACF;EACF,UAAA;AAEe,mBAAA;AACf,kCAA8B,UAAU,CAAC;AACzC,yBAAqB,MAAM;EAAA;AAE/B;AAEO,SAAS,MAAM,IAAgB;AACpC;AACI,MAAA;AACC,OAAA;EAAA,UAAA;AAEH;AACA,QAAI,iBAAiB,GAAG;AACtB,YAAM,uBAAuB,MAAM,KAAK,gBAAgB,EAAE,CAAC;AAG3D,UAAI,sBAAsB;AACxB,gBAAQ,oBAAoB;MAAA;IAC9B;EACF;AAEJ;;;AChIO,IAAM,QAAN,MAGL;EAMA,YAAY,cAAsB,SAA0C;AAL5E,SAAA,YAAA,oBAAgB,IAAsB;AAWtC,SAAA,YAAY,CAAC,aAA+B;;AACrC,WAAA,UAAU,IAAI,QAAQ;AAC3B,YAAM,SAAQ,MAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,gBAAd,OAAA,SAAA,GAAA,KAAA,IAA4B,UAAU,IAAA;AACpD,aAAO,MAAM;AACN,aAAA,UAAU,OAAO,QAAQ;AACtB,iBAAA,OAAA,SAAA,MAAA;MACV;IACF;AAEA,SAAA,WAAW,CAAC,YAAsB;;AAChC,WAAK,YAAY,KAAK;AACtB,WAAK,UAAQ,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,YACvB,KAAK,QAAQ,SAAS,KAAK,SAAS,EAAE,OAAO,IAC5C,QAAgB,KAAK,SAAS;AAGnC,OAAA,MAAA,KAAA,KAAK,YAAL,OAAA,SAAA,GAAc,aAAd,OAAA,SAAA,GAAA,KAAA,EAAA;AAGA,cAAQ,IAAa;IACvB;AAzBE,SAAK,YAAY;AACjB,SAAK,QAAQ;AACb,SAAK,UAAU;EAAA;AAwBnB;;;ACTO,IAAM,UAAN,MAAM,SAKX;EA4BA,YAAY,SAAuC;AA3BnD,SAAA,YAAA,oBAAgB,IAAsB;AAStC,SAAA,iBAAoC,CAAC;AAErC,SAAA,oBAAoC,CAAC;AACrC,SAAA,aAAa,MAAM;AACjB,YAAM,cAAc,CAAC;AACrB,YAAM,cAAc,CAAC;AACV,iBAAA,OAAO,KAAK,QAAQ,MAAM;AACvB,oBAAA,KAAK,IAAI,SAAS;AAClB,oBAAA,KAAK,IAAI,KAAK;MAAA;AAE5B,WAAK,oBAAoB;AAClB,aAAA;QACL;QACA;QACA,SAAS,KAAK,aAAa;MAC7B;IACF;AA4DA,SAAA,YAAY,MAAM;;AAChB,WAAK,YAAY,KAAK;AACtB,YAAM,EAAE,aAAa,aAAa,QAAQ,IAAI,KAAK,WAAW;AACzD,WAAA,QAAQ,KAAK,QAAQ,GAAG;QAC3B;QACA;QACA;MAAA,CACD;AAED,OAAA,MAAA,KAAA,KAAK,SAAQ,aAAb,OAAA,SAAA,GAAA,KAAA,EAAA;IACF;AAEA,SAAA,mCAAmC,MAAM;AAC5B,iBAAA,OAAO,KAAK,QAAQ,MAAM;AACnC,YAAI,eAAe,UAAS;AAC1B,cAAI,iCAAiC;QAAA;MACvC;AAEF,UAAI,kBAAkB;AACtB,YAAM,oBAAoB,KAAK;AAC/B,YAAM,EAAE,YAAA,IAAgB,KAAK,WAAW;AACxC,eAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,YAAI,YAAY,CAAC,MAAM,kBAAkB,CAAC,GAAG;AACzB,4BAAA;AAClB;QAAA;MACF;AAGF,UAAI,iBAAiB;AACnB,aAAK,UAAU;MAAA;IAEnB;AAEA,SAAA,QAAQ,MAAM;AACZ,WAAK,gBAAgB;AACrB,WAAK,iCAAiC;AAEtC,aAAO,MAAM;AACX,aAAK,oBAAoB;AACd,mBAAA,WAAW,KAAK,gBAAgB;AACjC,kBAAA;QAAA;MAEZ;IACF;AAEA,SAAA,YAAY,CAAC,aAA+B;;AACrC,WAAA,UAAU,IAAI,QAAQ;AAC3B,YAAM,SAAQ,MAAA,KAAA,KAAK,SAAQ,gBAAb,OAAA,SAAA,GAAA,KAAA,IAA2B,UAAU,IAAA;AACnD,aAAO,MAAM;AACN,aAAA,UAAU,OAAO,QAAQ;AACtB,iBAAA,OAAA,SAAA,MAAA;MACV;IACF;AA7GE,SAAK,UAAU;AACV,SAAA,QAAQ,QAAQ,GAAG;MACtB,aAAa;MACb,SAAS;MACT,aAAa,KAAK,WAAA,EAAa;IAAA,CAChC;EAAA;EAGH,gBACE,OAAiD,KAAK,QAAQ,MAC9D;AACA,eAAW,OAAO,MAAM;AACtB,UAAI,eAAe,UAAS;AAE1B,YAAI,gBAAgB;AAEf,aAAA,gBAAgB,IAAI,QAAQ,IAAI;MAAA,WAC5B,eAAe,OAAO;AAE3B,YAAA,2BAA2B,iBAAiB,IAAI,GAAG;AACvD,YAAI,CAAC,0BAA0B;AAC7B,qCAAA,oBAA+B,IAAI;AAClB,2BAAA,IAAI,KAAK,wBAAwB;QAAA;AAEpD,iCAAyB,IAAI,IAAa;AAGtC,YAAA,gBAAgB,iBAAiB,IAAI,IAAa;AACtD,YAAI,CAAC,eAAe;AAClB,0BAAA,oBAAoB,IAAI;AACP,2BAAA,IAAI,MAAe,aAAa;QAAA;AAEnD,sBAAc,IAAI,GAAG;MAAA;IACvB;EACF;EAGF,oBACE,OAAiD,KAAK,QAAQ,MAC9D;AACA,eAAW,OAAO,MAAM;AACtB,UAAI,eAAe,UAAS;AACrB,aAAA,oBAAoB,IAAI,QAAQ,IAAI;MAAA,WAChC,eAAe,OAAO;AACzB,cAAA,2BAA2B,iBAAiB,IAAI,GAAG;AACzD,YAAI,0BAA0B;AAC5B,mCAAyB,OAAO,IAAa;QAAA;AAGzC,cAAA,gBAAgB,iBAAiB,IAAI,IAAa;AACxD,YAAI,eAAe;AACjB,wBAAc,OAAO,GAAG;QAAA;MAC1B;IACF;EACF;AAwDJ;;;ACjLA,SAAS,wBAAwB;AAC3B,MAAA;AACF,QACE,OAAO,WAAW,eAClB,OAAO,OAAO,mBAAmB,UACjC;AACA,aAAO,OAAO;IAAA;EAChB,QACM;AACC,WAAA;EAAA;AAEF,SAAA;AACT;AAEO,IAAM,aAAa;AAE1B,IAAM,WAAW,CAAC,IAAmC,SAAiB;AAChE,MAAA;AACJ,SAAO,IAAI,SAAqB;AAC9B,QAAI,CAAC,SAAS;AACZ,gBAAU,WAAW,MAAM;AACzB,WAAG,GAAG,IAAI;AACA,kBAAA;MAAA,GACT,IAAI;IAAA;EAEX;AACF;AAEA,SAAS,+BAAmE;AAC1E,QAAM,qBAAqB,sBAAsB;AACjD,MAAI,CAAC,oBAAoB;AAChB,WAAA;EAAA;AAGH,QAAA,iBAAiB,mBAAmB,QAAQ,UAAU;AAC5D,MAAI,QAAgC,iBAChC,KAAK,MAAM,cAAc,IACzB,CAAC;AAEE,SAAA;IACL;;;;IAIA,KAAK,CAAC,aACH,QAAQ,iBAAiB,SAAS,KAAK,KAAK,OAC7C,mBAAmB,QAAQ,YAAY,KAAK,UAAU,KAAK,CAAC;EAEhE;AACF;AAEO,IAAM,yBAAyB,6BAA6B;AAStD,IAAA,iCAAiC,CAAC,aAA6B;AACnE,SAAA,SAAS,MAAM,OAAQ,SAAS;AACzC;AAEO,SAAS,eAAe,IAAiB;AAC9C,QAAM,OAAO,CAAC;AACV,MAAA;AACI,SAAA,SAAS,GAAG,YAAa;AAC1B,SAAA;MACH,GAAG,GAAG,OAAO,cAAe,CAAA,EAAG,QAAgB,KAAK,OAAO,UAAU,EAAE,IAAI,CAAC;IAC9E;AACK,SAAA;EAAA;AAEP,SAAO,GAAG,KAAK,KAAK,KAAK,CAAC,GAAG,YAAY;AAC3C;AAEA,IAAI,eAAe;AAMZ,SAAS,cACdC,aACA,KACA,UACA,yBACA,sBACA;;AACI,MAAA;AAEA,MAAA;AACF,YAAQ,KAAK,MAAM,eAAe,QAAQA,WAAU,KAAK,IAAI;EAAA,SACtD,OAAY;AACnB,YAAQ,MAAM,KAAK;AACnB;EAAA;AAGF,QAAM,cAAc,SAAO,KAAA,OAAO,QAAQ,UAAf,OAAA,SAAA,GAAsB;AAC3C,QAAA,iBAAiB,MAAM,WAAW;AAGzB,iBAAA;AAGd,GAAC,MAAM;AAGN,QAAI,2BAA2B,gBAAgB;AAC7C,iBAAW,mBAAmB,gBAAgB;AACtC,cAAA,QAAQ,eAAe,eAAe;AAC5C,YAAI,oBAAoB,UAAU;AAChC,iBAAO,SAAS;YACd,KAAK,MAAM;YACX,MAAM,MAAM;YACZ;UAAA,CACD;QAAA,WACQ,iBAAiB;AACpB,gBAAA,UAAU,SAAS,cAAc,eAAe;AACtD,cAAI,SAAS;AACX,oBAAQ,aAAa,MAAM;AAC3B,oBAAQ,YAAY,MAAM;UAAA;QAC5B;MACF;AAGF;IAAA;AAOF,UAAM,OAAO,OAAO,SAAS,KAAK,MAAM,GAAG,EAAE,CAAC;AAE9C,QAAI,MAAM;AACR,YAAM,6BACH,OAAO,QAAQ,SAAS,CAAA,GAAI,+BAA+B;AAE9D,UAAI,2BAA2B;AACvB,cAAA,KAAK,SAAS,eAAe,IAAI;AACvC,YAAI,IAAI;AACN,aAAG,eAAe,yBAAyB;QAAA;MAC7C;AAGF;IAAA;AAKD;MACC;MACA,IAAI,wBAAA,OAAA,SAAA,qBAAsB,OAAO,CAAC,MAAM,MAAM,QAAA,MAAa,CAAA;IAAC,EAC5D,QAAQ,CAAC,aAAa;AACtB,YAAM,UACJ,aAAa,WAAW,SAAS,SAAS,cAAc,QAAQ;AAClE,UAAI,SAAS;AACX,gBAAQ,SAAS;UACf,KAAK;UACL,MAAM;UACN;QAAA,CACD;MAAA;IACH,CACD;EAAA,GACA;AAGY,iBAAA;AACjB;AAEgB,SAAA,uBAAuB,QAAmB,OAAiB;AACzE,MAAI,2BAA2B,QAAW;AACxC;EAAA;AAEF,QAAM,0BACJ,SAAS,OAAO,QAAQ,qBAAqB;AAE/C,MAAI,yBAAyB;AAC3B,WAAO,oBAAoB;EAAA;AAG7B,MAAI,OAAO,aAAa,eAAe,OAAO,0BAA0B;AACtE;EAAA;AAGF,SAAO,2BAA2B;AAGnB,iBAAA;AAET,QAAA,SACJ,OAAO,QAAQ,2BAA2B;AAE5C,SAAO,QAAQ,oBAAoB;AAuC7B,QAAA,WAAW,CAAC,UAAiB;AAG7B,QAAA,gBAAgB,CAAC,OAAO,mBAAmB;AAC7C;IAAA;AAGF,QAAI,kBAAkB;AAEtB,QAAI,MAAM,WAAW,YAAY,MAAM,WAAW,QAAQ;AACtC,wBAAA;IAAA,OACb;AACC,YAAA,SAAU,MAAM,OAAmB;QACvC;MACF;AAEA,UAAI,QAAQ;AACV,0BAAkB,gCAAgC,MAAM;MAAA,OACnD;AACa,0BAAA,eAAe,MAAM,MAAM;MAAA;IAC/C;AAGF,UAAM,aAAa,OAAO,OAAO,MAAM,QAAQ;AAExB,2BAAA,IAAI,CAAC,UAAU;AACpC,YAAM,WAAY,MAAM,UAAU,IAChC,MAAM,UAAU,KAAM,CAAC;AAEzB,YAAM,eAAgB,SAAS,eAAe,IAC5C,SAAS,eAAe,KAAM,CAAC;AAEjC,UAAI,oBAAoB,UAAU;AACnB,qBAAA,UAAU,OAAO,WAAW;AAC5B,qBAAA,UAAU,OAAO,WAAW;MAAA,WAChC,iBAAiB;AACpB,cAAA,UAAU,SAAS,cAAc,eAAe;AACtD,YAAI,SAAS;AACE,uBAAA,UAAU,QAAQ,cAAc;AAChC,uBAAA,UAAU,QAAQ,aAAa;QAAA;MAC9C;AAGK,aAAA;IAAA,CACR;EACH;AAGI,MAAA,OAAO,aAAa,aAAa;AACnC,aAAS,iBAAiB,UAAU,SAAS,UAAU,GAAG,GAAG,IAAI;EAAA;AAG5D,SAAA,UAAU,cAAc,CAAC,UAAU;AAGlC,UAAA,WAAW,OAAO,MAAM,UAAU;AAIpC,QAAA,CAAC,OAAO,iBAAiB;AAC3B,aAAO,kBAAkB;AACzB;IAAA;AAGF;MACE;MACA;MACA,OAAO,QAAQ,6BAA6B;MAC5C,OAAO,qBAAqB;MAC5B,OAAO,QAAQ,wBAAwB;IACzC;AAEA,QAAI,OAAO,mBAAmB;AAEL,6BAAA,IAAI,CAAC,UAAU;AACpC,cAAM,QAAQ,IAAI,MAAM,QAAQ,KAAM,CAAC;AAEhC,eAAA;MAAA,CACR;IAAA;EACH,CACD;AACH;AAUO,SAAS,iBAAiB,QAAmB;AAClD,MAAI,OAAO,aAAa,eAAgB,SAAiB,eAAe;AACtE,UAAM,4BACJ,OAAO,MAAM,SAAS,MAAM,+BAA+B;AAE7D,QAAI,6BAA6B,OAAO,MAAM,SAAS,SAAS,IAAI;AAClE,YAAM,KAAK,SAAS,eAAe,OAAO,MAAM,SAAS,IAAI;AAC7D,UAAI,IAAI;AACN,WAAG,eAAe,yBAAyB;MAAA;IAC7C;EACF;AAEJ;;;ACuUO,SAAS,sBAAsB,KAAc;AAClD,MAAI,eAAe,OAAO;AACxB,UAAM,MAAM;MACV,MAAM,IAAI;MACV,SAAS,IAAI;IACf;AAEI,QAAA,MAAwC;AACxC,UAAY,QAAQ,IAAI;IAAA;AAGrB,WAAA;EAAA;AAGF,SAAA;IACL,MAAM;EACR;AACF;AA2BO,SAAS,sBAAsB,aAGnC;AACD,QAAM,eAAe,YAAY;AACjC,QAAM,aAAa,YAAY;AACzB,QAAA,eAAc,gBAAA,OAAA,SAAA,aAAc,cAAa,WAAW;AACpD,QAAA,eAAc,gBAAA,OAAA,SAAA,aAAc,UAAS,WAAW;AAChD,QAAA,eAAc,gBAAA,OAAA,SAAA,aAAc,UAAS,WAAW;AACtD,SAAO,EAAE,cAAc,YAAY,aAAa,aAAa,YAAY;AAC3E;AA0BO,IAAM,aAAN,MAML;;;;EAsCA,YACE,SAOA;AA5CF,SAAA,kBAAsC,GAAG,KAAK;MAC5C,KAAK,OAAA,IAAW;IAAA,CACjB;AACiB,SAAA,kBAAA;AACuC,SAAA,uBAAA;AACd,SAAA,iCAAA;AAC3C,SAAA,cAAA,oBAAkB,IAAiC;AAE/B,SAAA,oBAAA;AACO,SAAA,2BAAA;AAwDU,SAAA,kBAAA,CAAC,OAAO,GAAG;AAEhD,SAAA,SAMI,CAAC,eAAe;;AAClB,UAAI,WAAW,eAAe;AACpB,gBAAA;UACN;QACF;MAAA;AAGF,YAAM,kBAAkB,KAAK;AAC7B,WAAK,UAAU;QACb,GAAG,KAAK;QACR,GAAG;MACL;AAEA,WAAK,WAAW,KAAK,QAAQ,YAAY,OAAO,aAAa;AAE7D,WAAK,0BAA0B,KAAK,QAAQ,8BACxC,IAAI;QACF,KAAK,QAAQ,4BAA4B,IAAI,CAAC,SAAS;UACrD,mBAAmB,IAAI;UACvB;QACD,CAAA;MAAA,IAEH;AAGF,UAAA,CAAC,KAAK,YACL,WAAW,YAAY,WAAW,aAAa,gBAAgB,UAChE;AAEE,YAAA,WAAW,aAAa,UACxB,WAAW,aAAa,MACxB,WAAW,aAAa,KACxB;AACA,eAAK,WAAW;QAAA,OACX;AACL,eAAK,WAAW,IAAI,SAAS,WAAW,QAAQ,CAAC;QAAA;MACnD;AAGF;;QAEE,CAAC,KAAK,WACL,KAAK,QAAQ,WAAW,KAAK,QAAQ,YAAY,KAAK;QACvD;AACA,aAAK,UACH,KAAK,QAAQ,YACX,KAAK,WACH,oBAAoB;UAClB,gBAAgB,CAAC,KAAK,YAAY,GAAG;QAAA,CACtC,IACD,qBAAqB;AACtB,aAAA,iBAAiB,KAAK,cAAc;MAAA;AAG3C,UAAI,KAAK,QAAQ,cAAc,KAAK,WAAW;AACxC,aAAA,YAAY,KAAK,QAAQ;AAC9B,aAAK,eAAe;MAAA;AAIlB,UAAA,CAAC,KAAK,SAAS;AACjB,aAAK,UAAU,IAAI,MAAM,sBAAsB,KAAK,cAAc,GAAG;UACnE,UAAU,MAAM;AACd,iBAAK,QAAQ,QAAQ;cACnB,GAAG,KAAK;cACR,eAAe,KAAK,MAAM,cAAc;gBACtC,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,SAAS,EAAE,MAAM;cAAA;YAE5C;UAAA;QACF,CACD;AAED,+BAAuB,IAAI;MAAA;AAI3B,UAAA,OAAO,WAAW,eAClB,SAAS;MAET,SAAO,KAAA,OAAO,QAAP,OAAA,SAAA,GAAY,cAAa,YAChC;AACK,aAAA,iCAAiC,OAAO,IAAI;UAC/C;QACF;MAAA;IAEJ;AAMA,SAAA,iBAAiB,MAAM;AACrB,WAAK,aAAa,CAAC;AACnB,WAAK,eAAe,CAAC;AAEf,YAAA,gBAAgB,KAAK,QAAQ;AACnC,UAAI,eAAe;AACjB,sBAAc,KAAK;UACjB,eAAe;UACf,YAAY,KAAK,QAAQ;QAAA,CAC1B;AACC,aAAK,WAAmB,cAAc,EAAE,IAAI;MAAA;AAG1C,YAAA,gBAAgB,CAAC,gBAAiC;AAC1C,oBAAA,QAAQ,CAAC,YAAY,MAAM;AACrC,qBAAW,KAAK;YACd,eAAe;YACf,YAAY,KAAK,QAAQ;UAAA,CAC1B;AAED,gBAAM,gBAAiB,KAAK,WAAmB,WAAW,EAAE;AAE5D;YACE,CAAC;YACD,mCAAmC,OAAO,WAAW,EAAE,CAAC;UAC1D;AACE,eAAK,WAAmB,WAAW,EAAE,IAAI;AAE3C,cAAI,CAAC,WAAW,UAAU,WAAW,MAAM;AACnC,kBAAA,kBAAkB,cAAc,WAAW,QAAQ;AAEvD,gBAAA,CAAE,KAAK,aAAqB,eAAe,KAC3C,WAAW,SAAS,SAAS,GAAG,GAChC;AACE,mBAAK,aAAqB,eAAe,IAAI;YAAA;UACjD;AAGF,gBAAM,WAAW,WAAW;AAE5B,cAAI,YAAA,OAAA,SAAA,SAAU,QAAQ;AACpB,0BAAc,QAAQ;UAAA;QACxB,CACD;MACH;AAEc,oBAAA,CAAC,KAAK,SAAS,CAAC;AAE9B,YAAM,eAMD,CAAC;AAEN,YAAM,SAA0B,OAAO,OAAO,KAAK,UAAU;AAEtD,aAAA,QAAQ,CAAC,GAAG,MAAM;;AACvB,YAAI,EAAE,UAAU,CAAC,EAAE,MAAM;AACvB;QAAA;AAGI,cAAA,UAAU,aAAa,EAAE,QAAQ;AACjC,cAAA,SAAS,cAAc,OAAO;AAEpC,eAAO,OAAO,SAAS,OAAK,KAAA,OAAO,CAAC,MAAR,OAAA,SAAA,GAAW,WAAU,KAAK;AACpD,iBAAO,MAAM;QAAA;AAGf,cAAM,SAAS,OAAO,IAAI,CAAC,YAAY;AACjC,cAAA,QAAQ,UAAU,KAAK;AAClB,mBAAA;UAAA;AAGL,cAAA,QAAQ,SAAS,SAAS;AACrB,mBAAA;UAAA;AAGL,cAAA,QAAQ,SAAS,YAAY;AACxB,mBAAA;UAAA;AAGF,iBAAA;QAAA,CACR;AAEY,qBAAA,KAAK,EAAE,OAAO,GAAG,SAAS,QAAQ,OAAO,GAAG,OAAA,CAAQ;MAAA,CAClE;AAED,WAAK,aAAa,aACf,KAAK,CAAC,GAAG,MAAM;AACR,cAAA,YAAY,KAAK,IAAI,EAAE,OAAO,QAAQ,EAAE,OAAO,MAAM;AAG3D,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,cAAI,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG;AAC/B,mBAAO,EAAE,OAAO,CAAC,IAAK,EAAE,OAAO,CAAC;UAAA;QAClC;AAIF,YAAI,EAAE,OAAO,WAAW,EAAE,OAAO,QAAQ;AACvC,iBAAO,EAAE,OAAO,SAAS,EAAE,OAAO;QAAA;AAIpC,iBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAC9B,cAAA,EAAE,OAAO,CAAC,EAAG,UAAU,EAAE,OAAO,CAAC,EAAG,OAAO;AACtC,mBAAA,EAAE,OAAO,CAAC,EAAG,QAAQ,EAAE,OAAO,CAAC,EAAG,QAAQ,IAAI;UAAA;QACvD;AAIK,eAAA,EAAE,QAAQ,EAAE;MACpB,CAAA,EACA,IAAI,CAAC,GAAG,MAAM;AACb,UAAE,MAAM,OAAO;AACf,eAAO,EAAE;MAAA,CACV;IACL;AAEyB,SAAA,YAAA,CAAC,WAAW,OAAO;AAC1C,YAAM,WAAgC;QACpC;QACA;MACF;AAEK,WAAA,YAAY,IAAI,QAAQ;AAE7B,aAAO,MAAM;AACN,aAAA,YAAY,OAAO,QAAQ;MAClC;IACF;AAEA,SAAA,OAAe,CAAC,gBAAgB;AACzB,WAAA,YAAY,QAAQ,CAAC,aAAa;AACjC,YAAA,SAAS,cAAc,YAAY,MAAM;AAC3C,mBAAS,GAAG,WAAW;QAAA;MACzB,CACD;IACH;AAE6C,SAAA,gBAAA,CAC3C,kBACA,oBACG;AACH,YAAM,QAAQ,CAAC;QACb;QACA;QACA;QACA;MAAA,MACmE;AACnE,cAAM,eAAe,KAAK,QAAQ,YAAY,MAAM;AACpD,cAAM,YAAY,KAAK,QAAQ,gBAAgB,YAAY;AAEpD,eAAA;UACL;UACA;UACA,QAAQ,iBAAiB,oBAAA,OAAA,SAAA,iBAAkB,QAAQ,YAAY;UAC/D,MAAM,KAAK,MAAM,GAAG,EAAE,QAAQ,EAAE,CAAC,KAAK;UACtC,MAAM,GAAG,QAAQ,GAAG,SAAS,GAAG,IAAI;UACpC,OAAO,iBAAiB,oBAAA,OAAA,SAAA,iBAAkB,OAAO,KAAK;QACxD;MACF;AAEA,YAAM,WAAW,MAAM,mBAAmB,KAAK,QAAQ,QAAQ;AAE/D,YAAM,EAAE,gBAAgB,UAAU,IAAI,SAAS;AAE/C,UAAI,mBAAmB,CAAC,aAAa,cAAc,KAAK,kBAAkB;AAElE,cAAA,qBAAqB,MAAM,cAAc;AAC5B,2BAAA,MAAM,MAAM,SAAS,MAAM;AAE9C,eAAO,mBAAmB,MAAM;AAEzB,eAAA;UACL,GAAG;UACH,gBAAgB;QAClB;MAAA;AAGK,aAAA;IACT;AAEsB,SAAA,sBAAA,CAAC,MAAc,SAAiB;AACpD,YAAM,eAAe,YAAY;QAC/B,UAAU,KAAK;QACf,MAAM;QACN,IAAI,UAAU,IAAI;QAClB,eAAe,KAAK,QAAQ;QAC5B,eAAe,KAAK,QAAQ;MAAA,CAC7B;AACM,aAAA;IACT;AAe6B,SAAA,cAAA,CAC3B,gBACA,sBACA,SACG;AACC,UAAA,OAAO,mBAAmB,UAAU;AACtC,eAAO,KAAK;UACV;YACE,UAAU;YACV,QAAQ;UACV;UACA;QACF;MAAA,OACK;AACE,eAAA,KAAK,oBAAoB,gBAAgB,oBAAoB;MAAA;IAExE;AAsSqC,SAAA,mBAAA,CAAC,MAAM,SAAS;AACnD,UAAI,cAAsC,CAAC;AACrC,YAAA,cAAc,cAAc,KAAK,QAAQ;AACzC,YAAA,mBAAmB,CAAC,UAAoB;AAC5C,cAAM,SAAS,cAAc,KAAK,UAAU,aAAa;UACvD,IAAI,MAAM;UACV,eACE,MAAM,QAAQ,iBAAiB,KAAK,QAAQ;UAC9C,OAAO;QAAA,CACR;AACM,eAAA;MACT;AAEI,UAAA,cACF,QAAA,OAAA,SAAA,KAAM,QAAO,SAAY,KAAK,aAAa,KAAK,EAAG,IAAI;AACzD,UAAI,YAAY;AACd,sBAAc,iBAAiB,UAAU;MAAA,OACpC;AACL,qBAAa,KAAK,WAAW,KAAK,CAAC,UAAU;AACrC,gBAAA,gBAAgB,iBAAiB,KAAK;AAE5C,cAAI,eAAe;AACH,0BAAA;AACP,mBAAA;UAAA;AAGF,iBAAA;QAAA,CACR;MAAA;AAGH,UAAI,cACF,cAAe,KAAK,WAAmB,WAAW;AAE9C,YAAA,gBAAiC,CAAC,WAAW;AAEnD,aAAO,YAAY,aAAa;AAC9B,sBAAc,YAAY;AAC1B,sBAAc,QAAQ,WAAW;MAAA;AAG5B,aAAA,EAAE,eAAe,aAAa,WAAW;IAClD;AAEA,SAAA,cAAc,CAAC,OAAe;AACtB,YAAA,QAAQ,KAAK,SAAS,EAAE;AAE9B,UAAI,CAAC,MAAO;AAEZ,YAAM,gBAAgB,MAAM;AAC5B,mBAAa,MAAM,cAAc;IACnC;AAEA,SAAA,gBAAgB,MAAM;;AACpB,OAAA,KAAA,KAAK,MAAM,mBAAX,OAAA,SAAA,GAA2B,QAAQ,CAAC,UAAU;AACvC,aAAA,YAAY,MAAM,EAAE;MAAA,CAAA;IAE7B;AAEA,SAAA,gBAAiC,CAAC,SAAS;AACzC,YAAM,QAAQ,CACZ,OAEI,CAAA,GACJ,wBACmB;;AACnB,cAAM,cAAc,KAAK,gBACrB,KAAK,YAAY,KAAK,eAAe,EAAE,gBAAgB,KAAA,CAAM,IAC7D,KAAK,MAAM;AAEf,cAAM,YACJ,KAAK,QAAQ,OACT,YAAY;UAAK,CAAC,MAChB,cAAc,KAAK,UAAU,cAAc,EAAE,QAAQ,GAAG;YACtD,IAAI,KAAK;YACT,eAAe;YACf,OAAO;UACR,CAAA;QAAA,IAEH;AAEN,cAAM,YAAW,aAAA,OAAA,SAAA,UAAW,aAAY,KAAK,eAAe;AAE5D;UACE,KAAK,QAAQ,QAAQ,aAAa;UAClC,oCAAoC,KAAK;QAC3C;AAEA,cAAM,eAAa,KAAA,KAAK,MAAM,mBAAX,OAAA,SAAA,GAA2B,WAC1C,KAAA,KAAK,KAAK,MAAM,cAAc,MAA9B,OAAA,SAAA,GAAiC,WACjC,KAAA,KAAK,WAAW,MAAhB,OAAA,SAAA,GAAmB,WAAU,KAAK,eAAe;AAE/C,cAAA,iBAAiB,uBAAA,OAAA,SAAA,oBAAqB,cAAc;UAAO,CAAC,MAChE,YAAY,KAAK,CAAC,MAAM,EAAE,YAAY,EAAE,EAAE;QAAA;AAExC,YAAA;AACJ,YAAI,KAAK,IAAI;AACL,gBAAA,iBACJ,aAAA,OAAA,SAAA,UAAW,eACX,KAAA,KAAK,WAAW,MAAhB,OAAA,SAAA,GAAmB,aACnB,KAAK,eAAe;AACtB,qBAAW,KAAK,oBAAoB,eAAe,GAAG,KAAK,EAAE,EAAE;QAAA,OAC1D;AACL,gBAAM,6BACJ,KAAK,YACH,KAAA,kBAAA,OAAA,SAAA,eAAgB,KAAK,CAAC,UAAU;AAC9B,kBAAM,mBAAmB,gBAAgB;cACvC,MAAM,MAAM;cACZ,SAAQ,uBAAA,OAAA,SAAA,oBAAqB,gBAAe,CAAC;cAC7C,eAAe,KAAK;YACrB,CAAA,EAAE;AACH,kBAAMC,YAAW,UAAU,CAAC,KAAK,UAAU,gBAAgB,CAAC;AAC5D,mBAAOA,cAAa;UACrB,CAAA,MARD,OAAA,SAAA,GAQI,EACN;AACF,qBAAW,KAAK;YACd;aACA,8BAAA,OAAA,SAAA,2BAA4B,OAAM;UACpC;QAAA;AAGF,cAAM,aAAa,EAAE,IAAG,KAAA,KAAK,WAAW,MAAhB,OAAA,SAAA,GAAmB,OAAO;AAElD,YAAI,cACD,KAAK,UAAU,UAAU,OACtB,aACA;UACE,GAAG;UACH,GAAG,iBAAiB,KAAK,QAAe,UAAU;QACpD;AAEN,YAAI,OAAO,KAAK,UAAU,EAAE,SAAS,GAAG;AACjB,iCAAA,OAAA,SAAA,oBAAA,cAClB,IAAI,CAAC,UAAU;;AACd,qBACEC,MAAA,MAAM,QAAQ,WAAd,OAAA,SAAAA,IAAsB,cAAa,MAAM,QAAQ;UAEpD,CAAA,EACA,OAAO,OAAA,EACP,QAAQ,CAAC,OAAO;AACf,yBAAa,EAAE,GAAG,YAAa,GAAG,GAAI,UAAU,EAAE;UAAA,CAAA;QACnD;AAGL,mBAAW,gBAAgB;UACzB,MAAM;UACN,QAAQ,cAAc,CAAC;UACvB,gBAAgB;UAChB,aAAa,KAAK;UAClB,eAAe,KAAK;QACrB,CAAA,EAAE;AAEH,YAAI,SAAS;AACb,YAAI,KAAK,4BAA0B,KAAA,KAAK,QAAQ,WAAb,OAAA,SAAA,GAAqB,SAAQ;AAC9D,cAAI,kBAAkB,CAAC;AACF,iCAAA,OAAA,SAAA,oBAAA,cAAc,QAAQ,CAAC,UAAU;AAChD,gBAAA;AACE,kBAAA,MAAM,QAAQ,gBAAgB;AACd,kCAAA;kBAChB,GAAG;kBACH,GAAI,eAAe,MAAM,QAAQ,gBAAgB;oBAC/C,GAAG;oBACH,GAAG;kBAAA,CACJ,KAAK,CAAA;gBACR;cAAA;YACF,QACM;YAAA;UAER,CAAA;AAEO,mBAAA;QAAA;AAGL,cAAA,mBAAmB,CAACC,YAAgB;AAClC,gBAAA,kBACJ,uBAAA,OAAA,SAAA,oBAAqB,cAAc;YACjC,CAAC,KAAK,UAAU;;AACd,oBAAM,cAA4C,CAAC;AAC/C,kBAAA,YAAY,MAAM,SAAS;AACzB,qBAAAD,MAAA,MAAM,QAAQ,WAAd,OAAA,SAAAA,IAAsB,aAAa;AACrC,8BAAY,KAAK,GAAG,MAAM,QAAQ,OAAO,WAAW;gBAAA;cACtD,WAIA,MAAM,QAAQ,oBACd,MAAM,QAAQ,mBACd;AACA,sBAAM,mBAA0C,CAAC;kBAC/C,QAAAC;kBACA;gBAAA,MACI;AACJ,sBAAI,aAAaA;AACjB,sBACE,sBAAsB,MAAM,WAC5B,MAAM,QAAQ,kBACd;AACa,iCAAA,MAAM,QAAQ,iBAAiB;sBAC1C,CAAC,MAAMC,UAASA,MAAK,IAAI;sBACzBD;oBACF;kBAAA;AAEI,wBAAA,SAAS,KAAK,UAAU;AAC9B,sBACE,uBAAuB,MAAM,WAC7B,MAAM,QAAQ,mBACd;AACO,2BAAA,MAAM,QAAQ,kBAAkB;sBACrC,CAAC,MAAMC,UAASA,MAAK,IAAI;sBACzB;oBACF;kBAAA;AAEK,yBAAA;gBACT;AACA,4BAAY,KAAK,gBAAgB;cAAA;AAEnC,kBAAI,KAAK,0BAA0B,MAAM,QAAQ,gBAAgB;AAC/D,sBAAM,WAAkC,CAAC,EAAE,QAAAD,SAAQ,KAAA,MAAW;AACtD,wBAAA,SAAS,KAAKA,OAAM;AACtB,sBAAA;AACF,0BAAM,kBAAkB;sBACtB,GAAG;sBACH,GAAI;wBACF,MAAM,QAAQ;wBACd;sBAAA,KACG,CAAA;oBACP;AACO,2BAAA;kBAAA,QACD;AAEC,2BAAA;kBAAA;gBAEX;AACA,4BAAY,KAAK,QAAQ;cAAA;AAEpB,qBAAA,IAAI,OAAO,WAAW;YAC/B;YACA,CAAA;UAAA,MACG,CAAC;AAGR,gBAAM,QAA+B,CAAC,EAAE,QAAAA,QAAAA,MAAa;AAC/C,gBAAA,CAAC,KAAK,QAAQ;AAChB,qBAAO,CAAC;YAAA;AAEN,gBAAA,KAAK,WAAW,MAAM;AACjBA,qBAAAA;YAAA;AAEF,mBAAA,iBAAiB,KAAK,QAAQA,OAAM;UAC7C;AACA,yBAAe,KAAK,KAAK;AAEnB,gBAAA,YAAY,CAAC,OAAe,kBAA4B;AAExD,gBAAA,SAAS,eAAe,QAAQ;AAC3B,qBAAA;YAAA;AAGH,kBAAA,aAAa,eAAe,KAAK;AAEjC,kBAAA,OAAO,CAAC,cAAwB;AAC7B,qBAAA,UAAU,QAAQ,GAAG,SAAS;YACvC;AAEA,mBAAO,WAAW,EAAE,QAAQ,eAAe,KAAA,CAAM;UACnD;AAGO,iBAAA,UAAU,GAAGA,OAAM;QAC5B;AAEA,iBAAS,iBAAiB,MAAM;AAEvB,iBAAA,iBAAiB,YAAY,MAAM;AAC5C,cAAM,YAAY,KAAK,QAAQ,gBAAgB,MAAM;AAErD,cAAM,OACJ,KAAK,SAAS,OACV,KAAK,eAAe,OACpB,KAAK,OACH,iBAAiB,KAAK,MAAM,KAAK,eAAe,IAAI,IACpD;AAER,cAAM,UAAU,OAAO,IAAI,IAAI,KAAK;AAEpC,YAAI,YACF,KAAK,UAAU,OACX,KAAK,eAAe,QACpB,KAAK,QACH,iBAAiB,KAAK,OAAO,KAAK,eAAe,KAAK,IACtD,CAAC;AAET,oBAAY,iBAAiB,KAAK,eAAe,OAAO,SAAS;AAE1D,eAAA;UACL;UACA;UACA;UACA,OAAO;UACP,MAAM,QAAQ;UACd,MAAM,GAAG,QAAQ,GAAG,SAAS,GAAG,OAAO;UACvC,gBAAgB,KAAK;QACvB;MACF;AAEA,YAAM,mBAAmB,CACvB,OAAyB,CAAA,GACzB,eACG;;AACG,cAAA,OAAO,MAAM,IAAI;AACvB,YAAI,aAAa,aAAa,MAAM,UAAU,IAAI;AAElD,YAAI,CAAC,YAAY;AACf,cAAI,SAAS,CAAC;AAEd,gBAAM,aAAY,KAAA,KAAK,QAAQ,eAAb,OAAA,SAAA,GAAyB,KAAK,CAAC,MAAM;AACrD,kBAAM,QAAQ,cAAc,KAAK,UAAU,KAAK,UAAU;cACxD,IAAI,EAAE;cACN,eAAe;cACf,OAAO;YAAA,CACR;AAED,gBAAI,OAAO;AACA,uBAAA;AACF,qBAAA;YAAA;AAGF,mBAAA;UAAA,CAAA;AAGT,cAAI,WAAW;AACb,kBAAM,EAAE,MAAM,OAAO,GAAG,UAAc,IAAA;AACzB,yBAAA;cACX,GAAG,KAAK,MAAM,CAAC,MAAM,CAAC;cACtB,GAAG;cACH;YACF;AACA,yBAAa,MAAM,UAAU;UAAA;QAC/B;AAGF,cAAM,cAAc,KAAK,iBAAiB,MAAM,IAAI;AAC9C,cAAA,QAAQ,MAAM,MAAM,WAAW;AAErC,YAAI,YAAY;AACd,gBAAM,gBAAgB,KAAK,iBAAiB,YAAY,UAAU;AAC5D,gBAAA,cAAc,MAAM,YAAY,aAAa;AACnD,gBAAM,iBAAiB;QAAA;AAGlB,eAAA;MACT;AAEA,UAAI,KAAK,MAAM;AACb,eAAO,iBAAiB,MAAM;UAC5B,GAAG,KAAK,MAAM,CAAC,MAAM,CAAC;UACtB,GAAG,KAAK;QAAA,CACT;MAAA;AAGH,aAAO,iBAAiB,IAAI;IAC9B;AAIA,SAAA,iBAAmC,CAAC;MAClC;MACA;MACA,GAAG;IAAA,MACC;AACJ,YAAM,cAAc,MAAM;AAIxB,cAAM,eAAe;UACnB;UACA;UACA;QACF;AACa,qBAAA,QAAQ,CAAC,SAAS;AAC3B,eAAK,MAAc,IAAI,IAAI,KAAK,eAAe,MAAM,IAAI;QAAA,CAC5D;AACD,cAAM,UAAU,UAAU,KAAK,OAAO,KAAK,eAAe,KAAK;AAClD,qBAAA,QAAQ,CAAC,SAAS;AACtB,iBAAA,KAAK,MAAM,IAAI;QAAA,CACvB;AACM,eAAA;MACT;AAEA,YAAM,YAAY,KAAK,eAAe,SAAS,KAAK;AAEpD,YAAM,wBAAwB,KAAK;AAC9B,WAAA,wBAAwB,wBAA8B,MAAM;AAC/D,iCAAA,OAAA,SAAA,sBAAuB,QAAA;MAAQ,CAChC;AAGG,UAAA,aAAa,YAAA,GAAe;AAC9B,aAAK,KAAK;MAAA,OACL;AAEL,YAAI,EAAE,gBAAgB,oBAAoB,GAAG,YAAgB,IAAA;AAE7D,YAAI,gBAAgB;AACJ,wBAAA;YACZ,GAAG;YACH,OAAO;cACL,GAAG,eAAe;cAClB,WAAW;cACX,gBAAgB;gBACd,GAAG;gBACH,QAAQ,YAAY;gBACpB,OAAO;kBACL,GAAG,YAAY;kBACf,WAAW;kBACX,gBAAgB;kBAChB,KAAK;gBAAA;cACP;YACF;UAEJ;AAEA,cACE,YAAY,kBACZ,KAAK,QAAQ,kBACb,OACA;AACY,wBAAA,MAAM,YAAY,KAAK;UAAA;QACrC;AAGF,oBAAY,MAAM,8BAChB,sBAAsB,KAAK,QAAQ,6BAA6B;AAElE,aAAK,uBAAuB;AAE5B,aAAK,QAAQ,KAAK,UAAU,YAAY,MAAM;UAC5C,YAAY;UACZ,YAAY;UACZ,EAAE,cAAc;QAClB;MAAA;AAGG,WAAA,kBAAkB,KAAK,eAAe;AAE3C,UAAI,CAAC,KAAK,QAAQ,YAAY,MAAM;AAClC,aAAK,KAAK;MAAA;AAGZ,aAAO,KAAK;IACd;AAEA,SAAA,yBAAyB,CAAC;MACxB;MACA;MACA;MACA;MACA;MACA;MACA,GAAG;IACL,IAA8C,CAAA,MAAO;AACnD,UAAI,MAAM;AACR,cAAM,eAAe,KAAK,QAAQ,SAAS,MAAM;AAC3C,cAAA,SAAS,UAAU,MAAM;UAC7B,aAAa,UAAU,eAAe,eAAe;QAAA,CACtD;AACD,aAAK,KAAK,OAAO;AACjB,aAAK,SAAS,KAAK,QAAQ,YAAY,OAAO,MAAM;AAEpD,aAAK,OAAO,OAAO,KAAK,MAAM,CAAC;MAAA;AAG3B,YAAA,WAAW,KAAK,cAAc;QAClC,GAAI;QACJ,wBAAwB;MAAA,CACzB;AACD,aAAO,KAAK,eAAe;QACzB,GAAG;QACH;QACA;QACA;QACA;QACA;MAAA,CACD;IACH;AAEA,SAAA,WAAuB,CAAC,EAAE,IAAI,gBAAgB,MAAM,GAAG,KAAA,MAAW;AAChE,UAAI,gBAAgB;AAClB,YAAI,CAAC,MAAM;AACT,gBAAM,WAAW,KAAK,cAAc,EAAE,IAAI,GAAG,KAAA,CAAa;AAC1D,iBAAO,KAAK,QAAQ,WAAW,SAAS,IAAI;QAAA;AAE9C,YAAI,KAAK,SAAS;AACT,iBAAA,SAAS,QAAQ,IAAI;QAAA,OACvB;AACL,iBAAO,SAAS,OAAO;QAAA;AAEzB;MAAA;AAGF,aAAO,KAAK,uBAAuB;QACjC,GAAG;QACH;QACA;MAAA,CACD;IACH;AAIA,SAAA,OAAe,OAAO,SAA6C;AACjE,WAAK,iBAAiB,KAAK,cAAc,KAAK,cAAc;AAExD,UAAAE;AACA,UAAAC;AAEA,UAAA;AAGU,oBAAA,IAAI,QAAc,CAAC,YAAY;AAC3C,aAAK,gBAAgB,YAAY;;AAC3B,cAAA;AACF,kBAAM,OAAO,KAAK;AACZ,kBAAA,eAAe,KAAK,MAAM;AAGhC,iBAAK,cAAc;AAEf,gBAAA;AAEJ,kBAAM,MAAM;AAMO,+BAAA,KAAK,YAAY,IAAI;AAGjC,mBAAA,QAAQ,SAAS,CAAC,OAAO;gBAC5B,GAAG;gBACH,QAAQ;gBACR,WAAW;gBACX,UAAU;gBACV;;gBAEA,eAAe,EAAE,cAAc,OAAO,CAAC,MAAM;AACpC,yBAAA,CAAC,eAAe,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;gBACjD,CAAA;cAAA,EACD;YAAA,CACH;AAEG,gBAAA,CAAC,KAAK,MAAM,UAAU;AACxB,mBAAK,KAAK;gBACR,MAAM;gBACN,GAAG,sBAAsB;kBACvB,kBAAkB;kBAClB,UAAU;gBACX,CAAA;cAAA,CACF;YAAA;AAGH,iBAAK,KAAK;cACR,MAAM;cACN,GAAG,sBAAsB;gBACvB,kBAAkB;gBAClB,UAAU;cACX,CAAA;YAAA,CACF;AAED,kBAAM,KAAK,YAAY;cACrB,MAAM,QAAA,OAAA,SAAA,KAAM;cACZ,SAAS;cACT,UAAU;;cAEV,SAAS,YAAY;AAEnB,qBAAK,oBAAoB,YAAY;AAK/B,sBAAA;AACA,sBAAA;AACA,sBAAA;AAEJ,wBAAM,MAAM;AACL,yBAAA,QAAQ,SAAS,CAAC,MAAM;AAC3B,4BAAM,kBAAkB,EAAE;AACpB,4BAAA,aAAa,EAAE,kBAAkB,EAAE;AAEzC,uCAAiB,gBAAgB;wBAC/B,CAAC,UAAU,CAAC,WAAW,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;sBACtD;AACA,wCAAkB,WAAW;wBAC3B,CAAC,UACC,CAAC,gBAAgB,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;sBAClD;AACA,uCAAiB,gBAAgB;wBAAO,CAAC,UACvC,WAAW,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,EAAE;sBAC1C;AAEO,6BAAA;wBACL,GAAG;wBACH,WAAW;wBACX,UAAU,KAAK,IAAI;wBACnB,SAAS;wBACT,gBAAgB;wBAChB,eAAe;0BACb,GAAG,EAAE;0BACL,GAAG,eAAe,OAAO,CAAC,MAAM,EAAE,WAAW,OAAO;wBAAA;sBAExD;oBAAA,CACD;AACD,yBAAK,kBAAkB;kBAAA,CACxB;AAIC;oBACE,CAAC,gBAAgB,SAAS;oBAC1B,CAAC,iBAAiB,SAAS;oBAC3B,CAAC,gBAAgB,QAAQ;kBAAA,EAE3B,QAAQ,CAAC,CAAC,SAAS,IAAI,MAAM;AACrB,4BAAA,QAAQ,CAAC,UAAU;;AACzB,uBAAA,MAAAJ,MAAA,KAAK,gBAAgB,MAAM,OAAO,EAAG,SAAQ,IAAA,MAA7C,OAAA,SAAA,GAAA,KAAAA,KAAqD,KAAA;oBAAK,CAC3D;kBAAA,CACF;gBAAA,CACF;cAAA;YACH,CACD;UAAA,SACM,KAAK;AACR,gBAAA,mBAAmB,GAAG,GAAG;AAChB,cAAAG,YAAA;AACP,kBAAA,CAAC,KAAK,UAAU;AAClB,qBAAK,SAAS;kBACZ,GAAGA;kBACH,SAAS;kBACT,eAAe;gBAAA,CAChB;cAAA;YACH,WACS,WAAW,GAAG,GAAG;AACf,cAAAC,YAAA;YAAA;AAGR,iBAAA,QAAQ,SAAS,CAAC,OAAO;cAC5B,GAAG;cACH,YAAYD,YACRA,UAAS,aACTC,YACE,MACA,EAAE,QAAQ,KAAK,CAAC,MAAM,EAAE,WAAW,OAAO,IACxC,MACA;cACR,UAAAD;YAAA,EACA;UAAA;AAGA,cAAA,KAAK,sBAAsB,aAAa;AAC1C,aAAA,KAAA,KAAK,0BAAL,OAAA,SAAA,GAA4B,QAAA;AAC5B,iBAAK,oBAAoB;AACzB,iBAAK,wBAAwB;UAAA;AAEvB,kBAAA;QAAA,CACT;MAAA,CACF;AAED,WAAK,oBAAoB;AAEnB,YAAA;AAEN,aACG,KAAK,qBACN,gBAAgB,KAAK,mBACrB;AACA,cAAM,KAAK;MAAA;AAGT,UAAA,KAAK,iBAAA,GAAoB;AACtB,aAAA,QAAQ,SAAS,CAAC,OAAO;UAC5B,GAAG;UACH,YAAY;QAAA,EACZ;MAAA;IAEN;AAEA,SAAA,sBAAsB,CAAC,OAA4B;AAGjD,YAAM,uBACJ,KAAK,wBAAwB,KAAK,QAAQ;AAG5C,aAAO,KAAK;AAGV,UAAA,wBACA,OAAO,aAAa,eACpB,yBAAyB,YACzB,OAAO,SAAS,wBAAwB,YACxC;AAGI,YAAA;AAEJ,YACE,OAAO,yBAAyB,YAChC,KAAK,gCACL;AACA,gBAAM,OAAO,KAAK;AACZ,gBAAA,eAAe,KAAK,MAAM;AAEhC,gBAAM,8BACJ,OAAO,qBAAqB,UAAU,aAClC,qBAAqB;YACnB,sBAAsB;cACpB,kBAAkB;cAClB,UAAU;YACX,CAAA;UAAA,IAEH,qBAAqB;AAEC,sCAAA;YAC1B,QAAQ;YACR,OAAO;UACT;QAAA,OACK;AACuB,sCAAA;QAAA;AAG9B,iBAAS,oBAAoB,yBAAyB;MAAA,OACjD;AACF,WAAA;MAAA;IAEP;AAE6B,SAAA,cAAA,CAAC,IAAI,YAAY;;AACxC,UAAA;AACE,YAAA,aAAY,KAAA,KAAK,MAAM,mBAAX,OAAA,SAAA,GAA2B,KAAK,CAAC,MAAM,EAAE,OAAO,EAAA;AAC5D,YAAA,YAAY,KAAK,MAAM,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE;AACtD,YAAA,WAAW,KAAK,MAAM,cAAc,KAAK,CAAC,MAAM,EAAE,OAAO,EAAE;AAEjE,YAAM,aAAa,YACf,mBACA,YACE,YACA,WACE,kBACA;AAER,UAAI,YAAY;AACT,aAAA,QAAQ,SAAS,CAAC,MAAO;;AAAA,iBAAA;YAC5B,GAAG;YACH,CAAC,UAAU,IAAGH,MAAA,EAAE,UAAU,MAAZ,OAAA,SAAAA,IAAe;cAAI,CAAC,MAChC,EAAE,OAAO,KAAM,UAAU,QAAQ,CAAC,IAAK;YAAA;UACzC;QAAA,CACA;MAAA;AAGG,aAAA;IACT;AAEA,SAAA,WAAuB,CAAC,YAAoB;AACnC,aAAA;QACL,GAAG,KAAK,MAAM;QACd,GAAI,KAAK,MAAM,kBAAkB,CAAC;QAClC,GAAG,KAAK,MAAM;MAAA,EACd,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;IAChC;AAEA,SAAA,cAAc,OAAO;MACnB;MACA;MACA,SAAS;MACT;MACA,cAAc,KAAK;MACnB;IAAA,MAYoC;AAChC,UAAA;AACJ,UAAI,WAAW;AAEf,YAAM,iBAAiB,YAAY;AACjC,YAAI,CAAC,UAAU;AACF,qBAAA;AACX,iBAAM,WAAA,OAAA,SAAA,QAAA;QAAU;MAEpB;AAEM,YAAA,iBAAiB,CAAC,YAAoB;AAC1C,eAAO,CAAC,EAAE,cAAc,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;MAC1E;AAEM,YAAA,4BAA4B,CAAC,OAAsB,QAAa;;AAChE,YAAA,mBAAmB,GAAG,GAAG;AACvB,cAAA,CAAC,IAAI,gBAAgB;AACjB,kBAAA;UAAA;QACR;AAGF,YAAI,WAAW,GAAG,KAAK,WAAW,GAAG,GAAG;AAC1B,sBAAA,MAAM,IAAI,CAAC,UAAU;YAC/B,GAAG;YACH,QAAQ,WAAW,GAAG,IAClB,eACA,WAAW,GAAG,IACZ,aACA;YACN,YAAY;YACZ,OAAO;YACP,mBAAmB;YACnB,eAAe;UAAA,EACf;AAEE,cAAA,CAAE,IAAY,SAAS;AACvB,gBAAY,UAAU,MAAM;UAAA;AAGhC,WAAA,KAAA,MAAM,sBAAN,OAAA,SAAA,GAAyB,QAAA;AACzB,WAAA,KAAA,MAAM,kBAAN,OAAA,SAAA,GAAqB,QAAA;AACrB,WAAA,KAAA,MAAM,gBAAN,OAAA,SAAA,GAAmB,QAAA;AAEf,cAAA,WAAW,GAAG,GAAG;AACR,uBAAA;AACX,kBAAM,KAAK,gBAAgB,EAAE,GAAG,KAAK,eAAe,SAAA,CAAU;AACxD,kBAAA;UAAA,WACG,WAAW,GAAG,GAAG;AACrB,iBAAA,gBAAgB,SAAS,KAAK;cACjC;YAAA,CACD;AACD,aAAA,KAAA,KAAK,cAAL,OAAA,SAAA,GAAgB,eAAe;cAC7B,QAAQ;cACR,OAAO,KAAK,SAAS,MAAM,EAAE;YAAA,CAAA;AAEzB,kBAAA;UAAA;QACR;MAEJ;AAEI,UAAA;AACF,cAAM,IAAI,QAAc,CAAC,YAAY,cAAc;AACjD;AAAC,WAAC,YAAY;;AACR,gBAAA;AACF,oBAAM,oBAAoB,CACxB,OACA,KACA,eACG;;AACH,sBAAM,EAAE,IAAI,SAAS,QAAQ,IAAI,QAAQ,KAAK;AACxC,sBAAA,QAAQ,KAAK,gBAAgB,OAAO;AAK1C,oBAAI,eAAe,SAAS;AACpB,wBAAA;gBAAA;AAGR,oBAAI,aAAa;AACjB,qCAAqB,sBAAsB;AAC3C,0CAA0B,KAAK,SAAS,OAAO,GAAI,GAAG;AAElD,oBAAA;AACI,mBAAAK,OAAAL,MAAA,MAAA,SAAQ,YAAR,OAAA,SAAAK,IAAA,KAAAL,KAAkB,GAAA;gBAAA,SACjB,iBAAiB;AAClB,wBAAA;AACN,4CAA0B,KAAK,SAAS,OAAO,GAAI,GAAG;gBAAA;AAG5C,4BAAA,SAAS,CAAC,SAAS;;AAC7B,mBAAAA,MAAA,KAAK,sBAAL,OAAA,SAAAA,IAAwB,QAAA;AACxB,mBAAAK,MAAA,KAAK,gBAAL,OAAA,SAAAA,IAAkB,QAAA;AAEX,yBAAA;oBACL,GAAG;oBACH,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,WAAW,KAAK,IAAI;oBACpB,iBAAiB,IAAI,gBAAgB;oBACrC,mBAAmB;kBACrB;gBAAA,CACD;cACH;AAEW,yBAAA,CAAC,OAAO,EAAE,IAAI,SAAS,QAAA,CAAS,KAAK,QAAQ,QAAA,GAAW;AAC3D,sBAAA,gBAAgB,KAAK,SAAS,OAAO;AAC3C,sBAAM,iBAAgB,KAAA,QAAQ,QAAQ,CAAC,MAAjB,OAAA,SAAA,GAAoB;AAEpC,sBAAA,QAAQ,KAAK,gBAAgB,OAAO;AAE1C,sBAAM,YACJ,MAAM,QAAQ,aAAa,KAAK,QAAQ;AAE1C,sBAAM,gBAAgB,CAAC,EACrB,WACA,CAAC,KAAK,YACN,CAAC,eAAe,OAAO,MACtB,MAAM,QAAQ,UACb,MAAM,QAAQ,cACd,kBAAkB,KAAK,MACzB,OAAO,cAAc,YACrB,cAAc,aACb,MAAM,QAAQ,sBACZ,KAAA,KAAK,YAAL,OAAA,SAAA,GAAsB;AAG3B,oBAAI,oBAAoB;AACxB;;;kBAGE,cAAc,qBACd,cAAc;kBACd;AACA,sBAAI,eAAe;AACjB,+BAAW,MAAM;AACX,0BAAA;AAGa,uCAAA;sBAAA,QACT;sBAAA;oBAAA,GACP,SAAS;kBAAA;AAId,wBAAM,cAAc;AACpB,sCAAoB,KAAK,SAAS,OAAO,EAAG,WAAW;gBAAA;AAEzD,oBAAI,mBAAmB;AAEjB,sBAAA;AACU,gCAAA,SAAS,CAAC,SAAS;AAE7B,4BAAM,kBAAkB,KAAK;AACtB,6BAAA;wBACL,GAAG;wBACH,aAAa,wBAA8B,MAAM;AAC/C,6CAAA,OAAA,SAAA,gBAAiB,QAAA;wBAAQ,CAC1B;wBACD,mBAAmB,wBAA8B;sBACnD;oBAAA,CACD;AACK,0BAAA,kBAAkB,IAAI,gBAAgB;AAExC,wBAAA;AAEJ,wBAAI,eAAe;AAGjB,uCAAiB,WAAW,MAAM;AAC5B,4BAAA;AAGa,yCAAA;wBAAA,QACT;wBAAA;sBAAA,GACP,SAAS;oBAAA;AAGd,0BAAM,EAAE,aAAa,YAAA,IAAgB,KAAK,SAAS,OAAO;AAE1D,wBAAI,aAAa;AACG,wCAAA,OAAO,aAAa,cAAc;oBAAA;AAGtD,wBAAI,aAAa;AACG,wCAAA,OAAO,aAAa,iBAAiB;oBAAA;AAGnD,0BAAA,wBAAwB,MAC5B,gBACI,KAAK,SAAS,aAAa,EAAG,UAC7B,KAAK,QAAQ,WAAW,CAAC;AAEpB,gCAAA,SAAS,CAAC,UAAU;sBAC9B,GAAG;sBACH,YAAY;sBACZ,YAAY,KAAK,aAAa;sBAC9B;sBACA;sBACA,SAAS;wBACP,GAAG,sBAAsB;wBACzB,GAAG,KAAK;sBAAA;oBACV,EACA;AAEI,0BAAA,EAAE,QAAQ,QAAQ,SAAS,MAAA,IAC/B,KAAK,SAAS,OAAO;AAEjB,0BAAA,UAAU,eAAe,OAAO;AAEtC,0BAAM,sBAMF;sBACF;sBACA;sBACA;sBACA;sBACA;sBACA;sBACA,UAAU,CAAC,SACT,KAAK,SAAS,EAAE,GAAG,MAAM,eAAe,SAAA,CAAU;sBACpD,eAAe,KAAK;sBACpB,OAAO,UAAU,YAAY;sBAC7B;oBACF;AAEA,0BAAM,oBACH,QAAM,MAAA,KAAA,MAAM,SAAQ,eAAd,OAAA,SAAA,GAAA,KAAA,IAA2B,mBAAA,MAClC,CAAC;AAEH,wBACE,WAAW,iBAAiB,KAC5B,WAAW,iBAAiB,GAC5B;AACkB,wCAAA,OAAO,mBAAmB,aAAa;oBAAA;AAG/C,gCAAA,SAAS,CAAC,SAAS;AACtB,6BAAA;wBACL,GAAG;wBACH,qBAAqB;wBACrB,SAAS;0BACP,GAAG,sBAAsB;0BACzB,GAAG,KAAK;0BACR,GAAG;wBACL;wBACA;sBACF;oBAAA,CACD;kBAAA,SACM,KAAK;AACM,sCAAA,OAAO,KAAK,aAAa;kBAAA;AAGjC,8BAAA,SAAS,CAAC,SAAS;;AAC7B,qBAAAL,MAAA,KAAK,sBAAL,OAAA,SAAAA,IAAwB,QAAA;AAEjB,2BAAA;sBACL,GAAG;sBACH,mBAAmB;sBACnB,YAAY;oBACd;kBAAA,CACD;gBAAA;cACH;AAGF,oBAAM,uBAAuB,QAAQ,MAAM,GAAG,kBAAkB;AAChE,oBAAM,gBAA+C,CAAC;AAEtD,mCAAqB,QAAQ,CAAC,EAAE,IAAI,SAAS,QAAA,GAAW,UAAU;AAClD,8BAAA;mBACX,YAAY;AACX,0BAAM,EAAE,eAAe,kBAAA,IACrB,KAAK,SAAS,OAAO;AAEvB,wBAAI,uBAAuB;AAC3B,wBAAI,uBAAuB;AAE3B,wBAAI,mBAAmB;AACf,4BAAA;AACA,4BAAA,QAAQ,KAAK,SAAS,OAAO;AACnC,0BAAI,MAAM,OAAO;AACW,kDAAA,OAAO,MAAM,KAAK;sBAAA;oBAC9C,OACK;AACC,4BAAA,qBAAqB,cAAc,QAAQ,CAAC;AAC5C,4BAAA,QAAQ,KAAK,gBAAgB,OAAO;AAE1C,4BAAM,mBAAmB,MAAuB;AACxC,8BAAA;0BACJ;0BACA;0BACA;0BACA;0BACA;wBAAA,IACE,KAAK,SAAS,OAAO;AAEnBM,8BAAAA,WAAU,eAAe,OAAO;AAE/B,+BAAA;0BACL;0BACA,MAAM;0BACN,SAAS,CAAC,CAACA;0BACX;0BACA;0BACA;0BACA;0BACA,UAAU,CAAC,SACT,KAAK,SAAS,EAAE,GAAG,MAAM,eAAe,SAAA,CAAU;0BACpD,OAAOA,WAAU,YAAY;0BAC7B;wBACF;sBACF;AAGA,4BAAM,MAAM,KAAK,IAAA,IAAQ,KAAK,SAAS,OAAO,EAAG;AAE3C,4BAAA,UAAU,eAAe,OAAO;AAEtC,4BAAM,WAAW,UACZ,MAAM,QAAQ,oBACf,KAAK,QAAQ,2BACb,MACC,MAAM,QAAQ,aACf,KAAK,QAAQ,oBACb;AAEE,4BAAA,qBAAqB,MAAM,QAAQ;AAKzC,4BAAM,eACJ,OAAO,uBAAuB,aAC1B,mBAAmB,iBAAkB,CAAA,IACrC;AAEM,kCAAA,SAAS,CAAC,UAAU;wBAC9B,GAAG;wBACH,eAAe,wBAA8B;wBAC7C,SACE,CAAC,CAAC,WACF,CAAC,KAAK,MAAM,QAAQ,KAAK,CAAC,MAAM,EAAE,OAAO,OAAO;sBAAA,EAClD;AAEF,4BAAM,cAAc,MAAM;;AAClB,8BAAA,QAAQ,KAAK,SAAS,OAAO;AAEnC,4BAAI,CAAC,OAAO;AACV;wBAAA;AAEF,8BAAM,eAAe;0BACnB;0BACA;0BACA,QAAQ,MAAM;0BACd,YAAY,MAAM;wBACpB;AACA,8BAAM,iBAAgBD,OAAAL,MAAA,MAAM,SAAQ,SAAd,OAAA,SAAAK,IAAA,KAAAL,KAAqB,YAAA;AAC3C,8BAAM,OAAO,iBAAA,OAAA,SAAA,cAAe;AAC5B,8BAAM,QAAQ,iBAAA,OAAA,SAAA,cAAe;AAC7B,8BAAM,cAAc,iBAAA,OAAA,SAAA,cAAe;AAEnC,8BAAM,WAAUO,OAAAC,MAAA,MAAM,SAAQ,YAAd,OAAA,SAAAD,IAAA,KAAAC,KAAwB,YAAA;AACxC,8BAAM,WAAU,MAAA,KAAA,MAAM,SAAQ,YAAd,OAAA,SAAA,GAAA,KAAA,IAAwB,YAAA;AAC5B,oCAAA,SAAS,CAAC,UAAU;0BAC9B,GAAG;0BACH;0BACA;0BACA;0BACA;0BACA;wBAAA,EACA;sBACJ;AAEA,4BAAM,YAAY,YAAY;;AACxB,4BAAA;AAMF,gCAAM,6BAA6B,YAAY;AACvC,kCAAA,cAAc,KAAK,SAAS,OAAO;AAEzC,gCAAI,YAAY,mBAAmB;AACjC,oCAAM,YAAY;4BAAA;0BAEtB;AAGI,8BAAA;AACF,iCAAK,eAAe,KAAK;AAEb,wCAAA,SAAS,CAAC,UAAU;8BAC9B,GAAG;8BACH,YAAY;4BAAA,EACZ;AAGF,kCAAM,aACJ,QAAMH,OAAAL,MAAA,MAAM,SAAQ,WAAd,OAAA,SAAAK,IAAA,KAAAL,KAAuB,iBAAA,CAAA;AAE/B;8BACE,KAAK,SAAS,OAAO;8BACrB;4BACF;AAKA,kCAAM,MAAM;AAEZ,kCAAM,2BAA2B;AAIjC,kCAAM,MAAM;AAEZ,kCAAM,MAAM;AACE,0CAAA,SAAS,CAAC,UAAU;gCAC9B,GAAG;gCACH,OAAO;gCACP,QAAQ;gCACR,YAAY;gCACZ,WAAW,KAAK,IAAI;gCACpB;8BAAA,EACA;AACU,0CAAA;4BAAA,CACb;0BAAA,SACM,GAAG;AACV,gCAAI,QAAQ;AAEZ,kCAAM,2BAA2B;AAEjC,sDAA0B,KAAK,SAAS,OAAO,GAAI,CAAC;AAEhD,gCAAA;AACI,+BAAAO,OAAAC,MAAA,MAAA,SAAQ,YAAR,OAAA,SAAAD,IAAA,KAAAC,KAAkB,CAAA;4BAAA,SACjB,cAAc;AACb,sCAAA;AACR;gCACE,KAAK,SAAS,OAAO;gCACrB;8BACF;4BAAA;AAGF,kCAAM,MAAM;AACE,0CAAA,SAAS,CAAC,UAAU;gCAC9B,GAAG;gCACH;gCACA,QAAQ;gCACR,YAAY;8BAAA,EACZ;AACU,0CAAA;4BAAA,CACb;0BAAA;AAGH,2BAAA,KAAA,KAAK,cAAL,OAAA,SAAA,GAAgB,eAAe;4BAC7B,QAAQ;4BACR,OAAO,KAAK,SAAS,OAAO;0BAAA,CAAA;wBAAA,SAEvB,KAAK;AACZ,gCAAM,MAAM;AACE,wCAAA,SAAS,CAAC,UAAU;8BAC9B,GAAG;8BACH,eAAe;4BAAA,EACf;AACU,wCAAA;0BAAA,CACb;AACD,oDAA0B,KAAK,SAAS,OAAO,GAAI,GAAG;wBAAA;sBAE1D;AAGA,4BAAM,EAAE,QAAQ,QAAA,IAAY,KAAK,SAAS,OAAO;AACjD,6CACE,WAAW,cACV,YAAY,gBAAgB,MAAM;AACrC,0BAAI,WAAW,MAAM,QAAQ,YAAY,OAAO;sBAAA,WAErC,wBAAwB,CAAC,MAAM;AACjB,+CAAA;AACtB,yBAAC,YAAY;AACR,8BAAA;AACF,kCAAM,UAAU;AAChB,kCAAM,EAAE,eAAe,YAAA,IACrB,KAAK,SAAS,OAAO;AACvB,6CAAA,OAAA,SAAA,cAAe,QAAA;AACf,2CAAA,OAAA,SAAA,YAAa,QAAA;AACD,wCAAA,SAAS,CAAC,UAAU;8BAC9B,GAAG;8BACH,eAAe;4BAAA,EACf;0BAAA,SACK,KAAK;AACR,gCAAA,mBAAmB,GAAG,GAAG;AACrB,oCAAA,KAAK,SAAS,GAAG;4BAAA;0BACzB;wBACF,GACC;sBAEH,WAAA,WAAW,aACV,wBAAwB,MACzB;AACA,8BAAM,UAAU;sBAAA,OACX;AAIO,oCAAA;sBAAA;oBACd;AAEF,wBAAI,CAAC,sBAAsB;AACzB,4BAAM,EAAE,eAAe,YAAA,IACrB,KAAK,SAAS,OAAO;AACvB,uCAAA,OAAA,SAAA,cAAe,QAAA;AACf,qCAAA,OAAA,SAAA,YAAa,QAAA;oBAAQ;AAGX,gCAAA,SAAS,CAAC,UAAU;sBAC9B,GAAG;sBACH,YAAY,uBAAuB,KAAK,aAAa;sBACrD,eAAe,uBACX,KAAK,gBACL;sBACJ,SAAS;oBAAA,EACT;AACK,2BAAA,KAAK,SAAS,OAAO;kBAC3B,GAAA;gBACL;cAAA,CACD;AAEK,oBAAA,QAAQ,IAAI,aAAa;AAEpB,yBAAA;YAAA,SACJ,KAAK;AACZ,wBAAU,GAAG;YAAA;UACf,GACC;QAAA,CACJ;AACD,cAAM,eAAe;MAAA,SACd,KAAK;AACZ,YAAI,WAAW,GAAG,KAAK,WAAW,GAAG,GAAG;AACtC,cAAI,WAAW,GAAG,KAAK,CAAC,YAAY;AAClC,kBAAM,eAAe;UAAA;AAGjB,gBAAA;QAAA;MACR;AAGK,aAAA;IACT;AAEA,SAAA,aAQI,CAAC,SAAS;AACN,YAAA,aAAa,CAAC,MAAkC;;AACpD,cAAI,KAAA,QAAA,OAAA,SAAA,KAAM,WAAN,OAAA,SAAA,GAAA,KAAA,MAAe,CAAA,MAAmC,MAAM;AACnD,iBAAA;YACL,GAAG;YACH,SAAS;YACT,GAAI,EAAE,WAAW,UACZ,EAAE,QAAQ,WAAW,OAAO,OAAA,IAC7B,CAAA;UACN;QAAA;AAEK,eAAA;MACT;AAEK,WAAA,QAAQ,SAAS,CAAC,MAAO;;AAAA,eAAA;UAC5B,GAAG;UACH,SAAS,EAAE,QAAQ,IAAI,UAAU;UACjC,eAAe,EAAE,cAAc,IAAI,UAAU;UAC7C,iBAAgB,KAAA,EAAE,mBAAF,OAAA,SAAA,GAAkB,IAAI,UAAA;QAAU;MAAA,CAChD;AAEF,aAAO,KAAK,KAAK,EAAE,MAAM,QAAA,OAAA,SAAA,KAAM,KAAA,CAAM;IACvC;AAEA,SAAA,kBAAkB,CAAC,QAAuC;AACxD,YAAML,YAAW;AAEb,UAAA,CAACA,UAAS,MAAM;AAClB,QAAAA,UAAS,OAAO,KAAK,cAAcA,SAAe,EAAE;MAAA;AAG/C,aAAAA;IACT;AAEA,SAAA,aAAiC,CAAC,SAAS;AACzC,YAAM,SAAS,QAAA,OAAA,SAAA,KAAM;AACrB,UAAI,WAAW,QAAW;AACnB,aAAA,QAAQ,SAAS,CAAC,MAAM;AACpB,iBAAA;YACL,GAAG;YACH,eAAe,EAAE,cAAc;cAC7B,CAAC,MAAM,CAAC,OAAO,CAA8B;YAAA;UAEjD;QAAA,CACD;MAAA,OACI;AACA,aAAA,QAAQ,SAAS,CAAC,MAAM;AACpB,iBAAA;YACL,GAAG;YACH,eAAe,CAAA;UACjB;QAAA,CACD;MAAA;IAEL;AAEA,SAAA,oBAAoB,MAAM;AAElB,YAAA,SAAS,CAAC,MAAkC;AAChD,cAAM,QAAQ,KAAK,gBAAgB,EAAE,OAAO;AAExC,YAAA,CAAC,MAAM,QAAQ,QAAQ;AAClB,iBAAA;QAAA;AAKT,cAAM,UACH,EAAE,UACE,MAAM,QAAQ,iBAAiB,KAAK,QAAQ,uBAC5C,MAAM,QAAQ,UAAU,KAAK,QAAQ,kBAC1C,IAAI,KAAK;AAEJ,eAAA,EAAE,EAAE,WAAW,WAAW,KAAK,IAAA,IAAQ,EAAE,YAAY;MAC9D;AACK,WAAA,WAAW,EAAE,OAAA,CAAQ;IAC5B;AAEA,SAAA,iBAAiB,CAAC,UAAoB;AAChC,UAAA,MAAM,iBAAiB,QAAW;AACpC,YAAI,MAAM,QAAQ;AAChB,gBAAM,eAAe,MAAM,OAAA,EAAS,KAAK,CAAC,cAAc;AAEtD,kBAAM,EAAE,IAAI,KAAK,GAAGM,SAAAA,IAAY,UAAU;AACnC,mBAAA,OAAO,MAAM,SAASA,QAAO;UAAA,CACrC;QAAA,OACI;AACC,gBAAA,eAAe,QAAQ,QAAQ;QAAA;MACvC;AAME,UAAA,MAAM,uBAAuB,QAAW;AACpC,cAAA,qBAAqB,MAAM,aAAa;UAAK,MACjD,QAAQ;YACN,eAAe,IAAI,OAAO,SAAS;AAC3B,oBAAA,YAAY,MAAM,QAAQ,IAAI;AACpC,kBAAK,aAAA,OAAA,SAAA,UAAmB,SAAS;AAC/B,sBAAO,UAAkB,QAAQ;cAAA;YAEpC,CAAA;UAAA;QAEL;MAAA;AAEF,aAAO,MAAM;IACf;AAEA,SAAA,eAKI,OAAO,SAAS;AACZ,YAAA,OAAO,KAAK,cAAc,IAAW;AAEvC,UAAA,UAAU,KAAK,YAAY,MAAM;QACnC,cAAc;QACd,SAAS;QACT,MAAM;MAAA,CACP;AAED,YAAM,iBAAiB,IAAI;QACzB,CAAC,GAAG,KAAK,MAAM,SAAS,GAAI,KAAK,MAAM,kBAAkB,CAAG,CAAA,EAAE;UAC5D,CAAC,MAAM,EAAE;QAAA;MAEb;AAEM,YAAA,iBAAA,oBAAqB,IAAI;QAC7B,GAAG;QACH,GAAG,KAAK,MAAM,cAAc,IAAI,CAAC,MAAM,EAAE,EAAE;MAAA,CAC5C;AAGD,YAAM,MAAM;AACF,gBAAA,QAAQ,CAAC,UAAU;AACzB,cAAI,CAAC,eAAe,IAAI,MAAM,EAAE,GAAG;AAC5B,iBAAA,QAAQ,SAAS,CAAC,OAAO;cAC5B,GAAG;cACH,eAAe,CAAC,GAAI,EAAE,eAAuB,KAAK;YAAA,EAClD;UAAA;QACJ,CACD;MAAA,CACF;AAEG,UAAA;AACQ,kBAAA,MAAM,KAAK,YAAY;UAC/B;UACA,UAAU;UACV,SAAS;UACT,aAAa,CAAC,IAAI,YAAY;AAExB,gBAAA,eAAe,IAAI,EAAE,GAAG;AAChB,wBAAA,QAAQ,IAAI,CAAC,MAAO,EAAE,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAE;YAAA,OACtD;AACA,mBAAA,YAAY,IAAI,OAAO;YAAA;UAC9B;QACF,CACD;AAEM,eAAA;MAAA,SACA,KAAK;AACR,YAAA,WAAW,GAAG,GAAG;AACnB,cAAI,IAAI,gBAAgB;AACf,mBAAA;UAAA;AAEF,iBAAA,MAAM,KAAK,aAAa;YAC7B,GAAI;YACJ,eAAe;UAAA,CAChB;QAAA;AAEC,YAAA,CAAC,WAAW,GAAG,GAAG;AAEpB,kBAAQ,MAAM,GAAG;QAAA;AAEZ,eAAA;MAAA;IAEX;AAOI,SAAA,aAAA,CAAC,UAAU,SAAS;AACtB,YAAM,gBAAgB;QACpB,GAAG;QACH,IAAI,SAAS,KACT,KAAK;UACF,SAAS,QAAQ;UAClB,SAAS;QAAA,IAEX;QACJ,QAAQ,SAAS,UAAU,CAAC;QAC5B,aAAa;MACf;AACM,YAAA,OAAO,KAAK,cAAc,aAAoB;AAEpD,WAAI,QAAA,OAAA,SAAA,KAAM,YAAW,KAAK,MAAM,WAAW,WAAW;AAC7C,eAAA;MAAA;AAGH,YAAA,WACJ,QAAA,OAAA,SAAA,KAAM,aAAY,SAAY,CAAC,KAAK,MAAM,YAAY,KAAK;AAEvD,YAAA,eAAe,UACjB,KAAK,iBACL,KAAK,MAAM,oBAAoB,KAAK,MAAM;AAE9C,YAAM,QAAQ,cAAc,KAAK,UAAU,aAAa,UAAU;QAChE,GAAG;QACH,IAAI,KAAK;MAAA,CACV;AAED,UAAI,CAAC,OAAO;AACH,eAAA;MAAA;AAET,UAAI,SAAS,QAAQ;AACf,YAAA,CAAC,UAAU,OAAO,SAAS,QAAQ,EAAE,SAAS,KAAK,CAAC,GAAG;AAClD,iBAAA;QAAA;MACT;AAGE,UAAA,WAAU,QAAA,OAAA,SAAA,KAAM,kBAAiB,OAAO;AACnC,eAAA,UAAU,aAAa,QAAQ,KAAK,QAAQ,EAAE,SAAS,KAAA,CAAM,IAChE,QACA;MAAA;AAGC,aAAA;IACT;AAuBkB,SAAA,kBAAA,CAChB,SACA,KACA;MACE,cAAc,KAAK;IACrB,IAKI,CAAA,MACD;;AAGH,YAAM,cAAc,KAAK,WAAW,IAAI,WAAW,EAAE,KAAK,KAAK;AAC/D,YAAM,mBAAkD,CAAC;AAGzD,iBAAW,SAAS,SAAS;AACV,yBAAA,MAAM,OAAO,IAAI;MAAA;AAIpC,UACE,CAAC,YAAY,QAAQ,uBACpB,KAAA,KAAK,YAAL,OAAA,SAAA,GAAsB,2BACvB;AACY,oBAAA,QAAQ,oBAClB,KAAK,QACL;MAAA;AAIJ;QACE,YAAY,QAAQ;QACpB;MACF;AAGM,YAAA,gBAAgB,iBAAiB,YAAY,EAAE;AAErD;QACE;QACA,qCAAqC,YAAY;MACnD;AAGY,kBAAA,cAAc,IAAI,CAAC,UAAU;QACvC,GAAG;QACH,QAAQ;QACR,OAAO;QACP,YAAY;MAAA,EACZ;AAEF,UAAK,IAAY,eAAe,iBAAiB,YAAY,aAAa;AACpE,YAAA,UAAU,YAAY,YAAY;AACjC,aAAA,gBAAgB,SAAS,KAAK;UACjC;QAAA,CACD;MAAA;IAEL;AAEA,SAAA,mBAAmB,MAAM;AAChB,aAAA,KAAK,QAAQ,MAAM,QAAQ;QAChC,CAAC,MAAM,EAAE,WAAW,cAAc,EAAE;MACtC;IACF;AAtwEE,SAAK,OAAO;MACV,qBAAqB;MACrB,kBAAkB;MAClB,qBAAqB;MACrB,SAAS;MACT,GAAG;MACH,eAAe,QAAQ,iBAAiB;MACxC,cAAc,QAAQ,gBAAgB;MACtC,iBAAiB,QAAQ,mBAAmB;MAC5C,aAAa,QAAQ,eAAe;IAAA,CACrC;AAEG,QAAA,OAAO,aAAa,aAAa;AACjC,aAAe,iBAAiB;IAAA;EACpC;EAqGF,IAAI,QAAQ;AACV,WAAO,KAAK,QAAQ;EAAA;EAuMtB,IAAI,kBAAkB;AACpB,WAAO,KAAK;EAAA;EA8BN,oBACN,MACA,MACsB;AACtB,UAAM,EAAE,YAAY,eAAe,YAAA,IAAgB,KAAK;MACtD;MACA,QAAA,OAAA,SAAA,KAAM;IACR;AACA,QAAI,mBAAmB;AAGvB;;MAEE,aACI,WAAW,SAAS,OAAO,YAAY,IAAI;;QAE3C,cAAc,KAAK,QAAQ;;MAC/B;AAEI,UAAA,KAAK,QAAQ,eAAe;AAChB,sBAAA,KAAK,KAAK,QAAQ,aAAa;MAAA,OACxC;AAEc,2BAAA;MAAA;IACrB;AAGF,UAAM,yBAAyB,MAAM;AACnC,UAAI,CAAC,kBAAkB;AACd,eAAA;MAAA;AAGL,UAAA,KAAK,QAAQ,iBAAiB,QAAQ;AACxC,iBAAS,IAAI,cAAc,SAAS,GAAG,KAAK,GAAG,KAAK;AAC5C,gBAAA,QAAQ,cAAc,CAAC;AAC7B,cAAI,MAAM,UAAU;AAClB,mBAAO,MAAM;UAAA;QACf;MACF;AAGK,aAAA;IAAA,GACN;AAEH,UAAM,cAAc,cAAc,IAAI,CAAC,UAAU;;AAC3C,UAAA;AAEJ,YAAM,gBACJ,KAAA,MAAM,QAAQ,WAAd,OAAA,SAAA,GAAsB,UAAS,MAAM,QAAQ;AAE/C,UAAI,aAAa;AACX,YAAA;AACI,gBAAA,eAAe,YAAY,WAAW;AAErC,iBAAA,OAAO,aAAa,YAAY;QAAA,SAChC,KAAU;AACG,8BAAA,IAAI,eAAe,IAAI,SAAS;YAClD,OAAO;UAAA,CACR;AAED,cAAI,QAAA,OAAA,SAAA,KAAM,cAAc;AAChB,kBAAA;UAAA;AAGD,iBAAA;QAAA;MACT;AAGF;IAAA,CACD;AAED,UAAM,UAAgC,CAAC;AAEjC,UAAA,mBAAmB,CAAC,gBAAgC;AACxD,YAAM,gBAAgB,eAAA,OAAA,SAAA,YAAa;AAEnC,YAAM,gBAAgB,CAAC,gBACjB,KAAK,QAAQ,WAAmB,CAAA,IACjC,YAAY,WAAW,KAAK,QAAQ,WAAW,CAAC;AAE9C,aAAA;IACT;AAEc,kBAAA,QAAQ,CAAC,OAAO,UAAU;;AAQhC,YAAA,cAAc,QAAQ,QAAQ,CAAC;AAErC,YAAM,CAAC,gBAAgB,mBAAmB,WAAW,KAIhD,MAAM;AAEH,cAAA,gBAAe,eAAA,OAAA,SAAA,YAAa,WAAU,KAAK;AAC3C,cAAA,sBAAqB,eAAA,OAAA,SAAA,YAAa,kBAAiB,CAAC;AAEtD,YAAA;AACI,gBAAA,eACJ,eAAe,MAAM,QAAQ,gBAAgB,EAAE,GAAG,aAAc,CAAA,KAChE,CAAC;AAEI,iBAAA;YACL;cACE,GAAG;cACH,GAAG;YACL;YACA,EAAE,GAAG,oBAAoB,GAAG,aAAa;YACzC;UACF;QAAA,SACO,KAAU;AACjB,cAAI,mBAAmB;AACnB,cAAA,EAAE,eAAe,mBAAmB;AACnB,+BAAA,IAAI,iBAAiB,IAAI,SAAS;cACnD,OAAO;YAAA,CACR;UAAA;AAGH,cAAI,QAAA,OAAA,SAAA,KAAM,cAAc;AAChB,kBAAA;UAAA;AAGR,iBAAO,CAAC,cAAc,CAAC,GAAG,gBAAgB;QAAA;MAC5C,GACC;AAOG,YAAA,eACJ,MAAA,KAAA,MAAM,SAAQ,eAAd,OAAA,SAAA,GAAA,KAAA,IAA2B;QACzB,QAAQ;MACT,CAAA,MAAK;AAER,YAAM,iBAAiB,aAAa,KAAK,UAAU,UAAU,IAAI;AAEjE,YAAM,EAAE,YAAY,iBAAiB,IAAI,gBAAgB;QACvD,MAAM,MAAM;QACZ,QAAQ;QACR,eAAe,KAAK;MAAA,CACrB;AAED,YAAM,UACJ,gBAAgB;QACd,MAAM,MAAM;QACZ,QAAQ;QACR,gBAAgB;QAChB,eAAe,KAAK;MAAA,CACrB,EAAE,mBAAmB;AAQlB,YAAA,gBAAgB,KAAK,SAAS,OAAO;AAErC,YAAA,gBAAgB,KAAK,MAAM,QAAQ;QACvC,CAAC,MAAM,EAAE,YAAY,MAAM;MAC7B;AAEM,YAAA,QAAQ,gBAAgB,SAAS;AAEnC,UAAA;AAEJ,UAAI,eAAe;AACT,gBAAA;UACN,GAAG;UACH;UACA,QAAQ,gBACJ,iBAAiB,cAAc,QAAQ,WAAW,IAClD;UACJ,eAAe;UACf,QAAQ,gBACJ,iBAAiB,cAAc,QAAQ,cAAc,IACrD,iBAAiB,cAAc,QAAQ,cAAc;UACzD,eAAe;QACjB;MAAA,OACK;AACL,cAAM,SACJ,MAAM,QAAQ,UACd,MAAM,QAAQ,cACd,MAAM,UACN,kBAAkB,KAAK,IACnB,YACA;AAEE,gBAAA;UACN,IAAI;UACJ;UACA,SAAS,MAAM;UACf,QAAQ,gBACJ,iBAAiB,cAAc,QAAQ,WAAW,IAClD;UACJ,eAAe;UACf,UAAU,UAAU,CAAC,KAAK,UAAU,gBAAgB,CAAC;UACrD,WAAW,KAAK,IAAI;UACpB,QAAQ,gBACJ,iBAAiB,cAAc,QAAQ,cAAc,IACrD;UACJ,eAAe;UACf,aAAa;UACb;UACA,YAAY;UACZ,OAAO;UACP,aAAa,YAAY,KAAK;UAC9B,gBAAgB,CAAC;UACjB,qBAAqB,CAAC;UACtB,SAAS,CAAC;UACV,iBAAiB,IAAI,gBAAgB;UACrC,YAAY;UACZ;UACA,YAAY,gBACR,iBAAiB,cAAc,YAAY,UAAU,IACrD;UACJ,SAAS;UACT,SAAS;UACT,OAAO;UACP,SAAS;UACT,aAAa;UACb,MAAM;UACN,YAAY,MAAM,QAAQ,cAAc,CAAC;UACzC,aAAa,wBAAwB;UACrC,UAAU,MAAM;QAClB;MAAA;AAGE,UAAA,EAAC,QAAA,OAAA,SAAA,KAAM,UAAS;AAEZ,cAAA,iBAAiB,0BAA0B,MAAM;MAAA;AAIzD,YAAM,cAAc;AAEd,YAAA,gBAAgB,iBAAiB,WAAW;AAElD,YAAM,UAAU;QACd,GAAG;QACH,GAAG,MAAM;QACT,GAAG,MAAM;MACX;AAEA,cAAQ,KAAK,KAAK;IAAA,CACnB;AAEO,YAAA,QAAQ,CAAC,OAAO,UAAU;;AAChC,YAAM,QAAQ,KAAK,gBAAgB,MAAM,OAAO;AAChD,YAAM,gBAAgB,KAAK,SAAS,MAAM,EAAE;AAG5C,UAAI,CAAC,kBAAiB,QAAA,OAAA,SAAA,KAAM,oBAAmB,MAAM;AAC7C,cAAA,cAAc,QAAQ,QAAQ,CAAC;AAC/B,cAAA,gBAAgB,iBAAiB,WAAW;AAGlD,cAAM,mBAA4D;UAChE,MAAM,MAAM;UACZ,QAAQ,MAAM;UACd,SAAS;UACT,UAAU;UACV,UAAU,CAACC,UACT,KAAK,SAAS,EAAE,GAAGA,OAAM,eAAe,KAAA,CAAM;UAChD,eAAe,KAAK;UACpB,OAAO,MAAM;UACb,iBAAiB,MAAM;UACvB,SAAS,CAAC,CAAC,MAAM;UACjB;QACF;AAGA,cAAM,mBAAiB,MAAA,KAAA,MAAM,SAAQ,YAAd,OAAA,SAAA,GAAA,KAAA,IAAwB,gBAAA,MAAqB,CAAC;AAErE,cAAM,UAAU;UACd,GAAG;UACH,GAAG,MAAM;UACT,GAAG,MAAM;QACX;MAAA;IACF,CACD;AAEM,WAAA;EAAA;AA4oDX;AAEO,IAAM,mBAAN,cAA+B,MAAM;AAAC;AAEtC,IAAM,iBAAN,cAA6B,MAAM;AAAC;AAK3B,SAAA,OAGd,IAAsB,KAAY;AAClC,SAAO,UACF,SACuC;AACpC,UAAA,WAAW,MAAM,GAAG;AAC1B,WAAO,SAAS,OAAO,SAAS,EAAE,GAAG,IAAI;EAC3C;AACF;AAEO,SAAS,sBACd,UACkB;AACX,SAAA;IACL,UAAU;IACV,WAAW;IACX,iBAAiB;IACjB,QAAQ;IACR,kBAAkB;IAClB;IACA,SAAS,CAAC;IACV,gBAAgB,CAAC;IACjB,eAAe,CAAC;IAChB,YAAY;EACd;AACF;AAEA,SAAS,eAAeC,iBAA8B,OAAyB;AACzEA,MAAAA,mBAAkB,KAAM,QAAO,CAAC;AAEpC,MAAI,eAAeA,iBAAgB;AACjC,UAAM,SAASA,gBAAe,WAAW,EAAE,SAAS,KAAK;AAEzD,QAAI,kBAAkB;AACd,YAAA,IAAI,iBAAiB,gCAAgC;AAE7D,QAAI,OAAO;AACH,YAAA,IAAI,iBAAiB,KAAK,UAAU,OAAO,QAAQ,QAAW,CAAC,GAAG;QACtE,OAAO;MAAA,CACR;AAEH,WAAO,OAAO;EAAA;AAGhB,MAAI,WAAWA,iBAAgB;AACtBA,WAAAA,gBAAe,MAAM,KAAK;EAAA;AAG/B,MAAA,OAAOA,oBAAmB,YAAY;AACxC,WAAOA,gBAAe,KAAK;EAAA;AAG7B,SAAO,CAAC;AACV;AAEO,IAAM,iBAAiB;EAC5B;EACA;EACA;EACA;AACF;AAEA,SAAS,kBAAkB,OAAiB;;AAC1C,aAAW,iBAAiB,gBAAgB;AAC1C,SAAK,KAAA,MAAM,QAAQ,aAAa,MAA3B,OAAA,SAAA,GAAsC,SAAS;AAC3C,aAAA;IAAA;EACT;AAEK,SAAA;AACT;;;ACroGa,IAAA,uBAAuB,OAAO,IAAI,sBAAsB;AAsBrD,SAAA,MACd,UACA,SAGA;AACA,QAAM,UAAU;AAEX,MAAA,QAAgB,oBAAoB,GAAG;AACnC,WAAA;EAAA;AAET,UAAQ,oBAAoB,IAAI,EAAE,QAAQ,UAAU;AAGjD,UAAA,KAAK,CAAC,SAAS;AACN,YAAA,oBAAoB,EAAE,SAAS;AAC/B,YAAA,oBAAoB,EAAE,OAAO;EAAA,CACtC,EACA,MAAM,CAAC,UAAU;AACR,YAAA,oBAAoB,EAAE,SAAS;AACrC,YAAQ,oBAAoB,EAAU,QAAQ;MAC9C,QAAO,WAAA,OAAA,SAAA,QAAS,mBAAkB,uBAAuB,KAAK;MAC9D,iBAAiB;IACnB;EAAA,CACD;AAEI,SAAA;AACT;;;ACwCa,IAAA,UAAU,CACrB,OACA,SAC8C;AACxC,QAAA,QAAS,KAAgB,MAAM,GAAG;AACpC,MAAA;AACJ,MAAI,QAAa;AAEjB,UAAQ,OAAO,MAAM,MAAY,MAAA,QAAQ,SAAS,MAAM;AACtD,YAAQ,MAAM,IAAI;EAAA;AAGpB,SAAO,SAAS;AAClB;;;ACnGO,SAAS,mBACd,MACiC;AACjC,SAAO,CAAC,EAAE,QAAQ,KAAA,MAAW;AACrB,UAAA,SAAS,KAAK,MAAM;AAC1B,QAAI,SAAS,MAAM;AACjB,aAAO,EAAE,GAAG,QAAQ,GAAG,OAAO;IAAA;AAG3B,SAAA,QAAQ,CAAC,QAAQ;AAChB,UAAA,EAAE,OAAO,SAAS;AACb,eAAA,GAAG,IAAI,OAAO,GAAG;MAAA;IAC1B,CACD;AACM,WAAA;EACT;AACF;AAEO,SAAS,kBASd,OAAyD;AACzD,SAAO,CAAC,EAAE,QAAQ,KAAA,MAAW;AAC3B,QAAI,UAAU,MAAM;AAClB,aAAO,CAAC;IAAA;AAEJ,UAAA,SAAS,KAAK,MAAM;AACtB,QAAA,MAAM,QAAQ,KAAK,GAAG;AAClB,YAAA,QAAQ,CAAC,QAAQ;AACrB,eAAO,OAAO,GAAG;MAAA,CAClB;IAAA,OACI;AACE,aAAA,QAAQ,KAAgC,EAAE;QAC/C,CAAC,CAAC,KAAK,KAAK,MAAM;AAChB,cAAI,UAAU,OAAO,GAAG,GAAG,KAAK,GAAG;AACjC,mBAAO,OAAO,GAAG;UAAA;QACnB;MAEJ;IAAA;AAEK,WAAA;EACT;AACF;;;AC0iBO,IAAM,iBAAiB;;;ACsqBvB,IAAM,YAAN,MAeL;EAqDA,YACE,SAcA;AA0BF,SAAA,OAAO,CAAC,SAAgE;;AACtE,WAAK,gBAAgB,KAAK;AAE1B,YAAMC,WAAU,KAAK;AAkBrB,YAAM,SAAS,EAACA,YAAA,OAAA,SAAAA,SAAS,SAAQ,EAACA,YAAA,OAAA,SAAAA,SAAS;AAEtC,WAAA,eAAc,MAAA,KAAA,KAAK,SAAQ,mBAAb,OAAA,SAAA,GAAA,KAAA,EAAA;AAEnB,UAAI,QAAQ;AACV,aAAK,QAAQ;MAAA,WACJ,CAAC,KAAK,aAAa;AAC5B,cAAM,IAAI;UACR;QACF;MAAA;AAGE,UAAA,OAA2B,SAAS,cAAcA,YAAA,OAAA,SAAAA,SAAS;AAG3D,UAAA,QAAQ,SAAS,KAAK;AACxB,eAAO,aAAa,IAAI;MAAA;AAGpB,YAAA,YAAWA,YAAA,OAAA,SAAAA,SAAS,OAAM;AAG5B,UAAA,KAAK,SACL,cACA,UAAU;QACR,KAAK,YAAY,OAAO,cAAc,KAAK,KAAK,YAAY;QAC5D;MAAA,CACD;AAEL,UAAI,SAAS,aAAa;AACjB,eAAA;MAAA;AAGT,UAAI,OAAO,aAAa;AACtB,aAAK,UAAU,CAAC,KAAK,EAAE,CAAC;MAAA;AAGpB,YAAA,WACJ,OAAO,cAAc,MAAM,UAAU,CAAC,KAAK,YAAY,UAAU,IAAI,CAAC;AAExE,WAAK,QAAQ;AACb,WAAK,MAAM;AACX,WAAK,YAAY;AACjB,WAAK,MAAM;AACX,WAAK,QAAOA,YAAA,OAAA,SAAAA,SAAS,QAAO,KAAK,cAAc;IACjD;AAEA,SAAA,QAAQ,CAAC,UAAuB;AAC9B,WAAK,QAAQ,MAAM;AACnB,WAAK,MAAM,MAAM;AACjB,WAAK,YAAY,MAAM;AACvB,WAAK,MAAM,MAAM;AACjB,WAAK,OAAO,MAAM;AACb,WAAA,QAAQ,iBAAiB,MAAM,QAAQ;AAC5C,WAAK,WAAW,MAAM;IACxB;AAEA,SAAA,cAcI,CAAC,aAAa;AACT,aAAA,KAAK,iBAAiB,QAAQ;IACvC;AAEA,SAAA,mBAcI,CAAC,aAAa;AACZ,UAAA,MAAM,QAAQ,QAAQ,GAAG;AAC3B,aAAK,WAAW;MAAA;AAGlB,UAAI,OAAO,aAAa,YAAY,aAAa,MAAM;AAChD,aAAA,WAAW,OAAO,OAAO,QAAQ;MAAA;AAGjC,aAAA;IACT;AAEA,SAAA,gBAcI,MAAM;AACD,aAAA;IACT;AAEA,SAAA,eAAe,CAAeA,aAaxB;AACG,aAAA,OAAO,KAAK,SAASA,QAAO;AAC5B,aAAA;IAgBT;AAEA,SAAA,SAAS,CACPA,aAYS;AACF,aAAA,OAAO,KAAK,SAASA,QAAO;AAC5B,aAAA;IACT;AAEA,SAAA,OAiBI,CAACC,YAAW;AACd,WAAK,SAASA;AACP,aAAA;IACT;AA5OO,SAAA,UAAW,WAAmB,CAAC;AAC/B,SAAA,SAAS,EAAC,WAAA,OAAA,SAAA,QAAS;AAEnB,SAAA,WAAA,OAAA,SAAA,QAAiB,QAAO,WAAA,OAAA,SAAA,QAAiB,OAAM;AAC5C,YAAA,IAAI,MAAM,qDAAqD;IAAA;EACvE;EAjDF,IAAW,KAAK;AACd,WAAO,KAAK;EAAA;EAGd,IAAW,KAAK;AACd,WAAO,KAAK;EAAA;EAGd,IAAW,OAAO;AAChB,WAAO,KAAK;EAAA;EAGd,IAAW,WAAW;AACpB,WAAO,KAAK;EAAA;EAGd,IAAW,MAAM;AACf,WAAO,KAAK;EAAA;AAwQhB;AAEO,IAAM,eAAN,MAAsE;EAG3E,YAAY,EAAE,GAAA,GAAmB;AAIjC,SAAA,WAAW,CAAC,SAAyB;AACnC,aAAO,SAAS,EAAE,SAAS,KAAK,IAAc,GAAG,KAAA,CAAM;IACzD;AALE,SAAK,KAAK;EAAA;AAMd;AA4BO,IAAM,gBAAN,cASG,UAeR;EACA,YACE,SAQA;AACA,UAAM,OAAc;EAAA;AAExB;", "names": ["_a", "blockers", "index", "isMatch", "store", "storageKey", "pathname", "_a", "search", "next", "redirect", "notFound", "_b", "preload", "_d", "_c", "options", "opts", "validateSearch", "options", "lazyFn"]}