import {
  startSerializer
} from "./chunk-DE7T3MBN.js";
import "./chunk-K2ZHHHIO.js";
import {
  encode,
  isNotFound,
  isPlainObject,
  isRedirect
} from "./chunk-OSGES7B7.js";
import "./chunk-5WRI5ZAA.js";

// node_modules/@tanstack/start-server-functions-fetcher/dist/esm/index.js
async function serverFnFetcher(url, args, handler) {
  var _a;
  const _first = args[0];
  if (isPlainObject(_first) && _first.method) {
    const first = _first;
    const type = first.data instanceof FormData ? "formData" : "payload";
    const headers = new Headers({
      ...type === "payload" ? {
        "content-type": "application/json",
        accept: "application/json"
      } : {},
      ...first.headers instanceof Headers ? Object.fromEntries(first.headers.entries()) : first.headers
    });
    if (first.method === "GET") {
      const encodedPayload = encode({
        payload: startSerializer.stringify({
          data: first.data,
          context: first.context
        })
      });
      if (encodedPayload) {
        if (url.includes("?")) {
          url += `&${encodedPayload}`;
        } else {
          url += `?${encodedPayload}`;
        }
      }
    }
    if (url.includes("?")) {
      url += `&createServerFn`;
    } else {
      url += `?createServerFn`;
    }
    if (first.response === "raw") {
      url += `&raw`;
    }
    const handlerResponse = await handler(url, {
      method: first.method,
      headers,
      signal: first.signal,
      ...getFetcherRequestOptions(first)
    });
    const response2 = await handleResponseErrors(handlerResponse);
    if ((_a = response2.headers.get("content-type")) == null ? void 0 : _a.includes("application/json")) {
      const json = startSerializer.decode(await response2.json());
      if (isRedirect(json) || isNotFound(json) || json instanceof Error) {
        throw json;
      }
      return json;
    }
    return response2;
  }
  const response = await handleResponseErrors(
    await handler(url, {
      method: "POST",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json"
      },
      body: JSON.stringify(args)
    })
  );
  const contentType = response.headers.get("content-type");
  if (contentType && contentType.includes("application/json")) {
    return startSerializer.decode(await response.json());
  } else {
    return response.text();
  }
}
function getFetcherRequestOptions(opts) {
  if (opts.method === "POST") {
    if (opts.data instanceof FormData) {
      opts.data.set("__TSR_CONTEXT", startSerializer.stringify(opts.context));
      return {
        body: opts.data
      };
    }
    return {
      body: startSerializer.stringify({
        data: opts.data ?? null,
        context: opts.context
      })
    };
  }
  return {};
}
async function handleResponseErrors(response) {
  if (!response.ok) {
    const contentType = response.headers.get("content-type");
    const isJson = contentType && contentType.includes("application/json");
    if (isJson) {
      throw startSerializer.decode(await response.json());
    }
    throw new Error(await response.text());
  }
  return response;
}

// node_modules/@tanstack/start-server-functions-client/dist/esm/index.js
function sanitizeBase(base) {
  return base.replace(/^\/|\/$/g, "");
}
var createClientRpc = (functionId, serverBase) => {
  const url = `/${sanitizeBase(serverBase)}/${functionId}`;
  const clientFn = (...args) => {
    return serverFnFetcher(url, args, fetch);
  };
  return Object.assign(clientFn, {
    url,
    functionId
  });
};
export {
  createClientRpc
};
//# sourceMappingURL=@tanstack_react-start_server-functions-client.js.map
